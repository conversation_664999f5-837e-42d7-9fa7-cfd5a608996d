/**
 * WebSocket Client with Session-Aware Communication
 * Integrates SessionManager for conversation continuity
 */

import * as vscode from 'vscode';
import WebSocket from 'ws';
import { SessionManager } from './sessionManager';

export interface CoderRequest {
    project_name: string;
    question: string;
    session_id?: string;
    user_id?: string;
    workspace_id?: string;
}

export interface WebSocketMessage {
    type: string;
    data: any;
}

export class SessionAwareWebSocketClient {
    private ws: WebSocket | null = null;
    private sessionManager: SessionManager;
    private outputChannel: vscode.OutputChannel;

    constructor(
        private context: vscode.ExtensionContext,
        private serverUrl: string
    ) {
        this.sessionManager = SessionManager.getInstance(context);
        this.outputChannel = vscode.window.createOutputChannel('ReCode AI WebSocket');
    }

    /**
     * Send coder request with automatic session management
     */
    public async sendCoderRequest(
        projectName: string, 
        question: string, 
        options: {
            userId?: string;
            workspaceId?: string;
            forceNewSession?: boolean;
        } = {}
    ): Promise<void> {
        try {
            // Handle new session creation if requested
            let sessionId: string;
            if (options.forceNewSession) {
                const session = await this.sessionManager.createNewSession(projectName);
                sessionId = session.sessionId;
            } else {
                sessionId = await this.sessionManager.getCurrentSessionId(projectName);
            }

            // Prepare request with session ID
            const request: CoderRequest = {
                project_name: projectName,
                question: question,
                session_id: sessionId,
                user_id: options.userId,
                workspace_id: options.workspaceId
            };

            // Log session usage
            this.logSessionUsage(sessionId, projectName, question);

            // Send WebSocket message
            await this.sendMessage({
                type: 'coder_request',
                data: request
            });

        } catch (error) {
            this.outputChannel.appendLine(`❌ Failed to send coder request: ${error}`);
            throw error;
        }
    }

    /**
     * Connect to WebSocket server
     */
    public async connect(clientId: string): Promise<void> {
        return new Promise((resolve, reject) => {
            try {
                const wsUrl = `${this.serverUrl}/ws/coder/${clientId}`;
                this.ws = new (WebSocket as any)(wsUrl);

                if (!this.ws) {
                    reject(new Error('Failed to create WebSocket connection'));
                    return;
                }

                this.ws.on('open', () => {
                    this.outputChannel.appendLine(`🔌 Connected to server: ${wsUrl}`);
                    resolve();
                });

                this.ws.on('message', (data: WebSocket.Data) => {
                    this.handleMessage(data.toString());
                });

                this.ws.on('error', (error) => {
                    this.outputChannel.appendLine(`❌ WebSocket error: ${error}`);
                    reject(error);
                });

                this.ws.on('close', () => {
                    this.outputChannel.appendLine('🔌 WebSocket connection closed');
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Send message to server
     */
    private async sendMessage(message: WebSocketMessage): Promise<void> {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket not connected');
        }

        this.ws.send(JSON.stringify(message));
    }

    /**
     * Handle incoming messages from server
     */
    private handleMessage(data: string): void {
        try {
            const message: WebSocketMessage = JSON.parse(data);
            
            switch (message.type) {
                case 'coder_response':
                    this.handleCoderResponse(message.data);
                    break;
                case 'queue_status':
                    this.handleQueueStatus(message.data);
                    break;
                case 'error':
                    this.handleError(message.data);
                    break;
                default:
                    this.outputChannel.appendLine(`📨 Unknown message type: ${message.type}`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`❌ Failed to parse message: ${error}`);
        }
    }

    /**
     * Handle coder response from server
     */
    private handleCoderResponse(data: any): void {
        this.outputChannel.appendLine(`🤖 Coder response: ${JSON.stringify(data, null, 2)}`);
        
        // Show response in VS Code
        if (data.status === 'success') {
            vscode.window.showInformationMessage('✅ Code generation completed!');
        } else if (data.status === 'error') {
            vscode.window.showErrorMessage(`❌ Error: ${data.message}`);
        }
    }

    /**
     * Handle queue status updates
     */
    private handleQueueStatus(data: any): void {
        if (data.status === 'queued') {
            vscode.window.showInformationMessage(
                `⏳ Request queued (position: ${data.position})`
            );
        }
    }

    /**
     * Handle error messages
     */
    private handleError(data: any): void {
        const errorMsg = data.message || 'Unknown error';
        this.outputChannel.appendLine(`❌ Server error: ${errorMsg}`);
        vscode.window.showErrorMessage(`Server Error: ${errorMsg}`);
    }

    /**
     * Log session usage for visibility
     */
    private logSessionUsage(sessionId: string, projectName: string, question: string): void {
        const shortId = sessionId.substring(0, 8);
        const shortQuestion = question.length > 50 ? question.substring(0, 50) + '...' : question;
        
        this.outputChannel.appendLine(
            `🔄 Using session ${shortId} for "${projectName}": ${shortQuestion}`
        );

        // Log session stats
        const stats = this.sessionManager.getSessionStats();
        if (stats) {
            this.outputChannel.appendLine(
                `📊 Session stats - ID: ${stats.sessionId}, Age: ${stats.age}, TTL: ${stats.ttl}`
            );
        }
    }

    /**
     * Create new session explicitly (for "New Session" command)
     */
    public async createNewSession(projectName?: string): Promise<string> {
        const session = await this.sessionManager.createNewSession(projectName);
        
        this.outputChannel.appendLine(
            `🆕 Manually created new session: ${session.sessionId.substring(0, 8)}`
        );
        
        return session.sessionId;
    }

    /**
     * Get current session info for UI display
     */
    public async getCurrentSessionInfo(): Promise<{
        sessionId: string;
        shortId: string;
        projectName?: string;
        age: string;
        ttl: string;
    } | null> {
        const session = await this.sessionManager.getCurrentSession();
        const stats = this.sessionManager.getSessionStats();
        
        if (!session || !stats) {
            return null;
        }

        return {
            sessionId: session.sessionId,
            shortId: session.sessionId.substring(0, 8),
            projectName: session.projectName,
            age: stats.age,
            ttl: stats.ttl
        };
    }

    /**
     * Disconnect WebSocket
     */
    public disconnect(): void {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}
