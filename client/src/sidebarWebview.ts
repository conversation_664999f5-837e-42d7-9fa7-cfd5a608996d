// Sidebar webview script for AI Coding Assistant extension
declare const acquireVsCodeApi: () => any;

interface SidebarVsCodeApi {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
}

class SidebarWebview {
  private vscode: SidebarVsCodeApi;
  private selectedIndexingType: string = "workspace";
  private isIndexed: boolean = false;
  private askModeEnabled: boolean = false;
  private welcomeMessageShown: boolean = false;
  private projectSummaryRequested: boolean = false;
  private isWaitingForResponse: boolean = false;
  private lastUserMessage: string | null = null;
  private errorShownForCurrentRequest: boolean = false;
  private toolStatus: { [tool: string]: string } = {};
  private lastMessageCount: number = 0;
  private scrollCheckInterval?: NodeJS.Timeout;
  private codeOperationInProgress: boolean = false;
  private forceScrollTimeout?: NodeJS.Timeout;
  private preserveScrollDuringOperation: boolean = false;
  private scrollLockActive: boolean = false;
  private lockedScrollPosition: number = 0;
  private chatMode: "agent" | "human" = "agent"; // Default to agent mode

  constructor() {
    this.vscode = acquireVsCodeApi();
    this.initializeEventListeners();
    this.loadSettings();
    this.loadWorkspaceName();
    this.startScrollMonitoring();
  }

  private initializeEventListeners() {
    // Settings icon toggle
    document.getElementById("settings-icon")?.addEventListener("click", () => {
      this.toggleSettings();
    });

    // Settings form handlers
    document.getElementById("save-settings")?.addEventListener("click", () => {
      this.saveSettings();
    });

    document
      .getElementById("test-connection")
      ?.addEventListener("click", () => {
        this.testConnection();
      });

    // GitHub option toggle
    document.getElementById("github-option")?.addEventListener("click", (e) => {
      e.preventDefault();
      this.toggleGitHubInput();
    });

    // Refresh workspace button
    document
      .getElementById("refresh-workspace")
      ?.addEventListener("click", () => {
        console.log("🔄 Manual workspace refresh requested");
        this.loadWorkspaceName();
      });

    // Start indexing button
    document.getElementById("start-indexing")?.addEventListener("click", () => {
      this.startIndexing();
    });

    // Chat toggle
    document.getElementById("chat-toggle")?.addEventListener("click", () => {
      this.toggleChatMode();
    });

    // Chat input auto-resize
    document.getElementById("chat-input")?.addEventListener("input", (e) => {
      this.adjustTextareaHeight(e.target as HTMLTextAreaElement);
    });

    // Send message button
    document.getElementById("send-message")?.addEventListener("click", () => {
      if (this.isWaitingForResponse) {
        this.stopGeneration();
      } else {
        this.sendMessage();
      }
    });

    // Rich editor enter key handling
    document.getElementById("rich-editor")?.addEventListener("keydown", (e) => {
      if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
        this.sendMessage();
      }
    });

    // Attachment button click
    document.getElementById("attachment-btn")?.addEventListener("click", () => {
      this.handleAttachment();
    });

    // File input change
    document.getElementById("file-input")?.addEventListener("change", (e) => {
      this.handleFileSelection(e);
    });

    // Click outside settings to close
    document.addEventListener("click", (e) => {
      const settingsPanel = document.getElementById("settings-panel");
      const settingsIcon = document.getElementById("settings-icon");
      if (
        settingsPanel &&
        !settingsPanel.contains(e.target as Node) &&
        !settingsIcon?.contains(e.target as Node)
      ) {
        settingsPanel.classList.add("hidden");
      }
    });

    // Listen for messages from extension
    window.addEventListener("message", (event) => {
      this.handleMessage(event.data);
    });

    // Add scroll listener to detect user scroll behavior
    setTimeout(() => {
      const chatMessages = document.getElementById("chat-messages");
      if (chatMessages) {
        chatMessages.addEventListener("scroll", () => {
          // This helps us track if user is manually scrolling
          // The isUserAtBottom() method will handle the logic
        });
      }
    }, 1000); // Delay to ensure DOM is ready

    // Chat mode dropdown
    const chatModeDropdown = document.getElementById(
      "chat-mode-dropdown"
    ) as HTMLSelectElement;
    if (chatModeDropdown) {
      this.chatMode = (chatModeDropdown.value as "agent" | "human") || "agent";
      chatModeDropdown.addEventListener("change", (e) => {
        this.chatMode =
          (chatModeDropdown.value as "agent" | "human") || "agent";
        this.updateChatModeUI();
        console.log(`🤖 Chat mode changed to: ${this.chatMode}`);

        // Save chat mode to extension state
        this.vscode.postMessage({
          command: "updateChatMode",
          chatMode: this.chatMode,
        });
      });
      this.updateChatModeUI();
    }

    // New Conversation button
    const newConversationBtn = document.getElementById("new-conversation-btn");
    if (newConversationBtn) {
      newConversationBtn.addEventListener("click", () => {
        this.startNewConversation();
      });
    }
  }

  private toggleSettings() {
    const settingsPanel = document.getElementById("settings-panel");
    if (settingsPanel) {
      settingsPanel.classList.toggle("hidden");
    }
  }

  private loadSettings() {
    this.vscode.postMessage({ command: "getSettings" });
  }

  private loadWorkspaceName() {
    console.log("🔍 Requesting workspace name from extension...");
    // Request workspace name from extension
    this.vscode.postMessage({ command: "getWorkspaceName" });

    // Add a timeout fallback in case the extension doesn't respond
    setTimeout(() => {
      const workspaceNameElement = document.getElementById("workspace-name");
      if (
        workspaceNameElement &&
        workspaceNameElement.textContent === "Loading workspace..."
      ) {
        console.log("⚠️ Workspace name request timed out, using fallback");
        this.updateWorkspaceName("Workspace");
      }
    }, 3000); // 3 second timeout
  }

  private toggleGitHubInput() {
    const githubContainer = document.getElementById("github-input-container");
    const workspaceDisplay = document.getElementById("workspace-display");

    if (githubContainer?.classList.contains("hidden")) {
      // Show GitHub input
      githubContainer.classList.remove("hidden");
      workspaceDisplay?.classList.add("hidden");
      this.selectedIndexingType = "github";
    } else {
      // Show workspace display
      githubContainer?.classList.add("hidden");
      workspaceDisplay?.classList.remove("hidden");
      this.selectedIndexingType = "workspace";
    }
  }

  private toggleChatMode() {
    this.askModeEnabled = !this.askModeEnabled;
    const chatToggle = document.getElementById("chat-toggle");

    if (this.askModeEnabled) {
      chatToggle!.innerHTML = "💬 Chat Active";
      chatToggle?.style.setProperty(
        "background",
        "linear-gradient(135deg, #4CAF50 0%, #45a049 100%)"
      );
    } else {
      chatToggle!.innerHTML = "💬 Chat";
      chatToggle?.style.setProperty(
        "background",
        "linear-gradient(135deg, var(--vscode-button-background) 0%, var(--vscode-textLink-foreground) 100%)"
      );
    }
  }

  private isAgentMode(): boolean {
    return this.chatMode === "agent";
  }

  private updateChatModeUI() {
    // Update any UI elements that need to reflect the current chat mode
    console.log(`🎯 Chat mode UI updated to: ${this.chatMode}`);
  }

  private startNewConversation() {
    console.log("🆕 Starting new conversation...");

    // Clear current chat UI
    this.clearChatUI();

    // Send message to extension to create new session
    this.vscode.postMessage({
      command: "newConversation",
    });

    // Show confirmation message
    this.addConsoleOutput("🆕 New conversation started", "info");
  }

  private updateAgentModeStatusToSuccess(messageDiv: HTMLElement) {
    // Update all status indicators in the message to show success for agent mode
    const statusElements = messageDiv.querySelectorAll(".action-status-icon");
    statusElements.forEach((element) => {
      element.className = "action-status-icon success";
      element.innerHTML = "✅";
    });

    // Remove any spinners
    const spinners = messageDiv.querySelectorAll(".action-status-spinner");
    spinners.forEach((spinner) => spinner.remove());
  }

  private saveSettings() {
    const provider = (
      document.getElementById("llm-provider") as HTMLSelectElement
    ).value;
    const model = (document.getElementById("llm-model") as HTMLSelectElement)
      .value;
    const apiKey = (document.getElementById("api-key") as HTMLInputElement)
      .value;

    if (!apiKey.trim()) {
      this.vscode.postMessage({
        command: "error",
        text: "Please enter an API key",
      });
      return;
    }

    this.vscode.postMessage({
      command: "saveSettings",
      settings: { provider, model, apiKey },
    });

    // Hide settings panel after saving
    document.getElementById("settings-panel")?.classList.add("hidden");
  }

  private testConnection() {
    this.vscode.postMessage({
      command: "testConnection",
    });
  }

  private startIndexing() {
    if (!this.selectedIndexingType) return;

    const progressContainer = document.getElementById("progress-container");
    progressContainer?.classList.remove("hidden");

    let indexingData: any = { type: this.selectedIndexingType };

    if (this.selectedIndexingType === "github") {
      const githubUrl = (
        document.getElementById("github-url") as HTMLInputElement
      ).value;
      if (!githubUrl.trim()) {
        this.vscode.postMessage({
          command: "error",
          text: "Please enter a GitHub repository URL",
        });
        return;
      }
      indexingData.url = githubUrl;
    }

    // Hide other elements during indexing
    const workspaceDisplay = document.getElementById("workspace-display");
    const githubOption = document.querySelector(".github-option");
    const githubInputContainer = document.getElementById(
      "github-input-container"
    );
    const startButton = document.getElementById("start-indexing");

    workspaceDisplay?.classList.add("hidden");
    githubOption?.classList.add("hidden");
    githubInputContainer?.classList.add("hidden");
    startButton?.classList.add("hidden");

    this.vscode.postMessage({
      command: "startIndexing",
      data: indexingData,
    });

    // Simulate progress steps
    this.simulateIndexingProgress();
  }

  private simulateIndexingProgress() {
    const steps = [
      "step-connecting",
      "step-analyzing",
      "step-embeddings",
      "step-storing",
    ];
    let currentStep = 0;

    const progressStep = () => {
      if (currentStep > 0) {
        document
          .getElementById(steps[currentStep - 1])
          ?.classList.remove("active");
        document
          .getElementById(steps[currentStep - 1])
          ?.classList.add("completed");
      }

      if (currentStep < steps.length) {
        document.getElementById(steps[currentStep])?.classList.add("active");
        currentStep++;
        setTimeout(progressStep, 2000);
      } else {
        // Indexing complete
        setTimeout(() => {
          this.isIndexed = true;
          const progressContainer =
            document.getElementById("progress-container");
          progressContainer?.classList.add("hidden");
          this.showIndexingComplete();
        }, 1000);
      }
    };

    progressStep();
  }

  private showIndexingComplete() {
    console.log("🎉 showIndexingComplete called");
    console.log(
      "🔍 projectSummaryRequested flag:",
      this.projectSummaryRequested
    );

    // Show chat interface
    this.showChatInterface();

    // Add a welcome message at the top of the chat
    this.addWelcomeMessage();

    // Request project summary from backend (only once)
    if (!this.projectSummaryRequested) {
      this.projectSummaryRequested = true;
      console.log("📤 Sending generateProjectSummary command to extension");
      this.vscode.postMessage({
        command: "generateProjectSummary",
      });
    } else {
      console.log("⚠️ Project summary already requested, skipping...");
    }

    // Show a success message briefly
    this.vscode.postMessage({
      command: "success", // Use success command instead of error
      text: "✅ Indexing Complete! AI Assistant is now ready to take any questions or coding requests.",
    });
  }

  private addWelcomeMessage() {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Prevent duplicate welcome messages
    if (this.welcomeMessageShown) return;
    this.welcomeMessageShown = true;

    const welcomeMessage = document.createElement("div");
    welcomeMessage.className = "message assistant-message welcome-message";
    welcomeMessage.innerHTML = `
      <div class="message-content">
        <div class="welcome-header">
          🎉 <strong>Indexing Complete!</strong>
        </div>
        <div class="welcome-text">
          AI Assistant is now ready to take any questions or coding requests.
          I have analyzed your codebase and can help you with:
          <ul>
            <li>📝 Code explanations and documentation</li>
            <li>🔧 Refactoring and improvements</li>
            <li>🐛 Bug fixes and debugging</li>
            <li>✨ New feature development</li>
          </ul>
          What would you like to work on today?
        </div>
      </div>
    `;

    // Add some styling
    welcomeMessage.style.cssText = `
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      margin-bottom: 16px;
      color: white;
    `;

    chatMessages.appendChild(welcomeMessage);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  private handleIndexingError(error: string) {
    // Hide progress container
    const progressContainer = document.getElementById("progress-container");
    progressContainer?.classList.add("hidden");

    // Show error message using professional styling
    this.addErrorMessage(`Indexing failed: ${error}`);

    // Reset indexing state
    this.isIndexed = false;
  }

  // Modify sendMessage to store last user message
  private sendMessage = (messageOverride?: string) => {
    const chatInput = document.getElementById(
      "chat-input"
    ) as HTMLTextAreaElement;
    const message =
      messageOverride !== undefined ? messageOverride : chatInput.value.trim();

    if (!message) {
      return;
    }

    this.lastUserMessage = message; // Store for retry

    if (!this.isIndexed) {
      this.addErrorMessage(
        "Please index your codebase first before sending messages."
      );
      return;
    }

    // Add user message to chat
    this.addMessageToChat("user", message);

    // Clear the input immediately
    if (!messageOverride) {
      chatInput.value = "";
      this.adjustTextareaHeight(chatInput);
    }

    // Show thinking state
    this.addThinkingMessage();

    // Update UI to show waiting state
    this.setWaitingState(true);

    // Send message to extension
    this.vscode.postMessage({
      command: "sendMessage",
      message: message,
      askMode: this.askModeEnabled,
    });
  };

  private addMessageToChat(
    sender: string,
    content: string,
    persist: boolean = true,
    skipActionIndicators: boolean = false
  ) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    const messageDiv = document.createElement("div");
    messageDiv.className = `message ${sender}`;

    const isUser = sender === "user";

    // Persist message to extension state if requested
    if (persist) {
      this.vscode.postMessage({
        command: "addChatMessage",
        message: {
          content: content,
          isUser: isUser,
          timestamp: Date.now(),
          messageType: "regular", // Regular chat message
          originalFormatting: false, // Uses standard formatting
        },
      });
    }

    if (isUser) {
      // User messages keep their header and styling
      messageDiv.innerHTML = `
        <div class="message-header">
          <div class="icon">👤</div>
          <span>You</span>
        </div>
        <div class="message-content">${content}</div>
      `;
    } else {
      // Assistant messages: check for action indicators first (unless skipped)
      if (!skipActionIndicators) {
        this.parseAndShowActionIndicators(content);
      }

      // Then render markdown and no header
      const renderedContent = this.renderMarkdown(content);
      messageDiv.innerHTML = `
        <div class="message-content markdown-content">${renderedContent}</div>
      `;
    }

    chatMessages.appendChild(messageDiv);

    // Force immediate scroll
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // For assistant messages, be more aggressive about scrolling to prevent UI freeze
    const forceScroll = !isUser;

    // Then use the comprehensive scroll function
    this.scrollToBottom(forceScroll);
  }

  private renderMarkdown(text: string): string {
    // Simple markdown renderer for common elements
    let html = text;

    // Headers
    html = html.replace(/^### (.*$)/gim, "<h3>$1</h3>");
    html = html.replace(/^## (.*$)/gim, "<h2>$1</h2>");
    html = html.replace(/^# (.*$)/gim, "<h1>$1</h1>");

    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
    html = html.replace(/__(.*?)__/g, "<strong>$1</strong>");

    // Italic
    html = html.replace(/\*(.*?)\*/g, "<em>$1</em>");
    html = html.replace(/_(.*?)_/g, "<em>$1</em>");

    // Code blocks
    html = html.replace(/```([\s\S]*?)```/g, "<pre><code>$1</code></pre>");

    // Inline code
    html = html.replace(/`([^`]+)`/g, "<code>$1</code>");

    // Lists
    html = html.replace(/^\* (.*$)/gim, "<li>$1</li>");
    html = html.replace(/^- (.*$)/gim, "<li>$1</li>");
    html = html.replace(/^\+ (.*$)/gim, "<li>$1</li>");

    // Wrap consecutive list items in ul
    html = html.replace(/(<li>.*<\/li>)/gs, "<ul>$1</ul>");

    // Fix nested ul tags
    html = html.replace(/<\/ul>\s*<ul>/g, "");

    // Line breaks
    html = html.replace(/\n/g, "<br>");

    return html;
  }

  private addThinkingMessage() {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    const messageDiv = document.createElement("div");
    messageDiv.className = "message thinking";
    messageDiv.id = "thinking-message";

    messageDiv.innerHTML = `
      <div class="message-content">
        <div class="thinking-content">
          <span>Thinking</span>
          <span class="animated-dots">
            <span class="dot">.</span>
            <span class="dot">.</span>
            <span class="dot">.</span>
          </span>
        </div>
      </div>
    `;

    chatMessages.appendChild(messageDiv);

    // CRITICAL FIX: Persist thinking message to state so it survives tab switches
    this.vscode.postMessage({
      command: "addChatMessage",
      message: {
        content: "Thinking...",
        isUser: false,
        timestamp: Date.now(),
        messageType: "thinking", // Save the message type for proper restoration
        originalFormatting: true,
      },
    });

    // Force immediate scroll
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Then use the comprehensive scroll function
    this.scrollToBottom();
  }

  private removeThinkingMessage() {
    const thinkingMessage = document.getElementById("thinking-message");
    if (thinkingMessage) {
      thinkingMessage.remove();
      // Ensure scroll after removing thinking message
      this.scrollToBottom();
    }

    // CRITICAL FIX: Remove thinking message from persisted state
    this.vscode.postMessage({
      command: "removeThinkingMessage",
    });
  }

  private isCodeApplicationInProgress(): boolean {
    // Check if we're in code application mode by looking for approval status elements
    const approvalElements = document.querySelectorAll(
      ".approval-status.approved"
    );
    const hasApprovalElements = approvalElements.length > 0;

    // Also check if we recently completed code application (within last 30 seconds)
    // This helps with scrolling issues after code application
    const recentCodeApplication = this.isRecentCodeApplicationFromState();

    console.log("🔄 isCodeApplicationInProgress:", {
      hasApprovalElements,
      recentCodeApplication,
      result: hasApprovalElements || recentCodeApplication,
    });

    return hasApprovalElements || recentCodeApplication;
  }

  private isRecentCodeApplicationFromState(): boolean {
    // This is a simplified check - in a real implementation, we'd get this from the state manager
    // For now, we'll use a more aggressive scrolling approach
    return false; // We'll handle this differently
  }

  private isUserAtBottom(): boolean {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return true;

    const threshold = 50; // pixels from bottom
    const isAtBottom =
      chatMessages.scrollTop + chatMessages.clientHeight >=
      chatMessages.scrollHeight - threshold;
    return isAtBottom;
  }

  private isUserNearBottom(): boolean {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return true;

    // More generous threshold - if user is within 200 pixels of bottom, consider them "near"
    const threshold = 200;
    const isNearBottom =
      chatMessages.scrollTop + chatMessages.clientHeight >=
      chatMessages.scrollHeight - threshold;

    console.log("🔄 isUserNearBottom check:", {
      scrollTop: chatMessages.scrollTop,
      clientHeight: chatMessages.clientHeight,
      scrollHeight: chatMessages.scrollHeight,
      threshold,
      distanceFromBottom:
        chatMessages.scrollHeight -
        (chatMessages.scrollTop + chatMessages.clientHeight),
      isNearBottom,
    });

    return isNearBottom;
  }

  private startScrollMonitoring() {
    // Monitor for new messages and ensure scrolling works
    let lastScrollTop = 0;
    let lastScrollHeight = 0;

    this.scrollCheckInterval = setInterval(() => {
      const chatMessages = document.getElementById("chat-messages");
      if (!chatMessages) return;

      const currentMessageCount = chatMessages.querySelectorAll(
        ".message:not(.thinking)"
      ).length;
      const currentScrollHeight = chatMessages.scrollHeight;

      // Enforce scroll lock if active
      this.enforceScrollLock();

      // Check if content became scrollable (important for short chats)
      if (currentScrollHeight > lastScrollHeight && lastScrollHeight > 0) {
        const wasNonScrollable = lastScrollHeight <= chatMessages.clientHeight;
        const isNowScrollable = currentScrollHeight > chatMessages.clientHeight;

        if (wasNonScrollable && isNowScrollable) {
          console.log(
            `🔄 Content became scrollable: ${lastScrollHeight} -> ${currentScrollHeight}, scrolling to bottom`
          );
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }
      }
      lastScrollHeight = currentScrollHeight;

      // Track unexpected scroll position resets
      const currentScrollTop = chatMessages.scrollTop;
      if (
        lastScrollTop > 1000 &&
        currentScrollTop === 0 &&
        !this.scrollLockActive
      ) {
        console.error("🚨 SCROLL RESET DETECTED!", {
          lastScrollTop,
          currentScrollTop,
          scrollHeight: chatMessages.scrollHeight,
          clientHeight: chatMessages.clientHeight,
        });
        console.trace("🚨 Stack trace for scroll reset");

        // Force scroll back to bottom if this was an unexpected reset
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
      lastScrollTop = currentScrollTop;

      // If new messages were added, ensure we scroll if user is near bottom
      if (currentMessageCount > this.lastMessageCount) {
        console.log(
          `🔄 New messages detected: ${this.lastMessageCount} -> ${currentMessageCount}`
        );
        this.lastMessageCount = currentMessageCount;

        // If user is reasonably close to bottom, scroll to show new messages
        if (this.isUserNearBottom()) {
          console.log(
            "🔄 Auto-scrolling due to new messages and user near bottom"
          );
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }
      }
    }, 500); // Check every 500ms for more responsive detection
  }

  private startPostOperationScrollProtection() {
    // After code operations, aggressively maintain scroll position for 10 seconds

    // Clear any existing timeout
    if (this.forceScrollTimeout) {
      clearTimeout(this.forceScrollTimeout);
    }

    // Force scroll to bottom immediately
    const chatMessages = document.getElementById("chat-messages");
    if (chatMessages) {
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Set up aggressive scroll monitoring for the next 15 seconds
    let protectionCount = 0;
    const maxProtectionChecks = 60; // 15 seconds at 250ms intervals
    let lastScrollHeight = chatMessages ? chatMessages.scrollHeight : 0;

    const protectionInterval = setInterval(() => {
      const chatMessages = document.getElementById("chat-messages");
      if (!chatMessages) {
        clearInterval(protectionInterval);
        return;
      }

      protectionCount++;
      const currentScrollHeight = chatMessages.scrollHeight;

      // Check if content became scrollable (height increased)
      if (currentScrollHeight > lastScrollHeight) {
        console.log(
          `🔄 Content height increased: ${lastScrollHeight} -> ${currentScrollHeight}, forcing scroll to bottom`
        );
        chatMessages.scrollTop = chatMessages.scrollHeight;
        lastScrollHeight = currentScrollHeight;
      }

      // If scroll position is not at bottom, force it there
      const isAtBottom =
        chatMessages.scrollTop + chatMessages.clientHeight >=
        chatMessages.scrollHeight - 50;
      if (!isAtBottom) {
        console.log(
          `🔄 Post-operation protection: forcing scroll to bottom (check ${protectionCount}/${maxProtectionChecks})`
        );
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }

      // Stop protection after 15 seconds
      if (protectionCount >= maxProtectionChecks) {
        clearInterval(protectionInterval);
      }
    }, 250); // More frequent checks for better responsiveness
  }

  private activateScrollLock() {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    this.scrollLockActive = true;

    // Check if content is actually scrollable
    const isScrollable = chatMessages.scrollHeight > chatMessages.clientHeight;
    const isNearBottom = this.isUserNearBottom();

    if (!isScrollable) {
      // Content fits in viewport - use special "bottom tracking" mode
      this.lockedScrollPosition = -1; // Special value meaning "track bottom"
      console.log(
        "🔒 Scroll lock activated in BOTTOM-TRACKING mode (content not scrollable yet)"
      );
    } else if (isNearBottom) {
      this.lockedScrollPosition = chatMessages.scrollHeight;
      console.log(
        "🔒 Scroll lock activated at BOTTOM position (user was near bottom)"
      );
    } else {
      this.lockedScrollPosition = chatMessages.scrollTop;
      console.log(
        `🔒 Scroll lock activated at current position ${this.lockedScrollPosition}`
      );
    }

    // Auto-deactivate after 10 seconds as a safety measure
    setTimeout(() => {
      console.log(
        "🔓 Safety timeout - deactivating scroll lock after 10 seconds"
      );
      this.deactivateScrollLock();
    }, 10000);
  }

  private deactivateScrollLock() {
    this.scrollLockActive = false;
  }

  private enforceScrollLock() {
    if (!this.scrollLockActive) return;

    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Handle special "bottom tracking" mode for non-scrollable content
    if (this.lockedScrollPosition === -1) {
      // Always keep at bottom when content becomes scrollable
      const isScrollable =
        chatMessages.scrollHeight > chatMessages.clientHeight;

      console.log(
        `🔒 Bottom-tracking check: scrollable=${isScrollable}, scrollHeight=${chatMessages.scrollHeight}, clientHeight=${chatMessages.clientHeight}`
      );

      if (isScrollable) {
        const targetPosition = chatMessages.scrollHeight;
        if (chatMessages.scrollTop !== targetPosition) {
          console.log(
            `🔒 Bottom-tracking mode: scrolling to bottom ${chatMessages.scrollTop} -> ${targetPosition}`
          );
          chatMessages.scrollTop = targetPosition;
        }
      } else {
        // Content still not scrollable, but ensure we're at position 0
        if (chatMessages.scrollTop !== 0) {
          console.log(
            `🔒 Bottom-tracking mode: content not scrollable, ensuring scroll at 0 (was ${chatMessages.scrollTop})`
          );
          chatMessages.scrollTop = 0;
        }
      }
      return;
    }

    // Normal scroll lock enforcement
    if (chatMessages.scrollTop !== this.lockedScrollPosition) {
      console.log(
        `🔒 Enforcing scroll lock: ${chatMessages.scrollTop} -> ${this.lockedScrollPosition}`
      );
      chatMessages.scrollTop = this.lockedScrollPosition;
    }
  }

  private scrollToBottom(force: boolean = false) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Force scroll during code application or if explicitly forced
    const shouldForceScroll = force || this.isCodeApplicationInProgress();

    // Be more aggressive about scrolling - if user is reasonably close to bottom, scroll
    const isNearBottom = this.isUserNearBottom();

    // Only auto-scroll if user is near bottom, or if forced/during code application
    if (!shouldForceScroll && !isNearBottom) {
      console.log(
        "🚫 User scrolled up, skipping auto-scroll until they scroll back down"
      );
      return;
    }

    if (shouldForceScroll) {
      console.log("🔄 Force scrolling during code application");
    } else {
      console.log("🔄 Auto-scrolling because user is near bottom");
    }

    // Force immediate scroll first
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Use multiple requestAnimationFrame calls to ensure DOM is fully updated
    requestAnimationFrame(() => {
      chatMessages.scrollTop = chatMessages.scrollHeight;

      requestAnimationFrame(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Use auto scroll instead of smooth to prevent issues
        chatMessages.scrollTo({
          top: chatMessages.scrollHeight,
          behavior: "auto",
        });

        // Multiple delayed scrolls to handle any layout changes
        setTimeout(() => {
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 50);

        setTimeout(() => {
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 150);

        setTimeout(() => {
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 300);
      });
    });
  }

  // Action-based UI methods
  public showActionIndicator(
    actionType: string,
    fileName: string,
    filePath: string,
    status: "in-progress" | "success" | "error" = "in-progress"
  ): string {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return "";

    const actionId = `action-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    const actionDiv = document.createElement("div");
    actionDiv.className = "action-status";
    actionDiv.id = actionId;

    // Custom heading for read_code_file_struct_tool
    let heading = "";
    if (actionType === "read_code_file_struct_tool") {
      heading = "Understanding file structure";
    } else if (actionType === "file_read_tool") {
      heading = `Reading code from ${fileName}`;
    } else if (actionType === "semantic_search_tool") {
      heading = "Searching codebase";
    } else {
      heading =
        this.getActionIcon(actionType) +
        " " +
        (this.getActionVerb ? this.getActionVerb(actionType) : actionType);
    }

    const icon = this.getActionIcon(actionType);
    const statusIcon =
      status === "in-progress"
        ? '<div class="loading-dots"><span></span><span></span><span></span></div>'
        : this.getStatusIcon(status);

    actionDiv.innerHTML = `
      <div class="action-heading-block">
        <span class="action-heading-text">${icon} ${heading} <span class="tool-status-indicator">${statusIcon}</span></span>
      </div>
    `;

    chatMessages.appendChild(actionDiv);
    this.scrollToBottom();
    return actionId;
  }

  public updateActionStatus(actionId: string, status: "success" | "error") {
    const actionElement = document.getElementById(actionId);
    if (!actionElement) return;

    const statusIconElement = actionElement.querySelector(
      ".action-status-icon"
    );
    const spinnerElement = actionElement.querySelector(
      ".action-status-spinner"
    );

    if (statusIconElement) {
      statusIconElement.className = `action-status-icon ${status}`;
      statusIconElement.innerHTML = this.getStatusIcon(status);
    }

    // Remove spinner when status is updated
    if (spinnerElement) {
      spinnerElement.remove();
    }
  }

  private getActionIcon(actionType: string): string {
    const icons: { [key: string]: string } = {
      Analyzing: "🔍",
      "Reading file": "📖",
      "Understanding structure": "🏗️",
      "Editing file": "✏️",
      "Creating file": "📝",
      "Deleting file": "🗑️",
      "Searching directory": "📁",
      Compiling: "⚙️",
      "Running tests": "🧪",
      "Installing dependencies": "📦",
      "Building project": "🔨",
    };
    return icons[actionType] || "⚡";
  }

  private getStatusIcon(status: string): string {
    const icons: { [key: string]: string } = {
      "in-progress": "⏳",
      success: "✅",
      error: "❌",
    };
    return icons[status] || "⏳";
  }

  private getStatusMessage(status: string, actionType: string): string {
    const messages: { [key: string]: { [key: string]: string } } = {
      success: {
        Analyzing: "Analysis completed successfully! 🎉",
        "Reading file": "File read successfully! 📖✨",
        "Understanding structure": "Structure analysis complete! 🏗️✨",
        "Editing file": "File edited successfully! ✏️✨",
        "Creating file": "File created successfully! 📝✨",
        "Deleting file": "File deleted successfully! 🗑️✨",
        "Searching directory": "Directory search completed! 📁✨",
        Compiling: "Compilation successful! ⚙️✨",
        "Running tests": "Tests passed! 🧪✨",
        "Installing dependencies": "Dependencies installed! 📦✨",
        "Building project": "Build successful! 🔨✨",
        terminal_command: "Command executed successfully! 💻✨",
        default: "Operation completed successfully! ✨",
      },
      error: {
        Analyzing: "Analysis failed 😞",
        "Reading file": "Failed to read file 📖❌",
        "Understanding structure": "Structure analysis failed 🏗️❌",
        "Editing file": "Failed to edit file ✏️❌",
        "Creating file": "Failed to create file 📝❌",
        "Deleting file": "Failed to delete file 🗑️❌",
        "Searching directory": "Directory search failed 📁❌",
        Compiling: "Compilation failed ⚙️❌",
        "Running tests": "Tests failed 🧪❌",
        "Installing dependencies": "Failed to install dependencies 📦❌",
        "Building project": "Build failed 🔨❌",
        terminal_command: "Command failed 💻❌",
        default: "Operation failed ❌",
      },
    };

    return (
      messages[status]?.[actionType] || messages[status]?.["default"] || ""
    );
  }

  public updateTerminalCommandStatus(
    buttonElement: HTMLElement,
    status: "executing" | "success" | "error"
  ) {
    const actionStatusDiv = buttonElement.closest(".action-status");
    if (!actionStatusDiv) return;

    // Update the button appearance
    switch (status) {
      case "executing":
        buttonElement.innerHTML = "⏳";
        buttonElement.setAttribute("disabled", "true");
        buttonElement.style.opacity = "0.8";
        buttonElement.style.background = "rgba(255,165,0,0.2)";
        buttonElement.style.border = "1px solid rgba(255,165,0,0.3)";
        break;
      case "success":
        buttonElement.innerHTML = "✅";
        buttonElement.style.background = "rgba(40,167,69,0.2)";
        buttonElement.style.color = "#28a745";
        buttonElement.style.border = "1px solid rgba(40,167,69,0.3)";
        buttonElement.style.opacity = "1";
        buttonElement.removeAttribute("disabled");

        // Add success message
        this.addStatusMessageToContainer(
          actionStatusDiv,
          "Command executed successfully! 💻✨",
          "success"
        );
        break;
      case "error":
        buttonElement.innerHTML = "❌";
        buttonElement.style.background = "rgba(220,53,69,0.2)";
        buttonElement.style.color = "#dc3545";
        buttonElement.style.border = "1px solid rgba(220,53,69,0.3)";
        buttonElement.style.opacity = "1";
        buttonElement.removeAttribute("disabled");

        // Add error message
        this.addStatusMessageToContainer(
          actionStatusDiv,
          "Command failed 💻❌",
          "error"
        );
        break;
    }
  }

  public updateFileOperationButtonStatus(
    buttonElement: HTMLElement,
    status: "executing" | "success" | "error"
  ) {
    const actionStatusDiv = buttonElement.closest(".action-status");
    if (!actionStatusDiv) return;

    // Update the button appearance
    switch (status) {
      case "executing":
        buttonElement.innerHTML = "⏳";
        buttonElement.setAttribute("disabled", "true");
        buttonElement.style.opacity = "0.8";
        buttonElement.style.background = "rgba(255,165,0,0.2)";
        buttonElement.style.border = "1px solid rgba(255,165,0,0.3)";
        break;
      case "success":
        buttonElement.innerHTML = "✅";
        buttonElement.style.background = "rgba(40,167,69,0.2)";
        buttonElement.style.color = "#28a745";
        buttonElement.style.border = "1px solid rgba(40,167,69,0.3)";
        buttonElement.style.opacity = "1";
        buttonElement.removeAttribute("disabled");

        // Add success message
        this.addStatusMessageToContainer(
          actionStatusDiv,
          "File operation completed successfully! ✨",
          "success"
        );
        break;
      case "error":
        buttonElement.innerHTML = "❌";
        buttonElement.style.background = "rgba(220,53,69,0.2)";
        buttonElement.style.color = "#dc3545";
        buttonElement.style.border = "1px solid rgba(220,53,69,0.3)";
        buttonElement.style.opacity = "1";
        buttonElement.removeAttribute("disabled");

        // Add error message
        this.addStatusMessageToContainer(
          actionStatusDiv,
          "File operation failed ❌",
          "error"
        );
        break;
    }
  }

  private addStatusMessageToContainer(
    container: Element,
    message: string,
    type: "success" | "error"
  ) {
    // Remove any existing status message
    const existingMessage = container.querySelector(".status-message");
    if (existingMessage) {
      existingMessage.remove();
    }

    // Configure styles and icons based on type
    const config = {
      success: {
        background: "rgba(40,167,69,0.1)",
        border: "1px solid rgba(40,167,69,0.3)",
        color: "#28a745",
        icon: "🎉",
        timeout: 5000,
      },
      error: {
        background: "rgba(220,53,69,0.1)",
        border: "1px solid rgba(220,53,69,0.3)",
        color: "#dc3545",
        icon: "😞",
        timeout: 8000,
      },
    };

    const style = config[type];

    // Add new status message
    const messageDiv = document.createElement("div");
    messageDiv.className = `status-message ${type}-message`;
    messageDiv.innerHTML = `
      <div style="
        margin-top: 8px;
        padding: 6px 12px;
        background: ${style.background};
        border: ${style.border};
        border-radius: 4px;
        color: ${style.color};
        font-size: 11px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;
      ">
        <span style="font-size: 12px;">${style.icon}</span>
        ${message}
      </div>
    `;
    container.appendChild(messageDiv);

    // Auto-remove after specified timeout
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.remove();
      }
    }, style.timeout);
  }

  public updateFileOperationButtonsByPath(
    filePath: string,
    status: "success" | "error"
  ) {
    // Find all file operation buttons for this file path
    const buttons = document.querySelectorAll(
      `[data-file-path="${filePath}"] .action-play-button`
    );
    buttons.forEach((button) => {
      this.updateFileOperationButtonStatus(button as HTMLElement, status);
    });
  }

  public updateTerminalCommandButtonsByCommand(
    command: string,
    status: "success" | "error"
  ) {
    // Find all terminal command buttons for this command
    const buttons = document.querySelectorAll(`[data-command="${command}"]`);
    buttons.forEach((button) => {
      this.updateTerminalCommandStatus(button as HTMLElement, status);
    });
  }

  private handleTerminalCommandCompletion(toolData: any) {
    const status = toolData.status === "success" ? "success" : "error";
    this.updateAllExecutingButtons(status);
  }

  public updateAllExecutingButtons(status: "success" | "error") {
    // Find all buttons and check their content/state
    const allButtons = document.querySelectorAll("button");
    let updatedCount = 0;

    allButtons.forEach((button) => {
      const buttonElement = button as HTMLElement;

      // Check if button is in executing state (disabled or contains hourglass)
      const isDisabled = buttonElement.hasAttribute("disabled");
      const hasHourglass = buttonElement.innerHTML.includes("⏳");

      if (isDisabled || hasHourglass) {
        if (buttonElement.classList.contains("terminal-command-btn")) {
          this.updateTerminalCommandStatus(buttonElement, status);
          updatedCount++;
        } else if (buttonElement.classList.contains("action-play-button")) {
          this.updateFileOperationButtonStatus(buttonElement, status);
          updatedCount++;
        }
      }
    });

    if (updatedCount > 0) {
      console.log(`✅ Updated ${updatedCount} buttons to ${status} status`);

      // If all operations completed successfully, allow natural scrolling after a brief delay
      if (status === "success") {
        setTimeout(() => {
          console.log(
            "🔓 All operations successful - allowing natural user scrolling"
          );
          this.deactivateScrollLock();
        }, 2000);
      }
    }
  }

  private parseAndShowActionIndicators(content: string) {
    // Parse content for action patterns and show action indicators
    const actionMap: { [key: string]: string } = {
      analyze: "Analyzing",
      search: "Searching directory",
      read: "Reading file",
      edit: "Editing file",
      create: "Creating file",
      delete: "Deleting file",
      compile: "Compiling",
      build: "Building project",
      install: "Installing dependencies",
      "run tests": "Running tests",
    };

    const actionPatterns = [
      /Let me (analyze|search|read|edit|create|delete|compile|build|install|run tests on)\s+(?:the\s+)?(?:file\s+)?(?:directory\s+)?(?:project\s+)?(?:in\s+)?(?:for\s+)?(?:from\s+)?(?:to\s+)?(?:`([^`]+)`|(\S+\.\w+)|([^.\s]+))/gi,
      /I'll (analyze|search|read|edit|create|delete|compile|build|install|run tests on)\s+(?:the\s+)?(?:file\s+)?(?:directory\s+)?(?:project\s+)?(?:in\s+)?(?:for\s+)?(?:from\s+)?(?:to\s+)?(?:`([^`]+)`|(\S+\.\w+)|([^.\s]+))/gi,
    ];

    actionPatterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const action = match[1].toLowerCase();
        const filePath = match[2] || match[3] || match[4] || "unknown";

        if (actionMap[action] && filePath !== "unknown") {
          const fileName = filePath.split("/").pop() || filePath;
          this.showActionIndicator(
            actionMap[action],
            fileName,
            filePath,
            "in-progress"
          );
        }
      }
    });
  }

  private getTerminalOperationTitle(op: any): string {
    // Determine operation title based on action type and commands
    if (op.action === "create_directory_terminal") {
      return "Directory Creation";
    }

    if (!op.commands || op.commands.length === 0) {
      return "Terminal Commands";
    }

    // Analyze commands to determine operation type
    const hasCreate = op.commands.some(
      (cmd: string) =>
        cmd.includes("mkdir") || cmd.includes("touch") || cmd.includes("echo")
    );
    const hasDelete = op.commands.some(
      (cmd: string) => cmd.includes("rm ") || cmd.includes("del ")
    );
    const hasMove = op.commands.some(
      (cmd: string) => cmd.includes("mv ") || cmd.includes("move ")
    );
    const hasCopy = op.commands.some(
      (cmd: string) => cmd.includes("cp ") || cmd.includes("copy ")
    );

    // Return appropriate title based on command types
    if (hasCreate && hasDelete) return "File System Operations";
    else if (hasCreate && hasMove) return "Create & Move Operations";
    else if (hasCreate && hasCopy) return "Create & Copy Operations";
    else if (hasCreate) return "Create Files/Directories";
    else if (hasDelete) return "Delete Files/Directories";
    else if (hasMove) return "Move Files/Directories";
    else if (hasCopy) return "Copy Files/Directories";
    else return "Terminal Commands";
  }

  private setWaitingState(waiting: boolean) {
    this.isWaitingForResponse = waiting;
    const sendBtn = document.getElementById("send-message");

    if (waiting) {
      // Change to stop button
      sendBtn!.innerHTML = "⏹";
      sendBtn!.className = "stop-btn";
      sendBtn!.title = "Stop generation";
    } else {
      // Change back to send button
      sendBtn!.innerHTML = "➤";
      sendBtn!.className = "send-btn";
      sendBtn!.title = "Send message";
    }
  }

  private stopGeneration() {
    this.removeThinkingMessage();
    this.setWaitingState(false);

    // Send stop command to extension
    this.vscode.postMessage({
      command: "stopGeneration",
    });

    // Add a message indicating generation was stopped
    this.addMessageToChat("assistant", "Generation stopped by user.");
  }

  private addConsoleOutput(
    message: string,
    level: string,
    heading?: string,
    rawMsgObj?: any
  ) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // CRITICAL FIX: Persist console output to state so it survives tab switches
    // Only persist if it's not a simple status update (to avoid spam)
    if (
      typeof message === "string" &&
      message.length > 10 &&
      !message.includes("🆕")
    ) {
      this.vscode.postMessage({
        command: "addChatMessage",
        message: {
          content: message,
          isUser: false,
          timestamp: Date.now(),
          messageType: "console", // Save the message type for proper restoration
          level: level,
          heading: heading,
          originalFormatting: true,
        },
      });
    }

    if (
      typeof message === "object" &&
      message !== null &&
      "message" in message
    ) {
      heading = (message as any).heading;
      message = (message as any).message;
    }

    let contentHtml = "";
    let extraClass = "";
    // Determine tool name for spinner
    let toolName = "";
    if (typeof message === "string") {
      if (/analyzing project structure/i.test(message)) toolName = "dir_tool";
      else if (/(📄\s*)?reading(\s+code\s+from)? /i.test(message))
        toolName = "file_read_tool";
      else if (/understanding structure/i.test(message) || /🏗️/i.test(message))
        toolName = "file_code_struc_tool";
      else if (
        /analyzing code patterns/i.test(message) ||
        /searching/i.test(message)
      )
        toolName = "semantic_search_tool";
    }
    const isRunning = toolName && this.toolStatus[toolName] === "running";
    const spinnerHtml = isRunning
      ? `<div class="loading-dots"><span></span><span></span><span></span></div>`
      : "";

    if (heading === "h2") {
      const match =
        typeof message === "string"
          ? message.match(/^([0-9]+):\s*(.*)$/)
          : null;
      if (match) {
        const num = match[1];
        const desc = match[2];
        contentHtml = `
          <div class=\"action-heading-block action-heading-h2\">\n            <span class=\"action-number-circle\">${num}</span>\n            <span class=\"action-heading-text\">${desc} ${spinnerHtml}</span>\n          </div>\n        `;
      } else {
        contentHtml = `<div class=\"action-heading-text action-heading-h2\">${message} ${spinnerHtml}</div>`;
      }
      extraClass = " action-heading-h2";
    } else if (
      typeof message === "string" &&
      /^(📄\s*)?(searching|analyzing|reading) /i.test(message.trim())
    ) {
      // Special handling for file reading messages to extract and highlight file names
      let displayMessage = message;
      let fileInfo = "";

      if (/(📄\s*)?reading /i.test(message)) {
        console.log(`🎯 Processing file reading message: "${message}"`);

        // Check if this is a generic "reading file content" message
        if (/reading\s+file\s+content/i.test(message)) {
          console.log(
            `🎯 Generic file content message detected, looking for context...`
          );

          // Try to extract filename from recent context
          const chatMessages = document.getElementById("chat-messages");
          let contextFileName = "";

          if (chatMessages) {
            // Look at the last few messages for file paths
            const recentMessages = Array.from(chatMessages.children).slice(-5);
            for (const msgElement of recentMessages.reverse()) {
              const msgText = msgElement.textContent || "";
              // Look for file paths in quotes or common patterns
              const filePathMatch = msgText.match(
                /['"`]([^'"`]*\.(java|js|ts|py|cpp|c|h|css|html|json|xml|yml|yaml|md|txt))['"]/i
              );
              if (filePathMatch && filePathMatch[1]) {
                contextFileName = filePathMatch[1];
                console.log(
                  `🎯 Found file path in context: "${contextFileName}"`
                );
                break;
              }
            }
          }

          if (contextFileName) {
            const justFileName =
              contextFileName.split("/").pop() || contextFileName;
            displayMessage = `📄 Reading ${justFileName}`;
            fileInfo = `<div class="action-file-path">${contextFileName}</div>`;
          } else {
            displayMessage = `📄 Reading file`;
            fileInfo = `<div class="action-file-path">Processing file content...</div>`;
          }
        } else {
          // Extract file name from reading message
          // Handle both "reading filename" and "reading code from filename" patterns
          const fileMatch = message.match(
            /(📄\s*)?reading(?:\s+code\s+from)?\s+(.+)/i
          );
          if (fileMatch && fileMatch[2]) {
            let fileName = fileMatch[2].trim();
            console.log(`🎯 Extracted file name: "${fileName}"`);

            // Check if the message contains line range information
            let lineRangeInfo = "";
            const lineRangeMatch = fileName.match(
              /(.+?)\s*\(lines?\s+(\d+)-(\d+)\)/i
            );
            if (lineRangeMatch) {
              // Extract file name and line range
              fileName = lineRangeMatch[1].trim();
              const lineFrom = lineRangeMatch[2];
              const lineTo = lineRangeMatch[3];
              lineRangeInfo = ` (lines ${lineFrom}-${lineTo})`;
              console.log(
                `🎯 Extracted line range: lines ${lineFrom}-${lineTo}`
              );
            }

            // Handle multiple files or clean up the file name
            if (fileName.includes(",")) {
              // Multiple files
              const files = fileName.split(",").map((f) => f.trim());
              displayMessage = `📄 Reading ${files.length} files${lineRangeInfo}`;
              fileInfo = `<div class="action-file-path">${files.join(
                ", "
              )}</div>`;
            } else {
              // Single file - extract just the file name if it's a path
              const justFileName = fileName.split("/").pop() || fileName;
              displayMessage = `📄 Reading ${justFileName}${lineRangeInfo}`;
              fileInfo = `<div class="action-file-path">${fileName}</div>`;
            }
          }
        }
      }

      contentHtml = `
        <div class="action-status">
          <div class="action-status-header">
            <div class="action-status-title">
              ${displayMessage}
              ${
                spinnerHtml
                  ? `<div class="action-status-spinner">${spinnerHtml}</div>`
                  : ""
              }
            </div>
          </div>
          ${fileInfo}
        </div>
      `;
      extraClass = " action-status-container";
    } else if (
      typeof message === "string" &&
      /understanding structure/i.test(message)
    ) {
      // Handle "Understanding structure" messages with proper action indicator styling
      let displayMessage = message;
      let fileInfo = "";

      // Extract filename from "Understanding structure of filename" pattern
      const fileMatch = message.match(
        /understanding\s+structure\s+of\s+(.+?)(?:\s+\(|$)/i
      );
      if (fileMatch && fileMatch[1]) {
        const fileName = fileMatch[1].trim();
        console.log(`🏗️ Extracted file name for structure: "${fileName}"`);

        // Extract just the file name if it's a path
        const justFileName = fileName.split("/").pop() || fileName;
        displayMessage = `🏗️ Understanding structure of ${justFileName}`;
        fileInfo = `<div class="action-file-path">${fileName}</div>`;
      }

      contentHtml = `
        <div class="action-status">
          <div class="action-status-header">
            <div class="action-status-title">
              ${displayMessage}
              ${
                spinnerHtml
                  ? `<div class="action-status-spinner">${spinnerHtml}</div>`
                  : ""
              }
            </div>
          </div>
          ${fileInfo}
        </div>
      `;
      extraClass = " action-status-container";
    } else if (heading === "h3") {
      contentHtml = `<h3 style=\"font-family: inherit; font-size: 1.1em; font-weight: 600; margin: 12px 0 8px 0;\">${message}</h3>`;
    } else {
      contentHtml = `<div class=\"status-text\">${message}</div>`;
    }

    const statusDiv = document.createElement("div");
    statusDiv.className = `status-message status-${level}${extraClass}`;
    statusDiv.innerHTML = contentHtml;
    chatMessages.appendChild(statusDiv);
    this.scrollToBottom();
  }

  private addSummaryMessage(content: string) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // CRITICAL FIX: Persist the summary message to state so it survives tab switches
    this.vscode.postMessage({
      command: "addChatMessage",
      message: {
        content: content,
        isUser: false,
        timestamp: Date.now(),
        messageType: "summary", // Save the message type for proper restoration
        originalFormatting: true,
      },
    });

    // Create a simple status message that matches the other status messages
    const summaryDiv = document.createElement("div");
    summaryDiv.className = "message assistant status-message";

    // Format as plain text with consistent styling
    const cleanContent = this.formatPlainTextContent(content);

    summaryDiv.innerHTML = `
      <div class="action-heading-h2">📋 What I have done is</div>
      <div class="summary-details-clean">${cleanContent}</div>
    `;

    chatMessages.appendChild(summaryDiv);

    // Force immediate scroll
    chatMessages.scrollTop = chatMessages.scrollHeight;
    this.scrollToBottom();
  }

  private isErrorMessage(message: string): boolean {
    const errorPatterns = [
      "Error connecting to coder agent",
      "WebSocket connection timeout",
      "Connection failed",
      "Server error",
      "Failed to connect",
      "Timeout",
      "❌",
    ];

    return errorPatterns.some((pattern) =>
      message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  private addErrorMessage(errorMsg: string) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Remove thinking message if present
    this.removeThinkingMessage();
    this.setWaitingState(false);

    // Clean the error message by removing emoji and formatting professionally
    const cleanErrorMsg = errorMsg.replace(/❌\s*/, "").trim();

    // CRITICAL FIX: Persist the error message to state so it survives tab switches
    this.vscode.postMessage({
      command: "addChatMessage",
      message: {
        content: cleanErrorMsg,
        isUser: false,
        timestamp: Date.now(),
        messageType: "error", // Save the message type for proper restoration
        originalFormatting: true,
      },
    });

    const errorDiv = document.createElement("div");
    errorDiv.className = "message assistant error-message";

    errorDiv.innerHTML = `
      <div class="error-content">
        <div class="error-text">${cleanErrorMsg}</div>
      </div>
    `;

    chatMessages.appendChild(errorDiv);

    // Mark that an error was shown for this request
    this.errorShownForCurrentRequest = true;

    // Force immediate scroll
    chatMessages.scrollTop = chatMessages.scrollHeight;
    this.scrollToBottom();
  }

  private displayTasksIdentified(data: any) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    console.log("📋 Tasks data received:", data); // Debug log
    console.log("📋 Tasks array:", data.tasks); // Debug log

    const tasksDiv = document.createElement("div");
    tasksDiv.className = "message assistant tasks-identified";

    const tasks = data.tasks || [];
    const phase = data.phase || "analysis";

    console.log("📋 Tasks array length:", tasks.length); // Debug log
    console.log("📋 First task:", tasks[0]); // Debug log

    // CRITICAL FIX: Persist tasks identified to state so it survives tab switches
    // Create a safe copy of the data to avoid serialization issues
    const safeTasksData = {
      tasks: Array.isArray(data.tasks)
        ? data.tasks.map((task: any) => ({
            description: task.description || "No description available",
            task_id: task.task_id || task.id || "",
            dependencies: task.dependencies || [],
            // Keep original task structure for compatibility
            ...task,
          }))
        : [],
      phase: data.phase || "analysis",
      user_acknowledgement: data.user_acknowledgement || "",
    };

    console.log("💾 Saving tasks data:", safeTasksData);
    console.log("💾 First task:", safeTasksData.tasks[0]);

    this.vscode.postMessage({
      command: "addChatMessage",
      message: {
        content: `Tasks Identified: ${safeTasksData.tasks.length} tasks`, // Readable content
        isUser: false,
        timestamp: Date.now(),
        messageType: "tasks_identified", // Save the message type for proper restoration
        originalFormatting: true,
        tasksData: safeTasksData, // Store structured data for restoration
      },
    });

    tasksDiv.innerHTML = `
      <div class="tasks-header-collapsible">
        <div class="tasks-title-row">
          <h4>Tasks Identified</h4>
          <span class="tasks-count">${tasks.length} tasks</span>
          <span class="collapse-indicator">▼</span>
        </div>
      </div>
      <div class="tasks-list collapsed">
        ${
          tasks.length > 0
            ? tasks
                .map(
                  (task: any, index: number) => `
              <div class="task-item-clean">
                <div class="task-number">${task.task_id || index + 1}</div>
                <div class="task-content">
                  <div class="task-description-clean">${
                    task.description || "No description available"
                  }</div>
                  ${
                    task.dependencies && task.dependencies.length > 0
                      ? `<div class="task-dependencies">Dependencies: ${task.dependencies.join(
                          ", "
                        )}</div>`
                      : ""
                  }
                </div>
              </div>
            `
                )
                .join("")
            : '<div class="no-tasks-message">No tasks identified yet</div>'
        }
      </div>
    `;

    chatMessages.appendChild(tasksDiv);
    // Attach event listener for collapsible dropdown
    const header = tasksDiv.querySelector(".tasks-header-collapsible");
    const list = tasksDiv.querySelector(".tasks-list");
    if (header && list) {
      header.addEventListener("click", () => {
        list.classList.toggle("collapsed");
      });
    }
    this.scrollToBottom();
  }

  private displayExecutionPlan(data: any) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    console.log("📋 Execution Plan data received:", data); // Debug log
    console.log("📋 Execution steps:", data.execution_steps); // Debug log

    const planDiv = document.createElement("div");
    planDiv.className = "message assistant execution-plan-message";

    const steps = data.execution_steps || [];
    const planId = `execution-plan-${Date.now()}`;

    console.log("📋 Steps array length:", steps.length); // Debug log
    console.log("📋 First step:", steps[0]); // Debug log

    planDiv.innerHTML = `
      <div class="task-list-header">
        <div class="status-content">
          <div class="status-icon">📋</div>
          <div class="status-text">Execution Plan Ready</div>
          <span class="tasks-count">${steps.length} steps</span>
          <div class="collapse-icon">▼</div>
        </div>
      </div>
      <div class="task-list-content collapsed" id="${planId}">
        ${steps
          .map(
            (step: any, index: number) => `
          <div class="execution-step-item">
            <div class="step-header">
              <div class="step-number">${index + 1}</div>
              <div class="step-info">
                <div class="step-title">
                  <span class="step-type-badge">${
                    step.step_type || "task"
                  }</span>
                  <span class="action-type-badge">${
                    step.action_type || "action"
                  }</span>
                </div>
              </div>
            </div>
            <div class="step-content">
              <div class="execution-details">${
                step.execution_details ||
                step.description ||
                "No details available"
              }</div>
              <div class="step-metadata">
                ${
                  step.target && step.target !== "N/A"
                    ? `<div class="metadata-item target-folder">Target: ${step.target}</div>`
                    : ""
                }
                ${
                  step.prerequisites && step.prerequisites.length > 0
                    ? `<div class="metadata-item prerequisites">Prerequisites: ${step.prerequisites.join(
                        ", "
                      )}</div>`
                    : ""
                }
                <div class="metadata-item effort-estimate">Effort: ${
                  step.estimated_effort || "unknown"
                }</div>
              </div>
            </div>
          </div>
        `
          )
          .join("")}
      </div>
    `;

    chatMessages.appendChild(planDiv);
    // Attach event listener for collapsible dropdown
    const header = planDiv.querySelector(".task-list-header");
    const content = planDiv.querySelector(".task-list-content");
    if (header && content) {
      header.addEventListener("click", () => {
        content.classList.toggle("collapsed");
      });
    }
    this.scrollToBottom();
  }

  private showFileOperation(
    operation: string,
    filePath: string,
    status: "in-progress" | "success" | "error" = "in-progress"
  ) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Preserve scroll position during DOM modifications
    const currentScrollTop = chatMessages.scrollTop;
    const currentScrollHeight = chatMessages.scrollHeight;

    console.log(
      `🔄 showFileOperation: preserving scroll position ${currentScrollTop}`
    );

    // Remove any existing file operation indicator for the same file
    const existingIndicator = document.querySelector(
      `[data-file-path="${filePath}"]`
    );
    if (existingIndicator) {
      existingIndicator.remove();
    }

    const operationDiv = document.createElement("div");
    operationDiv.className = "file-operation-indicator";
    operationDiv.setAttribute("data-file-path", filePath);

    // Map operation types to icons and text
    const operationMap: { [key: string]: { icon: string; text: string } } = {
      create: { icon: "📄", text: "Created file" },
      edit: { icon: "✏️", text: "Edited file" },
      modify: { icon: "✏️", text: "Modified file" },
      delete: { icon: "🗑️", text: "Deleted file" },
      move: { icon: "📦", text: "Moved file" },
      copy: { icon: "📋", text: "Copied file" },
    };

    const opInfo = operationMap[operation.toLowerCase()] || {
      icon: "📝",
      text: "Updated file",
    };

    // Status indicators
    let statusIcon = "";
    let statusClass = "";

    switch (status) {
      case "in-progress":
        statusIcon =
          '<div class="loading-dots"><span></span><span></span><span></span></div>';
        statusClass = "in-progress";
        break;
      case "success":
        statusIcon = "✅";
        statusClass = "success";
        break;
      case "error":
        statusIcon = "❌";
        statusClass = "error";
        break;
    }

    operationDiv.innerHTML = `
      <div class="file-operation-header">
        <div class="file-operation-action">
          <span class="file-operation-icon">${opInfo.icon}</span>
          <span>${opInfo.text}</span>
        </div>
        <div class="file-operation-status ${statusClass}">
          ${statusIcon}
        </div>
      </div>
      <div class="file-operation-path">${filePath}</div>
    `;

    chatMessages.appendChild(operationDiv);

    // Restore scroll position if it was significantly scrolled up
    // Only restore if user was not near the bottom (to avoid interfering with normal auto-scroll)
    if (currentScrollTop > 200) {
      console.log(
        `🔄 showFileOperation: restoring scroll position to ${currentScrollTop}`
      );
      chatMessages.scrollTop = currentScrollTop;
    } else {
      // User was near bottom, use normal scroll behavior
      this.scrollToBottom();
    }

    // Auto-remove success indicators after 3 seconds
    if (status === "success") {
      setTimeout(() => {
        if (operationDiv.parentNode) {
          operationDiv.remove();
        }
      }, 3000);
    }
  }

  private updateFileOperationStatus(
    filePath: string,
    status: "success" | "error"
  ) {
    const indicator = document.querySelector(`[data-file-path="${filePath}"]`);
    if (!indicator) return;

    const statusElement = indicator.querySelector(".file-operation-status");
    if (!statusElement) return;

    // Update status
    statusElement.className = `file-operation-status ${status}`;

    if (status === "success") {
      statusElement.innerHTML = "✅";
      // Auto-remove after 3 seconds
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.remove();
        }
      }, 3000);
    } else if (status === "error") {
      statusElement.innerHTML = "❌";
    }
  }

  private formatPlainTextContent(content: string): string {
    // Format content with proper markdown rendering
    let formatted = content;

    // Convert markdown bold formatting to HTML
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");

    // Convert backticks to code formatting
    formatted = formatted.replace(/`([^`]+)`/g, "<code>$1</code>");

    // Split into lines and format each line
    const lines = formatted.split("\n");
    const formattedLines = lines
      .map((line) => {
        line = line.trim();
        if (!line) return "";

        // Format bullet points consistently
        if (line.startsWith("• ") || line.startsWith("- ")) {
          return `<div class="summary-item">${line}</div>`;
        }

        // Format regular text
        return `<div class="summary-text">${line}</div>`;
      })
      .filter((line) => line !== "");

    return formattedLines.join("");
  }

  private addTaskListMessage(data: any) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // CRITICAL FIX: Persist task list message to state so it survives tab switches
    this.vscode.postMessage({
      command: "addChatMessage",
      message: {
        content: JSON.stringify(data), // Store the full data structure
        isUser: false,
        timestamp: Date.now(),
        messageType: "task_list", // Save the message type for proper restoration
        originalFormatting: true,
        taskListData: data, // Store structured data for restoration
      },
    });

    // Create a collapsible task list message
    const taskListDiv = document.createElement("div");
    taskListDiv.className = "message assistant task-list-message";

    const taskListId = `task-list-${Date.now()}`;

    taskListDiv.innerHTML = `
      <div class="task-list-header">
        <div class="status-content">
          <div class="status-icon">📝</div>
          <div class="status-text">${data.message}</div>
          <div class="collapse-icon">▶</div>
        </div>
      </div>
      <div class="task-list-content collapsed" id="${taskListId}">
        ${data.tasks
          .map((task: string, index: number) => {
            // Use a predictable tool name based on task order
            const toolNames = [
              "dir_tool",
              "file_read_tool",
              "semantic_search_tool",
              "code_expert_tool",
              "code_applier_tool",
              "summary_tool",
            ];
            const toolName = toolNames[index] || `task_${index}`;

            return `
          <div class="task-item">
            <span class="task-number">${index + 1}.</span>
            <span class="task-text">${task}</span>
            <span class="tool-status-indicator" id="tool-status-${toolName}" style="margin-left: auto; font-size: 14px; padding: 2px 4px; border-radius: 3px; background: rgba(255,255,255,0.1);">⏳</span>
          </div>
        `;
          })
          .join("")}
      </div>
    `;

    // Start collapsed by default
    taskListDiv.classList.add("collapsed");

    // Add click handler for collapsing/expanding
    const header = taskListDiv.querySelector(".task-list-header");
    const content = taskListDiv.querySelector(".task-list-content");
    if (header && content) {
      header.addEventListener("click", () => {
        content.classList.toggle("collapsed");
        taskListDiv.classList.toggle("collapsed");
      });
    }

    chatMessages.appendChild(taskListDiv);

    // Force immediate scroll
    chatMessages.scrollTop = chatMessages.scrollHeight;
    this.scrollToBottom();
  }

  private addExecutionPlanMessage(data: any) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    console.log("📋 addExecutionPlanMessage data received:", data); // Debug log
    console.log("📋 data.steps:", data.steps); // Debug log
    console.log("📋 data.execution_steps:", data.execution_steps); // Debug log

    // Create a collapsible execution plan message
    const executionPlanDiv = document.createElement("div");
    executionPlanDiv.className = "message assistant execution-plan-message";

    const executionPlanId = `execution-plan-${Date.now()}`;

    executionPlanDiv.innerHTML = `
      <div class="task-list-header">
        <div class="status-content">
          <div class="status-icon">📋</div>
          <div class="status-text">${
            data.message || "Execution Plan Ready"
          }</div>
          <span class="tasks-count">${
            (data.steps || data.execution_steps || []).length
          } steps</span>
          <div class="collapse-icon">▼</div>
        </div>
      </div>
      <div class="task-list-content collapsed" id="${executionPlanId}">
        ${(data.steps || data.execution_steps || [])
          .map((step: any, index: number) => {
            return `
          <div class="execution-step-item">
            <div class="step-header">
              <div class="step-number">${index + 1}</div>
              <div class="step-info">
                <div class="step-title">
                  <span class="step-type-badge">${
                    step.step_type || "task"
                  }</span>
                  <span class="action-type-badge">${
                    step.action_type || "action"
                  }</span>
                </div>
              </div>
            </div>
            <div class="step-content">
              <div class="execution-details">${
                step.execution_details ||
                step.description ||
                "No details available"
              }</div>
              <div class="step-metadata">
                ${
                  (step.target || step.target_folder) &&
                  step.target !== "N/A" &&
                  step.target_folder !== "N/A"
                    ? `<div class="metadata-item target-folder">Target: ${
                        step.target || step.target_folder
                      }</div>`
                    : ""
                }
                ${
                  step.prerequisites && step.prerequisites.length > 0
                    ? `<div class="metadata-item prerequisites">Prerequisites: ${step.prerequisites.join(
                        ", "
                      )}</div>`
                    : ""
                }
                <div class="metadata-item effort-estimate">Effort: ${
                  step.estimated_effort || "unknown"
                }</div>
              </div>
            </div>
          </div>
        `;
          })
          .join("")}
      </div>
    `;

    // Start collapsed by default
    executionPlanDiv.classList.add("collapsed");

    // Add click handler for collapsing/expanding
    const header = executionPlanDiv.querySelector(".task-list-header");
    const content = executionPlanDiv.querySelector(".task-list-content");
    if (header && content) {
      header.addEventListener("click", () => {
        content.classList.toggle("collapsed");
        executionPlanDiv.classList.toggle("collapsed");
      });
    }

    chatMessages.appendChild(executionPlanDiv);

    // Force immediate scroll
    chatMessages.scrollTop = chatMessages.scrollHeight;
    this.scrollToBottom();
  }

  private getToolNameFromTask(task: string): string {
    // Map task descriptions to tool names based on professional status messages
    const taskLower = task.toLowerCase();

    if (
      taskLower.includes("analyzing project structure") ||
      taskLower.includes("📂")
    )
      return "dir_tool";
    if (taskLower.includes("reading") || taskLower.includes("📄"))
      return "file_read_tool";
    if (
      taskLower.includes("understanding structure") ||
      taskLower.includes("understanding code structure") ||
      taskLower.includes("🏗️")
    )
      return "file_code_struc_tool";
    if (
      taskLower.includes("searching codebase") ||
      taskLower.includes("analyzing code patterns") ||
      taskLower.includes("🔍") ||
      taskLower.includes("🧠")
    )
      return "semantic_search_tool";
    if (
      taskLower.includes("generating code") ||
      taskLower.includes("code changes")
    )
      return "code_expert_tool";
    if (taskLower.includes("applying code") || taskLower.includes("✏️"))
      return "code_applier_tool";
    if (taskLower.includes("generating summary") || taskLower.includes("📋"))
      return "summary_tool";

    return "unknown_tool";
  }

  private getToolNameFromMessage(message: string): string {
    // Map console output messages to tool names
    const messageLower = message.toLowerCase();

    if (
      messageLower.includes("analyzing project structure") ||
      messageLower.includes("📂")
    )
      return "dir_tool";
    if (
      messageLower.includes("reading file") ||
      messageLower.includes("reading code from") ||
      messageLower.includes("📄")
    )
      return "file_read_tool";
    if (
      messageLower.includes("understanding structure") ||
      messageLower.includes("understanding code structure") ||
      messageLower.includes("🏗️")
    )
      return "file_code_struc_tool";
    if (
      messageLower.includes("searching codebase") ||
      messageLower.includes("🔍")
    )
      return "semantic_search_tool";
    if (
      messageLower.includes("analyzing code patterns") ||
      messageLower.includes("🧠")
    )
      return "code_expert_tool";
    if (messageLower.includes("applying code") || messageLower.includes("✏️"))
      return "code_applier_tool";
    if (
      messageLower.includes("generating summary") ||
      messageLower.includes("📋")
    )
      return "summary_tool";

    return "unknown_tool";
  }

  /**
   * Show file reading status with line range information
   */
  private showFileReadingStatus(message: string, status: string) {
    console.log(`📄 Showing file reading status: "${message}" (${status})`);

    // Parse the message to extract file name and line range
    // Expected formats:
    // - "Reading filename.ext (lines 1-50)"
    // - "Reading code from filename.ext (lines 1-50)"
    const match = message.match(
      /Reading(?:\s+code\s+from)?\s+(.+?)(?:\s+\(lines?\s+(\d+)-(\d+)\))?$/i
    );
    if (!match) {
      console.log(`📄 Could not parse file reading message: "${message}"`);
      return;
    }

    const fileName = match[1].trim();
    const lineFrom = match[2];
    const lineTo = match[3];
    const lineRangeInfo =
      lineFrom && lineTo ? ` (lines ${lineFrom}-${lineTo})` : "";

    // Use the same action indicator system as other tools for consistent styling
    const displayText = lineRangeInfo
      ? `${fileName}${lineRangeInfo}`
      : fileName;
    const actionId = this.showActionIndicator(
      "Reading file",
      displayText,
      fileName, // Use filename as path
      "in-progress" // Always start as in-progress, will be updated by general tool status handler
    );

    console.log(`📄 Created file reading action with ID: ${actionId}`);
  }

  /**
   * Update existing file reading action indicator with line range information from success message
   */
  private updateFileReadingActionWithLineRange(message: string) {
    console.log(
      `📄 Updating file reading action with line range: "${message}"`
    );

    // Parse the success message to extract file name and line range
    // Expected format: "Reading code from filename.ext (lines 1-50)"
    const match = message.match(
      /Reading\s+code\s+from\s+(.+?)(?:\s+\(lines?\s+(\d+)-(\d+)\))?$/i
    );
    if (!match) {
      console.log(
        `📄 Could not parse file reading success message: "${message}"`
      );
      console.log(`📄 Trying alternative regex patterns...`);

      // Try alternative patterns
      const altMatch1 = message.match(
        /Reading\s+(.+?)(?:\s+\(lines?\s+(\d+)-(\d+)\))?$/i
      );
      const altMatch2 = message.match(
        /(.+?)(?:\s+\(lines?\s+(\d+)-(\d+)\))?$/i
      );

      console.log(`📄 Alternative match 1:`, altMatch1);
      console.log(`📄 Alternative match 2:`, altMatch2);
      return;
    }

    const fileName = match[1].trim();
    const lineFrom = match[2];
    const lineTo = match[3];
    const lineRangeInfo =
      lineFrom && lineTo ? ` (lines ${lineFrom}-${lineTo})` : "";

    // Find existing action indicator for this file
    const actionIndicators = document.querySelectorAll(".action-status");
    console.log(
      `📄 Looking for action indicator among ${actionIndicators.length} indicators`
    );

    for (let i = 0; i < actionIndicators.length; i++) {
      const indicator = actionIndicators[i];
      const headingElement = indicator.querySelector(".action-heading-text");

      if (!headingElement) {
        console.log(`📄 Indicator ${i}: No heading element found`);
        continue;
      }

      const headingText = headingElement.textContent || "";
      console.log(`📄 Indicator ${i}: headingText="${headingText}"`);

      // Check if this is a reading file action for the same file
      if (headingText.includes("Reading") && headingText.includes(fileName)) {
        console.log(`📄 Found matching indicator for file: ${fileName}`);

        // Update the heading text to include line range
        if (lineRangeInfo) {
          // Replace the existing text with updated version including line range
          const updatedText = headingText.replace(
            new RegExp(
              `Reading.*?${fileName.replace(
                /[.*+?^${}()|[\]\\]/g,
                "\\$&"
              )}.*?(?=\\s*[⏳✅❌]|$)`,
              "i"
            ),
            `📖 Reading ${fileName}${lineRangeInfo}`
          );
          headingElement.textContent =
            updatedText.replace(/[⏳✅❌]/g, "") + " ✅";
          console.log(
            `📄 Updated action indicator with line range: ${updatedText}`
          );
        } else {
          // Just update the status icon
          headingElement.textContent =
            headingText.replace(/[⏳✅❌]/g, "") + " ✅";
        }
        break;
      }
    }
  }

  /**
   * Show file structure analysis status similar to file reading status
   */
  private showFileStructureStatus(message: string, status: string) {
    console.log(`🏗️ Showing file structure status: "${message}" (${status})`);

    // Extract filename from the message - handle both "Understanding structure of filename" and generic messages
    let fileName = "";

    // Try to match "Understanding structure of filename" pattern
    let fileMatch = message.match(
      /Understanding\s+structure\s+of\s+(.+?)(?:\s+\(|$)/i
    );
    if (fileMatch) {
      fileName = fileMatch[1].trim();
    } else {
      // Try to extract any filename with common extensions
      fileMatch = message.match(
        /([^\/\s]+\.(java|py|js|ts|cpp|c|h|cs|php|rb|go|rs|kt|swift))/i
      );
      if (fileMatch) {
        fileName = fileMatch[1];
      } else {
        console.log(`🏗️ No filename found in structure message: "${message}"`);
        return;
      }
    }

    console.log(`🏗️ Extracted filename for structure analysis: "${fileName}"`);

    // Use the same action indicator system as other tools for consistent styling
    const actionId = this.showActionIndicator(
      "Understanding structure",
      fileName,
      fileName, // Use filename as path for now
      "in-progress" // Always start as in-progress, will be updated by general tool status handler
    );

    console.log(`🏗️ Created structure analysis action with ID: ${actionId}`);
  }

  private updateActionStatusForTool(
    toolName: string,
    status: string,
    message?: string
  ) {
    console.log(
      `🎯 Updating action status for tool: ${toolName} -> ${status}${
        message ? ` (${message})` : ""
      }`
    );

    // For file_read_tool, create action status on "running" or update with line range on "success"
    if (toolName === "file_read_tool") {
      if (status === "running" && message) {
        console.log(
          `🎯 Processing file_read_tool running message: "${message}"`
        );
        this.showFileReadingStatus(message, status);
        return;
      } else if (
        status === "success" &&
        message &&
        message.includes("Reading code from")
      ) {
        console.log(
          `🎯 Processing file_read_tool success message with line range: "${message}"`
        );
        // Parse the success message to extract line range and update existing action indicator
        this.updateFileReadingActionWithLineRange(message);
        return;
      }
    }

    // For read_code_file_struct_tool, create action status only on first "running" status with message
    if (
      toolName === "read_code_file_struct_tool" &&
      status === "running" &&
      message
    ) {
      console.log(
        `🎯 Processing read_code_file_struct_tool running message: "${message}"`
      );
      this.showFileStructureStatus(message, status);
      return;
    }

    // Map tool names to action message patterns
    const toolToActionMap: { [key: string]: RegExp } = {
      dir_tool: /analyzing project structure/i,
      file_read_tool: /(📄\s*)?reading/i,
      semantic_search_tool: /analyzing code patterns|searching/i,
      code_expert_tool: /analyzing code patterns/i,
      code_applier_tool: /applying code changes/i,
      read_code_file_struct_tool: /understanding (structure|file structure)/i,
    };

    const actionPattern = toolToActionMap[toolName];
    if (!actionPattern) {
      console.log(`🎯 No action pattern found for tool: ${toolName}`);
      return;
    }

    // Find all action status elements that match this tool's pattern
    const actionElements = document.querySelectorAll(".action-status");
    console.log(`🎯 Found ${actionElements.length} action status elements`);

    actionElements.forEach((actionElement, index) => {
      // Try both possible title element classes
      let titleElement = actionElement.querySelector(".action-status-title");
      if (!titleElement) {
        titleElement = actionElement.querySelector(".action-heading-text");
      }

      if (!titleElement) {
        console.log(`🎯 Element ${index}: No title element found`);
        return;
      }

      const titleText = titleElement.textContent || "";
      console.log(`🎯 Element ${index}: Title text = "${titleText}"`);

      if (!actionPattern.test(titleText)) {
        console.log(
          `🎯 Element ${index}: Title doesn't match pattern for ${toolName}`
        );
        return;
      }

      console.log(`🎯 Element ${index}: MATCH! Updating status to ${status}`);

      // Update spinner and status based on tool status
      // Look for spinner in title area and status icon in controls area (current structure)
      let spinnerElement = titleElement.querySelector(".action-status-spinner");
      let statusIconInControls = actionElement.querySelector(
        ".action-status-controls .action-status-icon"
      ) as HTMLElement;

      // Create a consistent status indicator container in the title
      let statusContainer = titleElement.querySelector(
        ".tool-status-indicator"
      ) as HTMLElement;
      if (!statusContainer) {
        statusContainer = document.createElement("span");
        statusContainer.className = "tool-status-indicator";
        statusContainer.style.marginLeft = "8px";
        titleElement.appendChild(statusContainer);
      }

      switch (status) {
        case "running":
          console.log(`🎯 Setting running status with spinner`);
          statusContainer.innerHTML =
            '<div class="loading-dots"><span></span><span></span><span></span></div>';

          // Remove old spinner if it exists elsewhere
          if (spinnerElement && spinnerElement !== statusContainer) {
            spinnerElement.remove();
          }
          // Hide status icon in controls
          if (statusIconInControls) {
            statusIconInControls.style.display = "none";
          }
          break;

        case "success":
          console.log(`🎯 Setting success status with green tick`);
          statusContainer.innerHTML =
            '<span class="action-status-icon success">✅</span>';

          // Remove old spinner if it exists elsewhere
          if (spinnerElement && spinnerElement !== statusContainer) {
            spinnerElement.remove();
          }
          // Hide status icon in controls
          if (statusIconInControls) {
            statusIconInControls.style.display = "none";
          }
          break;

        case "error":
          console.log(`🎯 Setting error status with red X`);
          statusContainer.innerHTML =
            '<span class="action-status-icon error">❌</span>';

          // Remove old spinner if it exists elsewhere
          if (spinnerElement && spinnerElement !== statusContainer) {
            spinnerElement.remove();
          }
          // Hide status icon in controls
          if (statusIconInControls) {
            statusIconInControls.style.display = "none";
          }
          break;
      }
    });
  }

  private updateToolStatus(data: any) {
    const { tool_name, status, message } = data;
    const expectedId = `tool-status-${tool_name}`;

    // Find ALL elements with this tool name (there might be duplicates)
    const allToolElements = document.querySelectorAll(`[id="${expectedId}"]`);

    if (allToolElements.length > 0) {
      // Update ALL elements with this tool name
      allToolElements.forEach((statusElement) => {
        this.applyStatusToElement(
          statusElement as HTMLElement,
          status,
          message
        );
      });
    }
  }

  private applyStatusToElement(
    statusElement: HTMLElement,
    status: string,
    message?: string
  ) {
    // If element doesn't exist, skip (we're only updating existing elements now)
    if (!statusElement) {
      console.log(`🔧 Cannot update - element is null`);

      return;
    }

    if (statusElement) {
      // Update the status indicator with professional styling
      switch (status) {
        case "running":
          statusElement.innerHTML = "⏳";
          statusElement.style.cssText =
            "font-size: 14px; padding: 2px 6px; border-radius: 4px; background: rgba(255,165,0,0.2); color: #ffa500; border: 1px solid rgba(255,165,0,0.3); margin-left: 8px;";
          statusElement.title = message || "Running...";
          break;
        case "success":
          statusElement.innerHTML = "✅";
          statusElement.style.cssText =
            "font-size: 14px; padding: 2px 6px; border-radius: 4px; background: rgba(40,167,69,0.2); color: #28a745; border: 1px solid rgba(40,167,69,0.3); margin-left: 8px;";
          statusElement.title = message || "Success";
          break;
        case "error":
          statusElement.innerHTML = "❌";
          statusElement.style.cssText =
            "font-size: 14px; padding: 2px 6px; border-radius: 4px; background: rgba(220,53,69,0.2); color: #dc3545; border: 1px solid rgba(220,53,69,0.3); margin-left: 8px;";
          statusElement.title = message || "Error";
          break;
      }
    }
  }

  private handleQueueStatus(
    status: string,
    position?: number,
    message?: string
  ) {
    if (status === "queued" && position && position > 0) {
      // Show queue notification
      this.addConsoleOutput(
        `📋 Your request has been added to queue (position: ${position})`,
        "info"
      );
    } else if (status === "processing") {
      // Show processing notification
      this.addConsoleOutput("🔄 Your request is now being processed", "info");
    }
  }

  private updateAgentStatus(
    status: string,
    details: string,
    animation?: string
  ) {
    // Create or update activity banner
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Remove any existing activity banner
    const existingBanner = document.getElementById("activity-banner");
    if (existingBanner) {
      existingBanner.remove();
    }

    // Map server status to user-friendly messages
    const statusMessages: { [key: string]: string } = {
      analyzing_request: "Analyzing user request and requirements",
      analyzing_impact: "Analyzing impact and dependencies",
      creating_execution_plan: "Creating detailed execution plan",
      request_analysis_complete: "Request analysis complete",
      impact_analysis_complete: "Impact analysis complete",
      execution_plan_complete: "Execution plan complete",
    };

    // Don't show banner for completed status
    if (
      status === "completed" ||
      status === "complete" ||
      status.includes("_complete")
    ) {
      return;
    }

    // Create new activity banner
    const activityBanner = document.createElement("div");
    activityBanner.id = "activity-banner";
    activityBanner.className = "activity-banner";

    // Use mapped message or fallback to details/status
    let activityText = statusMessages[status] || details || status;
    activityText = activityText.replace(/^Step \d+\/\d+:\s*/, "").trim();

    // Determine if this is a completed activity
    const isCompleted =
      animation === "stop" ||
      status === "complete" ||
      status.includes("_complete");
    const tickMark = isCompleted ? "✓" : "";

    activityBanner.innerHTML = `
      <div class="activity-content">
        <span class="activity-text">${activityText}</span>
        ${tickMark ? `<span class="activity-tick">${tickMark}</span>` : ""}
      </div>
    `;

    chatMessages.appendChild(activityBanner);
    this.scrollToBottom();
  }

  private displayAgentReasoning(data: any) {
    try {
      console.log(`🤖💭 Displaying agent reasoning:`, data);

      if (!data || !data.reasoning) {
        console.log(`⚠️ No reasoning data provided, skipping display`);
        return;
      }

      const { next_tool, reasoning, professional_message } = data;

      // Create a simple reasoning display without box or heading
      const reasoningHtml = `
        <div class="reasoning-text">${reasoning}</div>
      `;

      // Add to chat area
      const chatMessages = document.getElementById("chat-messages");
      if (chatMessages) {
        const messageDiv = document.createElement("div");
        messageDiv.innerHTML = reasoningHtml;
        chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
      }
    } catch (error) {
      console.error(`❌ Error displaying agent reasoning:`, error);
      // Don't show error to user - just log it
    }
  }

  private adjustTextareaHeight(textarea: HTMLTextAreaElement) {
    textarea.style.height = "28px"; // Reset to minimum height
    const newHeight = Math.min(Math.max(textarea.scrollHeight, 28), 120);
    textarea.style.height = newHeight + "px";
  }

  private handleAttachment() {
    const fileInput = document.getElementById("file-input") as HTMLInputElement;
    fileInput?.click();
  }

  private handleFileSelection(event: Event) {
    const target = event.target as HTMLInputElement;
    const files = target.files;

    if (files && files.length > 0) {
      this.displayAttachedFiles(Array.from(files));
    }
  }

  private displayAttachedFiles(files: File[]) {
    const attachmentList = document.getElementById("attachment-list");
    if (!attachmentList) return;

    attachmentList.classList.remove("hidden");
    attachmentList.innerHTML = "";

    files.forEach((file, index) => {
      const attachmentItem = document.createElement("div");
      attachmentItem.className = "attachment-item";
      attachmentItem.innerHTML = `
        <span>📄 ${file.name}</span>
        <span class="remove-btn" data-index="${index}">×</span>
      `;

      // Add remove functionality
      const removeBtn = attachmentItem.querySelector(".remove-btn");
      removeBtn?.addEventListener("click", () => {
        this.removeAttachment(index);
      });

      attachmentList.appendChild(attachmentItem);
    });
  }

  private removeAttachment(index: number) {
    // For now, just hide the attachment list if no files
    const attachmentList = document.getElementById("attachment-list");
    const items = attachmentList?.querySelectorAll(".attachment-item");

    if (items && items[index]) {
      items[index].remove();

      // Hide list if no more attachments
      if (attachmentList?.children.length === 0) {
        attachmentList.classList.add("hidden");
      }
    }
  }

  private loadOpenFiles() {
    // Request open files from extension
    this.vscode.postMessage({ command: "getOpenFiles" });
  }

  // Add this utility at the class level, not inside another method
  private isCodeFile(fileName: string): boolean {
    if (!fileName) return false;
    const lower = fileName.toLowerCase();
    const isNonCode =
      lower.startsWith("extension-output-") ||
      lower.includes("json-server") ||
      lower.endsWith(".log") ||
      lower.endsWith(".output") ||
      lower.endsWith(".tmp") ||
      lower.startsWith(".") || // hidden/system files
      lower.includes("output"); // catch more output patterns
    if (isNonCode) {
      console.log("Filtered out non-code file:", fileName);
    }
    return !isNonCode;
  }

  private displayFileTabs(files: string[]) {
    const fileTabsContainer = document.getElementById("file-tabs");
    if (!fileTabsContainer) return;

    fileTabsContainer.innerHTML = "";

    // Filter out non-code files
    const codeFiles = files.filter((filePath) => {
      const fileName = filePath.split("/").pop() || filePath;
      return this.isCodeFile(fileName);
    });

    if (codeFiles.length === 0) {
      // Hide file tabs UI if no code files
      fileTabsContainer.innerHTML = "";
      return;
    }

    codeFiles.forEach((filePath, index) => {
      const fileName = filePath.split("/").pop() || filePath;
      const fileTab = document.createElement("div");
      fileTab.className = `file-tab ${index === 0 ? "active" : ""}`;
      fileTab.innerHTML = `
        <span>${fileName}</span>
        <span class="close-btn">×</span>
      `;

      fileTab.addEventListener("click", (e) => {
        if (!(e.target as HTMLElement).classList.contains("close-btn")) {
          this.selectFileTab(filePath);
        }
      });

      const closeBtn = fileTab.querySelector(".close-btn");
      closeBtn?.addEventListener("click", (e) => {
        e.stopPropagation();
        this.closeFileTab(filePath);
      });

      fileTabsContainer.appendChild(fileTab);
    });
  }

  private selectFileTab(filePath: string) {
    // Update active tab
    document.querySelectorAll(".file-tab").forEach((tab) => {
      tab.classList.remove("active");
    });

    // Find and activate the clicked tab
    const tabs = document.querySelectorAll(".file-tab");
    tabs.forEach((tab) => {
      const fileName = filePath.split("/").pop();
      if (tab.textContent?.includes(fileName || "")) {
        tab.classList.add("active");
      }
    });
  }

  private closeFileTab(filePath: string) {
    // Request to close file in VS Code
    this.vscode.postMessage({
      command: "closeFile",
      filePath: filePath,
    });
  }

  private updateActiveFileTab(fileName: string, filePath: string) {
    const fileTabsContainer = document.getElementById("file-tabs");
    if (!fileTabsContainer) return;

    // Clear all existing tabs and show only the current active file
    fileTabsContainer.innerHTML = "";

    // Create the active file tab
    const fileTab = document.createElement("div");
    fileTab.className = "file-tab active";
    fileTab.innerHTML = `
      <span>${fileName}</span>
      <span class="close-btn">×</span>
    `;

    fileTab.addEventListener("click", (e) => {
      if (!(e.target as HTMLElement).classList.contains("close-btn")) {
        this.selectFileTab(filePath);
      }
    });

    const closeBtn = fileTab.querySelector(".close-btn");
    closeBtn?.addEventListener("click", (e) => {
      e.stopPropagation();
      this.closeFileTab(filePath);
    });

    fileTabsContainer.appendChild(fileTab);
  }

  private handleMessage(message: any) {
    console.log("[DEBUG] handleMessage received:", message.command, message);

    // Helper to log UI reset triggers
    const logUIReset = (reason: string) => {
      console.warn(
        `[DEBUG] UI RESET TRIGGERED: ${reason} (message: ${message.command})`,
        message
      );
      console.trace();
    };

    switch (message.command) {
      case "initialState":
        logUIReset("handleInitialState");
        this.handleInitialState(message.isIndexed);
        break;
      case "restoreState":
        logUIReset("handleRestoreState");
        this.handleRestoreState(message.state);
        break;
      case "restoreCompleteState":
        console.log("[DEBUG] restoreCompleteState command received");
        this.handleRestoreCompleteState(message.restoreData);
        break;
      case "workspaceChanged":
        logUIReset("workspaceChanged");
        this.clearAllState();
        // Don't immediately show indexing screen - let the provider check server status
        console.log(
          "🔄 Workspace changed - provider will check indexing status"
        );
        break;
      case "clearAllState":
        logUIReset("clearAllState");
        this.clearAllState();
        break;
      case "showIndexingScreen":
        logUIReset("showIndexingScreen");
        this.showIndexingScreen();
        break;
      case "showChatInterface":
        console.log(
          "[DEBUG] showChatInterface command received - switching to chat"
        );
        // Set indexed state to true when showing chat interface
        this.isIndexed = true;
        console.log("[DEBUG] Set isIndexed = true for chat interface");
        this.showChatInterface();
        break;
      case "settingsLoaded":
        this.populateSettings(message.settings);
        break;
      case "connectionResult":
        if (message.success) {
          this.vscode.postMessage({
            command: "error",
            text: message.message,
          });
        }
        break;
      case "newConversation":
        console.log("🆕 New conversation command received");
        // The UI clearing is already handled in startNewConversation()
        break;
      case "indexingComplete":
        this.isIndexed = true;
        this.showIndexingComplete();
        break;
      case "indexingError":
        this.handleIndexingError(message.error);
        break;
      case "aiResponse":
        // Check if this is a final completion response
        if (
          message.response &&
          message.response.includes("Coder agent completed")
        ) {
          this.removeThinkingMessage();
          this.setWaitingState(false);
          // Only show success if no error was shown
          if (!this.errorShownForCurrentRequest) {
            this.addMessageToChat("assistant", message.response);
          }
          // Reset error flag for next request
          this.errorShownForCurrentRequest = false;
        } else if (message.response && this.isErrorMessage(message.response)) {
          this.addErrorMessage(message.response);
        } else {
          this.addMessageToChat("assistant", message.response);
        }
        break;
      case "projectSummary":
        console.log("📋 Received projectSummary message:", message);
        // Don't remove thinking message for project summary - it's not the final response
        // Skip action indicators for summary messages to prevent styling issues
        this.addMessageToChat("assistant", message.summary, true, true);
        break;
      case "consoleOutput":
        // Stream console output from WebSocket
        this.addConsoleOutput(
          message.message,
          message.level,
          message.heading,
          message
        );

        // Check if this is the final completion message
        if (
          message.message &&
          message.message.includes("Coder processing completed!")
        ) {
          this.removeThinkingMessage();
          this.setWaitingState(false);
        }
        break;
      case "agentStatus":
        // Update agent status
        this.updateAgentStatus(
          message.status,
          message.details,
          message.animation
        );
        break;
      case "queueStatus":
        // Handle queue status messages
        this.handleQueueStatus(
          message.status,
          message.position,
          message.message
        );
        break;
      case "agentReasoning":
        // Display LLM reasoning professionally
        this.displayAgentReasoning(message.data);
        break;
      case "coderResponse":
        // Final coder response - this means the request is complete
        this.removeThinkingMessage();
        this.setWaitingState(false);

        // Only show summary if there's no error and it's a successful response
        if (
          (message.summary || message.message) &&
          message.status !== "error"
        ) {
          const content = message.summary || message.message;

          console.log("📋 Received coderResponse:", {
            status: message.status,
            hasContent: !!content,
            contentPreview: content?.substring(0, 100),
          });

          // Check if the content contains actual error indicators (not just the word "failed" in context)
          // Be more specific to avoid false positives when describing analysis results
          const isError =
            content.toLowerCase().includes("error generating") ||
            content.toLowerCase().includes("tool execution failed") ||
            content.toLowerCase().includes("no yaml block found") ||
            content.toLowerCase().includes("failed to parse") ||
            content.toLowerCase().includes("server error") ||
            content.toLowerCase().includes("connection failed") ||
            // Only treat as error if it's an actual compilation failure, not analysis
            (content.toLowerCase().includes("compilation error encountered") &&
              !content.toLowerCase().includes("analysis") &&
              !content.toLowerCase().includes("investigation") &&
              !content.toLowerCase().includes("revealed") &&
              !content.toLowerCase().includes("found that"));

          if (!isError && !this.errorShownForCurrentRequest) {
            console.log("📋 Adding summary message to UI");
            // Add a professional summary message instead of raw markdown
            this.addSummaryMessage(content);
          } else if (isError) {
            console.log(
              "📋 Detected error in content, showing as error message"
            );
            // Display as error message instead
            this.addErrorMessage(content);
          }
          // Reset error flag for next request
          this.errorShownForCurrentRequest = false;
        } else {
          console.log("📋 Not showing summary:", {
            hasContent: !!(message.summary || message.message),
            status: message.status,
            errorShown: this.errorShownForCurrentRequest,
          });
        }
        break;
      case "workspaceName":
        console.log("📁 Received workspace name:", message.name);
        this.updateWorkspaceName(message.name);
        break;
      case "openFiles":
        this.displayFileTabs(message.files);
        break;
      case "updateActiveFile":
        this.updateActiveFileTab(message.fileName, message.filePath);
        break;
      case "showCodeApproval":
        // Mark that code operation is about to start
        this.codeOperationInProgress = true;
        this.preserveScrollDuringOperation = true;
        console.log(
          "🔄 Code operation starting - enabling aggressive scroll protection"
        );
        this.showCodeApprovalUI(message.data);
        break;
      case "task_list":
        this.addTaskListMessage(message.data);
        break;
      case "execution_plan":
        this.addExecutionPlanMessage(message.data);
        break;
      case "tool_status":
        if (message.data && message.data.tool_name) {
          this.toolStatus[message.data.tool_name] = message.data.status;
          this.updateActionStatusForTool(
            message.data.tool_name,
            message.data.status,
            message.data.message
          );

          // Handle terminal command completion
          if (
            message.data.tool_name === "terminal_command" ||
            message.data.tool_name === "create_directory_terminal" ||
            message.data.tool_name === "file_operations" ||
            message.data.status === "success" ||
            message.data.status === "error"
          ) {
            this.handleTerminalCommandCompletion(message.data);
          }
        }
        break;

      case "tasks_identified":
        this.displayTasksIdentified(message.data);
        break;

      case "execution_plan_ready":
        this.displayExecutionPlan(message.data);
        break;

      case "file_operation_start":
        const chatMessages1 = document.getElementById("chat-messages");
        const scrollPosA = chatMessages1 ? chatMessages1.scrollTop : 0;
        console.log(
          `🎯 [${new Date().toISOString()}] RECEIVED: file_operation_start for ${
            message.filePath
          } (scroll: ${scrollPosA})`
        );
        // Lock scroll position during file operations to prevent resets
        setTimeout(() => {
          const scrollPosB = chatMessages1 ? chatMessages1.scrollTop : 0;
          console.log(
            `🎯 [${new Date().toISOString()}] About to activate scroll lock from file_operation_start (scroll: ${scrollPosB})`
          );
          this.activateScrollLock();

          // File operation display removed - already included in summary
          // setTimeout(() => {
          //   const scrollPosC = chatMessages1 ? chatMessages1.scrollTop : 0;
          //   console.log(
          //     `🎯 [${new Date().toISOString()}] About to show file operation (scroll: ${scrollPosC})`
          //   );
          //   this.showFileOperation(
          //     message.operation,
          //     message.filePath,
          //     "in-progress"
          //   );

          //   setTimeout(() => {
          //     const scrollPosD = chatMessages1 ? chatMessages1.scrollTop : 0;
          //     console.log(
          //       `🎯 [${new Date().toISOString()}] File operation display completed (scroll: ${scrollPosD})`
          //     );
          //   }, 5000); // 5 second delay
          // }, 5000);
        }, 5000);
        break;

      case "file_operation_complete":
        this.updateFileOperationStatus(
          message.filePath,
          message.success ? "success" : "error"
        );

        // Also update any file operation buttons for this file
        this.updateFileOperationButtonsByPath(
          message.filePath,
          message.success ? "success" : "error"
        );

        // FALLBACK: Update all executing buttons when any operation completes
        this.updateAllExecutingButtons(message.success ? "success" : "error");
        break;
      case "syncComplete":
        console.log("🔄 Sync complete - webview is up to date");
        // Ensure UI is properly updated after sync
        this.scrollToBottom();
        break;
      case "showUserActionPrompt":
        const chatMessages2 = document.getElementById("chat-messages");
        const scrollPosW = chatMessages2 ? chatMessages2.scrollTop : 0;
        console.log(
          `🎯 [${new Date().toISOString()}] RECEIVED: showUserActionPrompt (scroll: ${scrollPosW})`
        );
        // Mark that code operation is complete and start aggressive scroll protection
        this.codeOperationInProgress = false;
        this.preserveScrollDuringOperation = false;

        // Deactivate scroll lock after a short delay to allow user natural scrolling
        setTimeout(() => {
          console.log(
            "🔓 Deactivating scroll lock - operations complete, allowing natural user scrolling"
          );
          this.deactivateScrollLock();
        }, 3000); // 3 seconds should be enough to prevent race conditions

        setTimeout(() => {
          const scrollPosX = chatMessages2 ? chatMessages2.scrollTop : 0;
          console.log(
            `🎯 [${new Date().toISOString()}] Code operation complete - starting post-operation scroll protection (scroll: ${scrollPosX})`
          );
          this.startPostOperationScrollProtection();

          setTimeout(() => {
            const scrollPosY = chatMessages2 ? chatMessages2.scrollTop : 0;
            console.log(
              `🎯 [${new Date().toISOString()}] About to show user action prompt (scroll: ${scrollPosY})`
            );
            this.showUserActionPrompt(message.data);

            setTimeout(() => {
              const scrollPosZ = chatMessages2 ? chatMessages2.scrollTop : 0;
              console.log(
                `🎯 [${new Date().toISOString()}] User action prompt display completed (scroll: ${scrollPosZ})`
              );
            }, 5000); // 5 second delay to see final state
          }, 5000);
        }, 5000);
        break;
      case "code_apply_response":
        // Handle code application response (success/error/cancelled)
        console.log("✅ Code application response received:", message.data);
        if (message.data.status === "success") {
          console.log(`✅ ${message.data.message}`);
        } else if (message.data.status === "error") {
          console.error(`❌ ${message.data.message}`);
        } else if (message.data.status === "cancelled") {
          console.log(`🚫 ${message.data.message}`);
        }
        break;
      case "error":
        // Make error handling non-destructive - NO UI reset
        console.error("❌ Error received from extension:", message.message);
        this.showErrorWithRetry(message.message);
        // Mark that an error was shown for this request
        this.errorShownForCurrentRequest = true;
        break;
      default:
        // Log all other messages for traceability
        console.log("[DEBUG] handleMessage: no UI reset for", message.command);
    }
  }

  private showErrorWithRetry(errorMsg: string) {
    const chatMessages = document.getElementById("chat-messages");
    if (chatMessages) {
      const errorDiv = document.createElement("div");
      errorDiv.className = "message assistant error-message";
      errorDiv.innerHTML = `
        <div class="error-content">
          <div class="error-text">Server Error: ${errorMsg}</div>
          <button class="retry-btn" id="retry-btn">Retry</button>
        </div>
      `;
      chatMessages.appendChild(errorDiv);
      this.scrollToBottom();
      document.getElementById("retry-btn")?.addEventListener("click", () => {
        this.retryLastRequest();
      });
    }
  }

  private retryLastRequest() {
    if (this.lastUserMessage) {
      this.sendMessage(this.lastUserMessage);
    } else {
      // Optionally show a message: nothing to retry
      this.showErrorWithRetry("No previous request to retry.");
    }
  }

  private showUserActionPrompt(data: any) {
    if (this.isAgentMode()) {
      // Auto-run all terminal commands
      if (data.commands && Array.isArray(data.commands)) {
        data.commands.forEach((cmd: any) => {
          if (cmd.command) {
            this.vscode.postMessage({
              command: "executeTerminalCommand",
              terminalCommand: cmd.command,
            });
          }
        });
      }
      // Optionally show a status message
      this.addMessageToChat(
        "assistant",
        "🤖 Agent mode: All terminal commands auto-executed."
      );
      this.scrollToBottom();
      return;
    }
    console.log("🔧 Showing user action prompt in webview:", data);

    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    const messageDiv = document.createElement("div");
    messageDiv.className = "message assistant";

    // Show compilation result first
    let compilationStatus = "";
    if (data.compilationResult) {
      const result = data.compilationResult;
      if (result.success) {
        if (result.warnings && result.warnings.length > 0) {
          compilationStatus = `<div class="compilation-status warning">⚠️ Compilation successful with ${result.warnings.length} warnings (${result.language})</div>`;
        } else {
          compilationStatus = `<div class="compilation-status success">✅ Compilation successful (${result.language})</div>`;
        }
      } else {
        compilationStatus = `<div class="compilation-status error">❌ Compilation failed with ${result.errors.length} errors (${result.language})</div>`;
      }
    }

    // Create terminal commands section
    const commandsHtml = data.commands
      .map(
        (cmd: any, index: number) => `
      <div class="terminal-command-item">
        <div class="command-description">${cmd.description}</div>
        <div class="command-detail">${cmd.command}</div>
        <button class="action-play-button terminal-command-btn" data-command="${cmd.command}">
          ▶️ Run
        </button>
      </div>
    `
      )
      .join("");

    messageDiv.innerHTML = `
      <div class="user-action-prompt">
        ${compilationStatus}
        <div class="action-message">${data.message}</div>
        <div class="terminal-commands">
          ${commandsHtml}
        </div>
      </div>
    `;

    chatMessages.appendChild(messageDiv);

    // Add event listeners for terminal command buttons
    const terminalButtons = messageDiv.querySelectorAll(
      ".terminal-command-btn"
    );
    terminalButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const command = (e.target as HTMLElement).getAttribute("data-command");
        if (command) {
          console.log(`🔧 Executing terminal command: ${command}`);
          this.vscode.postMessage({
            command: "executeTerminalCommand",
            terminalCommand: command,
          });
        }
      });
    });

    this.scrollToBottom();
  }

  private handleInitialState(isIndexed: boolean) {
    console.warn(
      `🚨 WEBVIEW: handleInitialState called with isIndexed=${isIndexed} - THIS MIGHT RESET UI!`
    );
    console.trace("🚨 WEBVIEW: Stack trace for handleInitialState call");
    console.log(
      `🔄 WEBVIEW: handleInitialState called with isIndexed=${isIndexed}`
    );
    this.isIndexed = isIndexed;

    if (isIndexed) {
      // Show chat interface directly
      console.log("🔄 WEBVIEW: Showing chat interface (indexed=true)");
      this.showChatInterface();
    } else {
      // Show indexing screen
      console.log("🔄 WEBVIEW: Showing indexing screen (indexed=false)");
      this.showIndexingScreen();
    }
  }

  private handleRestoreState(state: any) {
    console.warn(
      "🚨 WEBVIEW: handleRestoreState called - THIS MIGHT RESET UI!"
    );
    console.trace("🚨 WEBVIEW: Stack trace for handleRestoreState call");
    console.log("🔄 Restoring persisted state:", state);
    console.log("🔄 State details:", {
      isIndexed: state.isIndexed,
      chatHistoryLength: state.chatHistory?.length || 0,
      currentMode: state.currentMode,
      hasMessages: state.chatHistory && state.chatHistory.length > 0,
    });

    // Restore basic state
    this.isIndexed = state.isIndexed || false;
    console.log("🔄 Set isIndexed to:", this.isIndexed);

    // Restore chat mode
    if (state.chatMode) {
      this.chatMode = state.chatMode;
      console.log(`🔄 Restored chat mode to: ${this.chatMode}`);

      // Update the dropdown if it exists
      const chatModeDropdown = document.getElementById(
        "chat-mode-dropdown"
      ) as HTMLSelectElement;
      if (chatModeDropdown) {
        chatModeDropdown.value = this.chatMode;
      }
    }

    // Restore chat history intelligently (append missing messages instead of clearing)
    if (state.chatHistory && state.chatHistory.length > 0) {
      console.log("🔄 Intelligently restoring chat history...");
      const chatContainer = document.getElementById("chat-messages");
      if (chatContainer) {
        // Get current messages in the chat
        const currentMessages = Array.from(
          chatContainer.querySelectorAll(".message:not(.thinking)")
        );
        const currentMessageCount = currentMessages.length;

        console.log(
          `🔄 Current messages in chat: ${currentMessageCount}, State messages: ${state.chatHistory.length}`
        );

        // Only add messages that are missing (if state has more messages than current UI)
        if (state.chatHistory.length > currentMessageCount) {
          const missingMessages = state.chatHistory.slice(currentMessageCount);
          console.log(`🔄 Adding ${missingMessages.length} missing messages`);

          missingMessages.forEach((message: any, index: number) => {
            console.log(
              `🔄 Adding missing message ${currentMessageCount + index + 1}:`,
              message
            );
            this.addMessageToChat(
              message.isUser ? "user" : "assistant",
              message.content,
              false // Don't persist during restoration
            );
          });

          console.log(
            `✅ Added ${missingMessages.length} missing chat messages`
          );
        } else {
          console.log("🔄 No missing messages to restore - chat is up to date");
        }
      } else {
        console.error("❌ Could not find chat-messages container");
      }
    }

    // Restore tool statuses
    if (state.toolStatuses && Object.keys(state.toolStatuses).length > 0) {
      console.log("🔄 Restoring tool statuses...");
      Object.entries(state.toolStatuses).forEach(
        ([toolName, status]: [string, any]) => {
          console.log(
            `🔄 Restoring tool status: ${toolName} -> ${status.status}`
          );
          this.updateToolStatus({
            tool_name: toolName,
            status: status.status,
            message: status.message,
            timestamp: status.timestamp,
          });
        }
      );
      console.log(
        `✅ Restored ${Object.keys(state.toolStatuses).length} tool statuses`
      );
    }

    // Restore processing messages
    if (state.processingMessages && state.processingMessages.length > 0) {
      console.log("🔄 Restoring processing messages...");
      state.processingMessages.forEach((msg: any, index: number) => {
        console.log(
          `🔄 Restoring processing message ${index + 1}:`,
          msg.content
        );
        // Add processing messages as assistant messages with special styling
        this.addMessageToChat("assistant", `🔄 ${msg.content}`, false);
      });
      console.log(
        `✅ Restored ${state.processingMessages.length} processing messages`
      );
    }

    // Restore task list
    if (state.taskList) {
      console.log("🔄 Restoring task list...");
      this.addTaskListMessage(state.taskList);
      console.log("✅ Restored task list");
    }

    // Restore pending code approval prompt
    if (state.pendingCodeApproval) {
      console.log("🔄 Restoring pending code approval prompt...");
      const timeSincePrompt = Date.now() - state.pendingCodeApproval.timestamp;

      // Only restore if the prompt is less than 10 minutes old
      if (timeSincePrompt < 10 * 60 * 1000) {
        console.log("🔄 Code approval prompt is recent, restoring...");
        this.showCodeApprovalUI(state.pendingCodeApproval.data);
        console.log("✅ Restored pending code approval prompt");
      } else {
        console.log("🔄 Code approval prompt is too old, skipping restoration");
        // Clear the old pending approval
        this.vscode.postMessage({
          command: "clearPendingCodeApproval",
        });
      }
    }

    // Update UI based on current mode and state
    console.log("🔄 Determining UI state...");
    if (state.isIndexed) {
      console.log("🔄 WEBVIEW: State is indexed, showing chat interface");
      this.showChatInterface();
    } else {
      console.log(
        "🔄 WEBVIEW: State is not indexed, showing indexing screen - THIS CAUSES UI RESET"
      );
      this.showIndexingScreen();
    }

    console.log("✅ State restoration complete");
  }

  private handleRestoreCompleteState(restoreData: any) {
    console.log("🔄 WEBVIEW: handleRestoreCompleteState called");
    console.log("🔄 WEBVIEW: Restore data:", {
      chatHistory: restoreData.chatHistory?.length || 0,
      currentScreen: restoreData.currentScreen,
      sessionId: restoreData.sessionId,
    });

    // Set indexed state to true when restoring chat state
    this.isIndexed = true;
    console.log("🔄 WEBVIEW: Set isIndexed = true for state restoration");

    // First ensure we're showing the chat interface
    this.showChatInterface();

    // Restore chat messages
    if (restoreData.chatHistory && restoreData.chatHistory.length > 0) {
      console.log(
        `🔄 WEBVIEW: Restoring ${restoreData.chatHistory.length} chat messages`
      );

      // Clear existing messages first and wait for DOM to update
      const chatMessages = document.getElementById("chat-messages");
      if (chatMessages) {
        chatMessages.innerHTML = "";

        // Force DOM update before adding messages
        setTimeout(() => {
          console.log(
            "🔄 WEBVIEW: Starting message restoration after DOM clear"
          );

          // Restore each message with proper timing for styling
          restoreData.chatHistory.forEach((message: any, index: number) => {
            console.log(
              `🔄 WEBVIEW: Restoring message ${index + 1}:`,
              message.isUser ? "user" : "assistant",
              message.content.substring(0, 50) + "..."
            );

            // Add delay to ensure proper styling application
            setTimeout(() => {
              console.log(
                `🔄 WEBVIEW: Actually adding message ${index + 1} to DOM`
              );

              // Restore message with original formatting if available
              if (message.originalFormatting && message.messageType) {
                this.restoreMessageWithFormatting(message);
              } else {
                // Fallback to generic message restoration
                this.addMessageToChat(
                  message.isUser ? "user" : "assistant",
                  message.content,
                  false // Don't persist during restoration
                );
              }

              // Log after adding to check if it worked
              setTimeout(() => {
                const messageElements =
                  chatMessages.querySelectorAll(".message");
                console.log(
                  `🔄 WEBVIEW: DOM now has ${messageElements.length} message elements`
                );
              }, 5);
            }, index * 50); // Increased delay to 50ms between messages
          });

          // Restore scroll position after all messages are added
          const totalMessages = restoreData.chatHistory.length;
          setTimeout(() => {
            if (restoreData.scrollPosition && chatMessages) {
              chatMessages.scrollTop = restoreData.scrollPosition;
              console.log("🔄 WEBVIEW: Restored scroll position");
            } else {
              // Scroll to bottom if no saved position
              chatMessages.scrollTop = chatMessages.scrollHeight;
              console.log("🔄 WEBVIEW: Scrolled to bottom");
            }
          }, totalMessages * 50 + 100);
        }, 50); // Wait 50ms after clearing before starting restoration
      }
    }

    // Restore other UI state
    if (restoreData.inputValue) {
      const messageInput = document.getElementById(
        "message-input"
      ) as HTMLTextAreaElement;
      if (messageInput) {
        messageInput.value = restoreData.inputValue;
      }
    }

    console.log("✅ WEBVIEW: Complete state restoration finished");
  }

  private restoreMessageWithFormatting(message: any) {
    console.log(`🎨 Restoring message with ${message.messageType} formatting`);

    switch (message.messageType) {
      case "summary":
        // Restore summary message with original styling
        this.addSummaryMessageDirect(message.content);
        break;
      case "error":
        // Restore error message with original styling
        this.addErrorMessageDirect(message.content);
        break;
      case "console":
        // Restore console output with original styling
        this.addConsoleOutputDirect(
          message.content,
          message.level || "info",
          message.heading
        );
        break;
      case "thinking":
        // Restore thinking message with original styling
        this.addThinkingMessageDirect();
        break;
      case "tasks_identified":
        // Restore tasks identified with original styling
        console.log("🔍 Restoring tasks_identified, message:", message);
        console.log("🔍 tasksData:", message.tasksData);
        if (message.tasksData) {
          console.log("🔍 tasksData.tasks:", message.tasksData.tasks);
          this.displayTasksIdentifiedDirect(message.tasksData);
        } else {
          console.log("❌ No tasksData found in message");
        }
        break;
      case "task_list":
        // Restore task list with original styling
        if (message.taskListData) {
          this.addTaskListMessageDirect(message.taskListData);
        }
        break;
      default:
        // Fallback to generic message
        this.addMessageToChat(
          message.isUser ? "user" : "assistant",
          message.content,
          false
        );
        break;
    }
  }

  private addSummaryMessageDirect(content: string) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Create summary message with original styling (no persistence during restoration)
    const summaryDiv = document.createElement("div");
    summaryDiv.className = "message assistant status-message";

    const cleanContent = this.formatPlainTextContent(content);

    summaryDiv.innerHTML = `
      <div class="action-heading-h2">📋 What I have done is</div>
      <div class="summary-details-clean">${cleanContent}</div>
    `;

    chatMessages.appendChild(summaryDiv);
    this.scrollToBottom();
  }

  private addErrorMessageDirect(errorMsg: string) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Create error message with original styling (no persistence during restoration)
    const errorDiv = document.createElement("div");
    errorDiv.className = "message assistant error-message";

    errorDiv.innerHTML = `
      <div class="error-content">
        <div class="error-text">${errorMsg}</div>
      </div>
    `;

    chatMessages.appendChild(errorDiv);
    this.scrollToBottom();
  }

  private addConsoleOutputDirect(
    message: string,
    level: string,
    heading?: string
  ) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Create console output with original styling (no persistence during restoration)
    let contentHtml = "";
    let extraClass = "";

    if (heading === "h2") {
      const match =
        typeof message === "string"
          ? message.match(/^([0-9]+):\s*(.*)$/)
          : null;
      if (match) {
        const [, stepNumber, stepText] = match;
        contentHtml = `
          <div class="action-status">
            <div class="action-status-header">
              <div class="action-status-title">${stepText}</div>
            </div>
          </div>
        `;
        extraClass = " action-status-container";
      }
    } else if (
      typeof message === "string" &&
      /understanding structure/i.test(message)
    ) {
      // Handle "Understanding structure" messages with proper action indicator styling (restoration)
      let displayMessage = message;
      let fileInfo = "";

      // Extract filename from "Understanding structure of filename" pattern
      const fileMatch = message.match(
        /understanding\s+structure\s+of\s+(.+?)(?:\s+\(|$)/i
      );
      if (fileMatch && fileMatch[1]) {
        const fileName = fileMatch[1].trim();
        console.log(`🏗️ Restored file name for structure: "${fileName}"`);

        // Extract just the file name if it's a path
        const justFileName = fileName.split("/").pop() || fileName;
        displayMessage = `🏗️ Understanding structure of ${justFileName}`;
        fileInfo = `<div class="action-file-path">${fileName}</div>`;
      }

      contentHtml = `
        <div class="action-status">
          <div class="action-status-header">
            <div class="action-status-title">${displayMessage}</div>
          </div>
          ${fileInfo}
        </div>
      `;
      extraClass = " action-status-container";
    } else if (heading === "h3") {
      contentHtml = `<h3 style="font-family: inherit; font-size: 1.1em; font-weight: 600; margin: 12px 0 8px 0;">${message}</h3>`;
    } else {
      contentHtml = `<div class="status-text">${message}</div>`;
    }

    const statusDiv = document.createElement("div");
    statusDiv.className = `status-message status-${level}${extraClass}`;
    statusDiv.innerHTML = contentHtml;
    chatMessages.appendChild(statusDiv);
    this.scrollToBottom();
  }

  private addThinkingMessageDirect() {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Create thinking message with original styling (no persistence during restoration)
    const messageDiv = document.createElement("div");
    messageDiv.className = "message thinking";
    messageDiv.id = "thinking-message";

    messageDiv.innerHTML = `
      <div class="message-content">
        <div class="thinking-content">
          <span>Thinking</span>
          <span class="animated-dots">
            <span class="dot">.</span>
            <span class="dot">.</span>
            <span class="dot">.</span>
          </span>
        </div>
      </div>
    `;

    chatMessages.appendChild(messageDiv);
    this.scrollToBottom();
  }

  private displayTasksIdentifiedDirect(data: any) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    console.log("🔍 displayTasksIdentifiedDirect called with data:", data);
    console.log("🔍 data.tasks:", data.tasks);

    // Create tasks identified with original styling (no persistence during restoration)
    const tasksDiv = document.createElement("div");
    tasksDiv.className = "message assistant tasks-identified";

    const tasks = data.tasks || [];
    const phase = data.phase || "analysis";

    console.log("🔍 tasks array:", tasks);
    console.log("🔍 tasks.length:", tasks.length);

    tasksDiv.innerHTML = `
      <div class="tasks-header-collapsible">
        <div class="tasks-title-row">
          <h4>Tasks Identified</h4>
          <span class="tasks-count">${tasks.length} tasks</span>
          <span class="collapse-indicator">▼</span>
        </div>
      </div>
      <div class="tasks-list collapsed">
        ${
          tasks.length > 0
            ? tasks
                .map(
                  (task: any, index: number) => `
              <div class="task-item-clean">
                <div class="task-number">${task.task_id || index + 1}</div>
                <div class="task-content">
                  <div class="task-description-clean">${
                    task.description || "No description available"
                  }</div>
                  ${
                    task.dependencies && task.dependencies.length > 0
                      ? `<div class="task-dependencies">Dependencies: ${task.dependencies.join(
                          ", "
                        )}</div>`
                      : ""
                  }
                </div>
              </div>
            `
                )
                .join("")
            : '<div class="no-tasks-message">No tasks identified yet</div>'
        }
      </div>
    `;

    chatMessages.appendChild(tasksDiv);

    // Attach event listener for collapsible dropdown
    const header = tasksDiv.querySelector(".tasks-header-collapsible");
    const list = tasksDiv.querySelector(".tasks-list");
    if (header && list) {
      header.addEventListener("click", () => {
        list.classList.toggle("collapsed");
      });
    }
    this.scrollToBottom();
  }

  private addTaskListMessageDirect(data: any) {
    const chatMessages = document.getElementById("chat-messages");
    if (!chatMessages) return;

    // Create task list with original styling (no persistence during restoration)
    const taskListDiv = document.createElement("div");
    taskListDiv.className = "message assistant task-list-message";

    const taskListId = `task-list-${Date.now()}`;

    taskListDiv.innerHTML = `
      <div class="task-list-header">
        <div class="status-content">
          <div class="status-icon">📝</div>
          <div class="status-text">${data.message}</div>
          <div class="collapse-icon">▶</div>
        </div>
      </div>
      <div class="task-list-content collapsed" id="${taskListId}">
        ${data.tasks
          .map((task: string, index: number) => {
            // Use a predictable tool name based on task order
            const toolNames = [
              "dir_tool",
              "file_read_tool",
              "semantic_search_tool",
              "code_expert_tool",
              "code_applier_tool",
              "summary_tool",
            ];
            const toolName = toolNames[index] || `task_${index}`;

            return `
          <div class="task-item">
            <span class="task-number">${index + 1}.</span>
            <span class="task-text">${task}</span>
            <span class="tool-status-indicator" id="tool-status-${toolName}" style="margin-left: auto; font-size: 14px; padding: 2px 4px; border-radius: 3px; background: rgba(255,255,255,0.1);">⏳</span>
          </div>
        `;
          })
          .join("")}
      </div>
    `;

    chatMessages.appendChild(taskListDiv);

    // Add event listener for collapsible functionality
    const header = taskListDiv.querySelector(".task-list-header");
    const content = taskListDiv.querySelector(".task-list-content");
    const collapseIcon = taskListDiv.querySelector(".collapse-icon");

    if (header && content && collapseIcon) {
      header.addEventListener("click", () => {
        content.classList.toggle("collapsed");
        collapseIcon.textContent = content.classList.contains("collapsed")
          ? "▶"
          : "▼";
      });
    }

    this.scrollToBottom();
  }

  private showChatInterface() {
    console.log("🎯 showChatInterface() called");
    const indexingScreen = document.getElementById("indexing-screen");
    const chatSection = document.getElementById("chat-section");

    console.log("🎯 Elements found:", {
      indexingScreen: !!indexingScreen,
      chatSection: !!chatSection,
    });

    indexingScreen?.classList.add("hidden");
    chatSection?.classList.remove("hidden");

    console.log("🎯 Chat interface should now be visible");

    // Load open files and workspace name
    this.loadOpenFiles();
    this.loadWorkspaceName();
  }

  private showIndexingScreen() {
    console.warn(
      "🚨 WEBVIEW: showIndexingScreen() called - THIS WILL RESET UI TO INITIAL STATE!"
    );
    console.trace("🚨 WEBVIEW: Stack trace for showIndexingScreen call");
    console.log("🎯 showIndexingScreen() called");
    const indexingScreen = document.getElementById("indexing-screen");
    const chatSection = document.getElementById("chat-section");

    console.log("🎯 Elements found:", {
      indexingScreen: !!indexingScreen,
      chatSection: !!chatSection,
    });

    indexingScreen?.classList.remove("hidden");
    chatSection?.classList.add("hidden");

    console.log("🎯 Indexing screen should now be visible");

    // Load workspace name for indexing screen
    this.loadWorkspaceName();
  }

  private updateWorkspaceName(name: string) {
    console.log("📁 Updating workspace name to:", name);
    const workspaceNameElement = document.getElementById("workspace-name");
    if (workspaceNameElement) {
      console.log("📁 Found workspace-name element, updating text");
      workspaceNameElement.textContent = name;
    } else {
      console.error("❌ Could not find workspace-name element");
    }
  }

  private populateSettings(settings: any) {
    (document.getElementById("llm-provider") as HTMLSelectElement).value =
      settings.provider || "azure-openai";
    (document.getElementById("llm-model") as HTMLSelectElement).value =
      settings.model || "gpt-4";
    (document.getElementById("api-key") as HTMLInputElement).value =
      settings.apiKey || "";
  }

  private getFileOperationIcon(action: string): string {
    switch (action?.toLowerCase()) {
      case "create":
        return "📄";
      case "edit":
      case "modify":
        return "✏️";
      case "delete":
        return "🗑️";
      case "move":
        return "📁";
      case "copy":
        return "📋";
      default:
        return "📝";
    }
  }

  private getActionVerb(action: string): string {
    switch (action?.toLowerCase()) {
      case "create":
        return "Creating";
      case "edit":
      case "modify":
        return "Editing";
      case "delete":
        return "Deleting";
      case "move":
        return "Moving";
      case "copy":
        return "Copying";
      default:
        return "Modifying";
    }
  }

  private escapeHtml(text: string): string {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  private addCodeApprovalEventListeners(
    container: HTMLElement,
    operations: any[]
  ) {
    // Add event listeners for file operation play buttons
    const playButtons = container.querySelectorAll(
      ".action-play-button:not(.terminal-command-btn)"
    );
    playButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const target = e.target as HTMLElement;

        // Check for consolidated operations (multiple indices)
        const indices = target.getAttribute("data-operation-indices");
        const singleIndex = target.getAttribute("data-operation-index");

        if (indices) {
          // Handle consolidated operations (multiple operations on same file)
          const operationIndices = indices
            .split(",")
            .map((i) => parseInt(i.trim()));
          console.log(
            `📝 Executing consolidated file operations at indices: ${operationIndices.join(
              ", "
            )}`
          );

          // Get the operations for these indices
          const selectedOperations = operationIndices
            .map((idx) => operations[idx])
            .filter((op) => op);

          // Update button to show executing state
          this.updateFileOperationButtonStatus(target, "executing");

          // FALLBACK: Auto-update to success after 5 seconds if no completion message received
          setTimeout(() => {
            if (target.innerHTML.includes("⏳")) {
              console.log(
                "🔧 TIMEOUT FALLBACK: Updating consolidated file operation button to success after 5 seconds"
              );
              this.updateFileOperationButtonStatus(target, "success");
            }
          }, 5000);

          // Execute the consolidated operations
          this.handleCodeApproval(true, selectedOperations, undefined);
        } else if (singleIndex) {
          // Handle single operation (backward compatibility)
          const index = parseInt(singleIndex);
          console.log(`📝 Executing single file operation at index: ${index}`);

          // Update button to show executing state
          this.updateFileOperationButtonStatus(target, "executing");

          // FALLBACK: Auto-update to success after 5 seconds if no completion message received
          setTimeout(() => {
            if (target.innerHTML.includes("⏳")) {
              console.log(
                "🔧 TIMEOUT FALLBACK: Updating file operation button to success after 5 seconds"
              );
              this.updateFileOperationButtonStatus(target, "success");
            }
          }, 5000);

          // Execute single operation
          this.handleCodeApproval(true, [operations[index]], undefined);
        }
      });
    });

    // Add event listeners for terminal command buttons
    const terminalButtons = container.querySelectorAll(".terminal-command-btn");
    terminalButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const target = e.target as HTMLElement;
        const command = target.getAttribute("data-command");
        const opIndex = target.getAttribute("data-op-index");
        const cmdIndex = target.getAttribute("data-cmd-index");

        if (command && opIndex) {
          console.log(
            `⚡ Executing terminal command: ${command} (operation ${opIndex}, command ${cmdIndex})`
          );

          // Update button to show executing state
          this.updateTerminalCommandStatus(target, "executing");

          // FALLBACK: Auto-update to success after 5 seconds if no completion message received
          setTimeout(() => {
            if (target.innerHTML.includes("⏳")) {
              console.log(
                "🔧 TIMEOUT FALLBACK: Updating button to success after 5 seconds"
              );
              this.updateTerminalCommandStatus(target, "success");
            }
          }, 5000);

          this.vscode.postMessage({
            command: "executeTerminalCommand",
            terminalCommand: command,
            operationIndex: parseInt(opIndex),
            commandIndex: cmdIndex ? parseInt(cmdIndex) : undefined,
          });
        }
      });
    });

    // Add event listeners for file open links
    const openLinks = container.querySelectorAll(".file-open-link");
    openLinks.forEach((link) => {
      link.addEventListener("click", (e) => {
        const filePath = (e.target as HTMLElement).getAttribute(
          "data-file-path"
        );
        if (filePath) {
          console.log(`📝 Opening file in editor: ${filePath}`);
          this.vscode.postMessage({
            command: "openFile",
            filePath: filePath,
          });
        }
      });
    });
  }

  private showCodeApprovalUI(data: any) {
    const { operations, isAgentMode } = data;

    // Override local agent mode detection with server-provided flag if available
    const agentModeActive =
      isAgentMode !== undefined ? isAgentMode : this.isAgentMode();

    console.log(
      `🎯 showCodeApprovalUI: operations=${
        operations.length
      }, isAgentMode=${isAgentMode}, localAgentMode=${this.isAgentMode()}, agentModeActive=${agentModeActive}`
    );

    // Group operations by file and type
    const groupedOperations = this.groupOperationsByFile(operations);

    // Create operations list HTML with clean action-status styling
    let operationsHtml = "";

    groupedOperations.forEach((group) => {
      if (group.type === "terminal") {
        // Handle terminal operations (always individual)
        group.operations.forEach((op: any) => {
          // Handle terminal operations
          if (
            op.action === "terminal_command" ||
            op.action === "create_directory_terminal"
          ) {
            const commandsList =
              op.commands
                ?.map((cmd: string, cmdIndex: number) => {
                  return `
                    <div class="action-status terminal-command">
                      <div class="action-status-header">
                        <div class="action-status-content">
                          <div class="terminal-icon">💻</div>
                          <div class="action-text" title="${cmd}">${cmd}</div>
                        </div>
                        ${
                          agentModeActive
                            ? `<div class="agent-auto-apply" data-agent-status="applying" data-cmd="${cmd}" data-op-index="${op.originalIndex}" data-cmd-index="${cmdIndex}">🤖 Auto-applying...</div>`
                            : `<button class="action-play-button terminal-command-btn" data-command="${cmd}" data-op-index="${op.originalIndex}" data-cmd-index="${cmdIndex}" title="Execute command">▶️</button>`
                        }
                      </div>
                    </div>
                  `;
                })
                .join("") || "";

            operationsHtml += commandsList;
          }
        });
      } else {
        // Handle file operations (consolidate multiple ops on same file)
        operationsHtml += this.createFileOperationBlock(
          group as {
            type: "file";
            file?: string;
            operations: any[];
          },
          agentModeActive
        );
      }
    });

    // Create clean approval message HTML - just the operations
    const approvalHtml = `${operationsHtml}`;

    // Add to chat messages instead of showing as overlay
    const chatMessages = document.getElementById("chat-messages");
    if (chatMessages) {
      const messageDiv = document.createElement("div");
      messageDiv.innerHTML = approvalHtml;
      chatMessages.appendChild(messageDiv);

      // Add event listeners for play buttons and open links (only for human mode)
      if (!agentModeActive) {
        this.addCodeApprovalEventListeners(messageDiv, operations);
      }

      this.scrollToBottom();

      // Handle Agent mode auto-approval after showing the operations
      if (agentModeActive) {
        console.log(
          `🤖 Agent mode detected - will auto-apply ${operations.length} operations in 2 seconds`
        );
        // Show operations for 2 seconds, then auto-apply
        setTimeout(() => {
          console.log(`🤖 Agent mode: Auto-applying operations now`);
          this.handleCodeApproval(true, operations, messageDiv);

          // Update Agent mode status indicators to show success
          this.updateAgentModeStatusToSuccess(messageDiv);

          console.log(`🤖 Agent mode: Adding success message to chat`);
          this.addMessageToChat(
            "assistant",
            "🤖 Agent mode: Code changes auto-applied."
          );
          this.scrollToBottom();
        }, 2000);
        return;
      }
    }
  }

  /**
   * Group operations by file and type for consolidated display
   */
  private groupOperationsByFile(operations: any[]): Array<{
    type: "terminal" | "file";
    file?: string;
    operations: any[];
  }> {
    const groups: Array<{
      type: "terminal" | "file";
      file?: string;
      operations: any[];
    }> = [];

    // First, separate terminal and file operations
    const terminalOps: any[] = [];
    const fileOpsMap = new Map<string, any[]>();

    operations.forEach((op, index) => {
      // Add original index for reference
      op.originalIndex = index;

      if (
        op.action === "terminal_command" ||
        op.action === "create_directory_terminal"
      ) {
        terminalOps.push(op);
      } else {
        // File operation
        const filePath = op.file_path || op.file || "Unknown file";
        if (!fileOpsMap.has(filePath)) {
          fileOpsMap.set(filePath, []);
        }
        fileOpsMap.get(filePath)!.push(op);
      }
    });

    // Add terminal operations as individual groups
    terminalOps.forEach((op) => {
      groups.push({
        type: "terminal",
        operations: [op],
      });
    });

    // Add file operations grouped by file
    fileOpsMap.forEach((ops, filePath) => {
      groups.push({
        type: "file",
        file: filePath,
        operations: ops,
      });
    });

    return groups;
  }

  /**
   * Create a consolidated file operation block for multiple operations on the same file
   */
  private createFileOperationBlock(
    group: {
      type: "file";
      file?: string;
      operations: any[];
    },
    agentModeActive: boolean
  ): string {
    const filePath = group.file || "Unknown file";
    const operations = group.operations;

    // Extract filename for display
    let displayFileName = "";
    if (filePath.includes("/")) {
      displayFileName = filePath.split("/").pop() || filePath;
    } else {
      displayFileName = filePath;
    }

    // Determine if this is a single operation or multiple
    const isSingleOperation = operations.length === 1;
    const actionText = isSingleOperation
      ? `✏️ ${this.getActionVerb(operations[0].action)} ${displayFileName}`
      : `✏️ ${operations.length} edits to ${displayFileName}`;

    // Create consolidated code preview for all operations
    let codePreview = "";
    if (operations.some((op) => op.replacement || op.content)) {
      const previews = operations
        .filter((op) => op.replacement || op.content)
        .map((op) => {
          const content = op.replacement || op.content || "";
          const startLine = op.start_line || 1;
          const endLine = op.end_line || startLine;
          const lines = content.split("\n");

          return `
            <div class="operation-section">
              <div class="operation-header">Lines ${startLine}-${endLine}:</div>
              <div class="code-preview-content">
                ${lines
                  .map(
                    (line: string, lineIdx: number) =>
                      `<div class="code-line">
                        <span class="line-number">${startLine + lineIdx}</span>
                        <span class="line-content">${this.escapeHtml(
                          line
                        )}</span>
                      </div>`
                  )
                  .join("")}
              </div>
            </div>
          `;
        })
        .join("");

      codePreview = `
        <div class="code-preview-container">
          <div class="code-preview-header">Code Changes:</div>
          ${previews}
        </div>
      `;
    }

    // Create operation indices for the button (all operations in this group)
    const operationIndices = operations.map((op) => op.originalIndex).join(",");

    return `
      <div class="action-status code-operation">
        <div class="action-status-header">
          <div class="action-status-content">
            <span class="action-text">${actionText}</span>
          </div>
          <div class="operation-controls">
            ${
              agentModeActive
                ? `<div class="agent-auto-apply" data-agent-status="applying" data-operation-indices="${operationIndices}">🤖 Auto-applying...</div>`
                : `<button class="action-play-button" data-operation-indices="${operationIndices}" title="Apply changes">▶️</button>`
            }
            <span class="file-open-link" data-file-path="${filePath}" title="Open in editor">
              Open
            </span>
          </div>
        </div>
        ${codePreview}
      </div>
    `;
  }

  private handleCodeApproval(
    approved: boolean,
    operations: any[],
    messageDiv?: HTMLElement
  ) {
    const timestamp = new Date().toISOString();
    const chatMessages = document.getElementById("chat-messages");
    const scrollPos0 = chatMessages ? chatMessages.scrollTop : 0;
    console.log(
      `🎯 [${timestamp}] handleCodeApproval called - approved: ${approved}, messageDiv: ${
        messageDiv ? "exists" : "undefined"
      } (scroll: ${scrollPos0})`
    );

    // CRITICAL: Small delay to prevent race condition that causes scroll reset
    setTimeout(() => {
      if (approved) {
        this.activateScrollLock();

        // CRITICAL: Brief delay before sending message to prevent webview focus loss
        setTimeout(() => {
          console.log(
            `🎯 Sending code approval message with ${operations.length} operations`
          );
          console.log(`🎯 Operations details:`, operations);

          // Send response to extension with activity update combined
          this.vscode.postMessage({
            command: "codeApprovalResponse",
            approved: approved,
            operations: operations,
            updateActivity: true, // Flag to update activity in the same message
          });
        }, 100); // Minimal delay - just enough to prevent race condition
      }
    }, 100); // Minimal delay - just enough to prevent race condition
  }

  // Add this method to SidebarWebview
  private clearChatUI() {
    console.warn(
      "🚨 WEBVIEW: clearChatUI() called - THIS WILL CLEAR ALL CHAT MESSAGES!"
    );
    console.trace("🚨 WEBVIEW: Stack trace for clearChatUI call");
    console.log(
      "🔄 WEBVIEW: clearChatUI() called - CLEARING ALL CHAT MESSAGES"
    );

    // Clear chat messages
    const chatMessages = document.getElementById("chat-messages");
    if (chatMessages) {
      chatMessages.innerHTML = "";
    }
    // Clear file tabs
    const fileTabs = document.getElementById("file-tabs");
    if (fileTabs) {
      fileTabs.innerHTML = "";
    }
    // Optionally clear chat input
    const chatInput = document.getElementById(
      "chat-input"
    ) as HTMLTextAreaElement;
    if (chatInput) {
      chatInput.value = "";
    }
  }

  private clearAllState() {
    console.log("🧹 Clearing all webview state for fresh start");

    // Clear chat UI
    this.clearChatUI();

    // Reset all flags and state
    this.isIndexed = false;
    this.welcomeMessageShown = false;
    this.projectSummaryRequested = false;
    this.isWaitingForResponse = false;
    this.lastUserMessage = null;
    this.errorShownForCurrentRequest = false;
    this.lastMessageCount = 0;

    // Clear any timers
    if (this.forceScrollTimeout) {
      clearTimeout(this.forceScrollTimeout);
      this.forceScrollTimeout = undefined;
    }

    console.log("✅ All webview state cleared");
  }

  private isHumanInLoopMode() {
    return this.chatMode === "human";
  }
}

// Initialize the webview when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  const webview = new SidebarWebview();

  // Make webview instance globally available for executeFileOperation
  (window as any).webviewInstance = webview;

  // Add global window functions using type assertion
  (window as any).openFileInEditor = (filePath: string) => {
    console.log(`📝 Opening file in editor: ${filePath}`);
    // Send message to extension to open file
    const vscode = acquireVsCodeApi();
    vscode.postMessage({
      command: "openFile",
      filePath: filePath,
    });
  };

  (window as any).executeTerminalCommand = (
    command: string,
    opIndex: number,
    cmdIndex?: number
  ) => {
    console.log(
      `⚡ Executing terminal command: ${command} (operation ${opIndex}, command ${cmdIndex})`
    );
    // Send message to extension to execute terminal command
    const vscode = acquireVsCodeApi();
    vscode.postMessage({
      type: "executeTerminalCommand",
      command: command,
      operationIndex: opIndex,
      commandIndex: cmdIndex,
    });
  };

  (window as any).executeFileOperation = (index: number) => {
    const chatMessages = document.getElementById("chat-messages");
    const scrollPos = chatMessages ? chatMessages.scrollTop : 0;
    console.log(
      `📝 Executing file operation at index: ${index} (scroll: ${scrollPos})`
    );

    // CRITICAL FIX: Activate scroll lock IMMEDIATELY before sending message to extension
    // This prevents VSCode webview from resetting scroll position during message processing
    console.log(
      `🎯 [${new Date().toISOString()}] executeFileOperation: Activating scroll lock before sending message`
    );

    // Get the webview instance to call activateScrollLock
    const webviewInstance = (window as any).webviewInstance;
    if (webviewInstance && webviewInstance.activateScrollLock) {
      webviewInstance.activateScrollLock();
    }

    // For now, execute all operations when any play button is clicked
    // This mimics the "Approve & Apply All" behavior
    const vscode = acquireVsCodeApi();
    vscode.postMessage({
      command: "codeApprovalResponse",
      approved: true,
      operations: [], // Will be filled by the handler from pending state
    });

    const scrollPosAfter = chatMessages ? chatMessages.scrollTop : 0;
    console.log(
      `🎯 [${new Date().toISOString()}] executeFileOperation: Message sent (scroll: ${scrollPosAfter})`
    );
  };

  (window as any).approveAllTerminalCommands = () => {
    console.log(`✅ Approving all terminal commands`);
    // Implementation for approving all commands
  };

  (window as any).rejectTerminalCommands = () => {
    console.log(`❌ Rejecting terminal commands`);
    // Implementation for rejecting commands
  };

  // Demo function to show action indicators (for testing)
  (window as any).showDemoActions = () => {
    const demoActions = [
      {
        action: "Analyzing",
        file: "tasks.css",
        path: "ref/ReCodeAI/client/src/providers/css/tasks.css",
      },
      {
        action: "Reading file",
        file: "sidebarWebview.ts",
        path: "ref/ReCodeAI/client/src/sidebarWebview.ts",
      },
      {
        action: "Editing file",
        file: "MessageHandler.ts",
        path: "ref/ReCodeAI/client/src/providers/MessageHandler.ts",
      },
      {
        action: "Creating file",
        file: "newComponent.tsx",
        path: "src/components/newComponent.tsx",
      },
      {
        action: "Compiling",
        file: "Main.java",
        path: "src/main/java/com/example/Main.java",
      },
    ];

    demoActions.forEach((demo, index) => {
      setTimeout(() => {
        const actionId = webview.showActionIndicator(
          demo.action,
          demo.file,
          demo.path,
          "in-progress"
        );

        // Simulate completion after 2 seconds
        if (actionId) {
          setTimeout(() => {
            webview.updateActionStatus(
              actionId,
              Math.random() > 0.2 ? "success" : "error"
            );
          }, 2000);
        }
      }, index * 500);
    });
  };
});

// Export to make this a module
export {};
