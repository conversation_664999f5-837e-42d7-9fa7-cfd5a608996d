import * as vscode from "vscode";
import { getUri } from "../utilities/getUri";
import { getNonce } from "../utilities/getNonce";

export class HTMLTemplateManager {
  private _extensionUri: vscode.Uri;

  constructor(extensionUri: vscode.Uri) {
    this._extensionUri = extensionUri;
  }

  public getHtmlForWebview(webview: vscode.Webview): string {
    const scriptUri = getUri(webview, this._extensionUri, [
      "out",
      "sidebarWebview.js",
    ]);
    const nonce = getNonce();

    // Get CSS file URIs
    const cssFiles = [
      "base.css",
      "indexing.css",
      "chat.css",
      "markdown.css",
      "input.css",
      "animations.css",
      "status.css",
      "tasks.css",
      "code-approval.css",
    ];

    const cssLinks = cssFiles
      .map((cssFile) => {
        const cssUri = getUri(webview, this._extensionUri, [
          "src",
          "providers",
          "css",
          cssFile,
        ]);
        return `<link rel="stylesheet" href="${cssUri}">`;
      })
      .join("\n            ");

    return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; img-src ${webview.cspSource} https:; font-src ${webview.cspSource};">
            <title>AI Coding Assistant</title>
            ${cssLinks}
        </head>
        <body>
            <div class="settings-icon" id="settings-icon">⚙️</div>

            <!-- Settings Panel (hidden by default) -->
            <div class="settings-panel hidden" id="settings-panel">
                <div class="form-group">
                    <label for="llm-provider">LLM Provider</label>
                    <select id="llm-provider">
                        <option value="azure-openai">Azure OpenAI</option>
                        <option value="google-gemini">Google Gemini</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="llm-model">Model</label>
                    <select id="llm-model">
                        <option value="gpt-4">GPT-4</option>
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="api-key">API Key</label>
                    <input type="password" id="api-key" placeholder="Enter your API key">
                </div>
                <button class="btn btn-primary" id="save-settings">Save Settings</button>
                <button class="btn btn-primary" id="test-connection">Test Connection</button>
            </div>

            <!-- Initial Indexing Screen -->
            <div class="indexing-screen" id="indexing-screen">
                <div class="indexing-title">Index Codebase</div>

                <div class="indexing-description">
                    Indexing allows the AI to make tailored code suggestions and explain common practices or patterns.
                </div>

                <div class="privacy-note">
                    Your data always stays secure, private and anonymized.
                </div>

                <div class="workspace-display" id="workspace-display">
                    <span class="workspace-icon">📁</span>
                    <span id="workspace-name">Loading workspace...</span>
                    <button id="refresh-workspace" style="margin-left: 10px; padding: 2px 6px; font-size: 12px; cursor: pointer;" title="Refresh workspace name">🔄</button>
                </div>

                <div class="github-option">
                    Or <a href="#" class="github-link" id="github-option">index a GitHub repository</a>
                </div>

                <div class="github-input-container hidden" id="github-input-container">
                    <input type="text" id="github-url" placeholder="https://github.com/user/repo">
                </div>

                <button class="btn-index" id="start-indexing">Index Codebase</button>

                <div class="progress-container hidden" id="progress-container">
                    <div class="progress-step" id="step-connecting">🔗 Connecting...</div>
                    <div class="progress-step" id="step-analyzing">📊 Analyzing code structure...</div>
                    <div class="progress-step" id="step-embeddings">🧠 Creating embeddings...</div>
                    <div class="progress-step" id="step-storing">💾 Storing index...</div>
                </div>
            </div>

            <!-- Chat Interface Section (hidden initially) -->
            <div class="chat-container hidden" id="chat-section">
                <div class="chat-messages" id="chat-messages">
                    <!-- Chat messages will appear here -->
                </div>

                <div class="chat-input-area">
                    <div class="file-header">
                        <div class="file-tabs" id="file-tabs">
                            <!-- File tabs will be populated dynamically -->
                        </div>
                        <div class="chat-toggle-container">
                            <select class="chat-toggle-dropdown" id="chat-mode-dropdown">
                                <option value="agent" selected>🤖 Agent mode</option>
                                <option value="human">🧑‍💻 Human in loop</option>
                            </select>
                            <button class="new-conversation-btn" id="new-conversation-btn" title="Start New Conversation">
                                <span class="btn-icon">+</span>
                                <span class="btn-text">New</span>
                            </button>
                        </div>
                    </div>

                    <div class="chat-input-container">
                        <textarea class="chat-input" id="chat-input" placeholder="Ask or instruct AI Agent..." rows="3"></textarea>
                        <div class="chat-actions">
                            <button class="action-btn" id="attachment-btn" title="Attach files">📎</button>
                            <button class="action-btn" id="mention-btn" title="Mention">@</button>
                            <button class="action-btn" id="sparkles-btn" title="AI Suggestions">✨</button>
                            <button class="send-btn" id="send-message" title="Send message">➤</button>
                        </div>
                    </div>
                </div>

                <input type="file" id="file-input" style="display: none;" multiple>
            </div>

            <script nonce="${nonce}">
                // Define exports object for CommonJS compatibility
                const exports = {};
                const module = { exports: exports };
            </script>
            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`;
  }
}
