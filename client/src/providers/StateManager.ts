import * as vscode from "vscode";

export interface PersistedState {
  // Chat and messaging
  chatHistory: any[];
  processingMessages: any[];
  currentMode: string;
  chatMode: "agent" | "human";

  // Connection and status
  isConnected: boolean;
  lastActivity: number | null;
  isIndexed: boolean;

  // Tool and processing state
  toolStatuses: { [key: string]: any };
  taskList: any;
  pendingCodeApproval: any;
  codeApplicationInProgress: boolean;
  lastCodeApplicationTime: number | null;

  // Enhanced webview state
  currentScreen: "indexing" | "chat" | "loading";
  scrollPosition: number;
  inputValue: string;
  activeProcesses: any[];
  queueStatus: any;
  lastServerResponse: any;
  webviewVisible: boolean;
  sessionId: string;
}

export class StateManager {
  private _context: vscode.ExtensionContext;
  private _persistedState: PersistedState;

  constructor(context: vscode.ExtensionContext) {
    this._context = context;

    // Generate unique session ID
    const sessionId = `session_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    this._persistedState = {
      // Chat and messaging
      chatHistory: [],
      processingMessages: [],
      currentMode: "index",
      chatMode: "agent", // Default to agent mode

      // Connection and status
      isConnected: false,
      lastActivity: null,
      isIndexed: false,

      // Tool and processing state
      toolStatuses: {},
      taskList: null,
      pendingCodeApproval: null,
      codeApplicationInProgress: false,
      lastCodeApplicationTime: null,

      // Enhanced webview state
      currentScreen: "loading",
      scrollPosition: 0,
      inputValue: "",
      activeProcesses: [],
      queueStatus: null,
      lastServerResponse: null,
      webviewVisible: false,
      sessionId: sessionId,
    };

    // Try to restore state from previous session
    this.restorePersistedState();
  }

  public getState(): PersistedState {
    return this._persistedState;
  }

  public updateState(updates: Partial<PersistedState>) {
    this._persistedState = { ...this._persistedState, ...updates };
  }

  public addChatMessage(message: any) {
    this._persistedState.chatHistory.push(message);
    this._persistedState.lastActivity = Date.now();
    this._persistedState.currentMode = "chat";
    console.log(
      "📨 Updated chat history. Total messages:",
      this._persistedState.chatHistory.length
    );

    // Auto-save immediately when chat messages are added
    this.saveWebviewState();
  }

  public removeThinkingMessage() {
    // Remove thinking messages from chat history
    this._persistedState.chatHistory = this._persistedState.chatHistory.filter(
      (message: any) => message.messageType !== "thinking"
    );
    console.log(
      "🗑️ Removed thinking messages. Remaining messages:",
      this._persistedState.chatHistory.length
    );

    // Auto-save immediately when messages are removed
    this.saveWebviewState();
  }

  public updateToolStatus(toolName: string, status: any) {
    this._persistedState.toolStatuses[toolName] = {
      status: status.status,
      message: status.message,
      timestamp: status.timestamp || Date.now(),
    };
    console.log("💾 Updated tool status:", toolName, "->", status.status);
  }

  public addProcessingMessage(message: any) {
    this._persistedState.processingMessages.push({
      content: message.message || message,
      type: message.type || "info",
      timestamp: Date.now(),
    });
    // Keep only last 50 processing messages to avoid memory bloat
    if (this._persistedState.processingMessages.length > 50) {
      this._persistedState.processingMessages =
        this._persistedState.processingMessages.slice(-50);
    }
    console.log(
      "💾 Added processing message. Total:",
      this._persistedState.processingMessages.length
    );
  }

  public setTaskList(taskList: any) {
    this._persistedState.taskList = taskList;
    console.log("💾 Updated task list");
  }

  public setPendingCodeApproval(approval: any) {
    this._persistedState.pendingCodeApproval = {
      data: approval,
      timestamp: Date.now(),
    };
    console.log("💾 Saved pending code approval prompt");
  }

  public clearPendingCodeApproval() {
    this._persistedState.pendingCodeApproval = null;
    this._persistedState.lastActivity = Date.now(); // Update activity when code approval is cleared
    console.log("💾 Cleared pending code approval - user responded");
  }

  public updateActivity() {
    this._persistedState.lastActivity = Date.now();
    console.log("💾 Updated last activity timestamp");
  }

  public updateChatMode(chatMode: "agent" | "human") {
    this._persistedState.chatMode = chatMode;
    console.log(`💾 Updated chat mode to: ${chatMode}`);
  }

  public setCodeApplicationInProgress(inProgress: boolean) {
    this._persistedState.codeApplicationInProgress = inProgress;
    if (inProgress) {
      this._persistedState.lastActivity = Date.now();
      console.log("💾 Code application started - updated activity timestamp");
    } else {
      this._persistedState.lastCodeApplicationTime = Date.now();
      this._persistedState.lastActivity = Date.now();
      console.log("💾 Code application completed - updated timestamps");
    }
  }

  public isRecentCodeApplication(): boolean {
    if (!this._persistedState.lastCodeApplicationTime) {
      return false;
    }
    const timeSinceCodeApplication =
      Date.now() - this._persistedState.lastCodeApplicationTime;
    return timeSinceCodeApplication < 60000; // 60 seconds
  }

  public setIndexingComplete() {
    this._persistedState.isIndexed = true;
    this._persistedState.currentMode = "chat";
    this._persistedState.lastActivity = Date.now();
    console.log("💾 Indexing complete - updated persisted state");
  }

  public async saveIndexingState(workspaceHash: string, isIndexed: boolean) {
    try {
      // Save to global state (persists across VS Code sessions)
      await this._context.globalState.update(
        `recode-indexed-${workspaceHash}`,
        isIndexed
      );

      // Also save to workspace configuration as fallback
      const workspaceState = vscode.workspace.getConfiguration("recode");
      await workspaceState.update(
        "isIndexed",
        isIndexed,
        vscode.ConfigurationTarget.Workspace
      );

      console.log(
        `Indexing state saved for workspace ${workspaceHash}: ${isIndexed}`
      );
    } catch (error) {
      console.error("Failed to save indexing state:", error);
    }
  }

  public checkIndexingStatus(): boolean {
    console.log("🔍 checkIndexingStatus - START");

    // Get workspace folder path to create unique identifier
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      console.log("🔍 No workspace folders found, setting isIndexed = false");
      this._persistedState.isIndexed = false;
      return false;
    }

    const workspacePath = workspaceFolders[0].uri.fsPath;
    const workspaceHash = this.generateWorkspaceHash(workspacePath);
    console.log("🔍 Workspace hash:", workspaceHash);

    // Check global state for this specific workspace
    const globalStateIndexed = this._context.globalState.get(
      `recode-indexed-${workspaceHash}`,
      false
    );

    console.log("🔍 Global state indexed:", globalStateIndexed);
    console.log(
      "🔍 Current persisted isIndexed:",
      this._persistedState.isIndexed
    );

    // Use the most recent state (prioritize persisted state, then global state)
    if (this._persistedState.isIndexed) {
      console.log("🔍 Using persisted state: isIndexed = true");
      return true;
    } else if (globalStateIndexed) {
      this._persistedState.isIndexed = true;
      console.log("🔍 Using global state: isIndexed = true");
      return true;
    } else {
      this._persistedState.isIndexed = false;
      console.log("🔍 No indexing found: isIndexed = false");
      return false;
    }
  }

  private generateWorkspaceHash(workspacePath: string): string {
    // Simple hash function for workspace path
    let hash = 0;
    for (let i = 0; i < workspacePath.length; i++) {
      const char = workspacePath.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString();
  }

  public getStateSummary() {
    return {
      chatHistory: this._persistedState.chatHistory.length,
      toolStatuses: Object.keys(this._persistedState.toolStatuses).length,
      processingMessages: this._persistedState.processingMessages.length,
      hasTaskList: !!this._persistedState.taskList,
    };
  }

  public resetIndexingState() {
    this._persistedState.isIndexed = false;
    this._persistedState.chatHistory = [];
    this._persistedState.taskList = null;
    this._persistedState.pendingCodeApproval = null;
    // Add more resets if needed for your UI
  }

  // ===== ENHANCED WEBVIEW STATE PERSISTENCE =====

  /**
   * Get current webview state
   */
  public getWebviewState(): PersistedState {
    return this._persistedState;
  }

  /**
   * Save complete webview state to VSCode's persistent storage
   */
  public async saveWebviewState(): Promise<void> {
    try {
      const stateToSave = {
        ...this._persistedState,
        lastSaved: Date.now(),
      };

      // Save to workspace state (survives VSCode restarts)
      await this._context.workspaceState.update(
        "recode-webview-state",
        stateToSave
      );

      // Also save to global state as backup
      await this._context.globalState.update(
        "recode-webview-state-backup",
        stateToSave
      );

      console.log(
        `💾 Webview state saved (${this._persistedState.chatHistory.length} messages, session: ${this._persistedState.sessionId})`
      );
    } catch (error) {
      console.error("❌ Failed to save webview state:", error);
    }
  }

  /**
   * Restore webview state from persistent storage
   */
  private async restorePersistedState(): Promise<void> {
    try {
      // Try workspace state first
      let savedState = this._context.workspaceState.get<PersistedState>(
        "recode-webview-state"
      );

      // Fallback to global state
      if (!savedState) {
        savedState = this._context.globalState.get<PersistedState>(
          "recode-webview-state-backup"
        );
      }

      if (savedState) {
        // Merge saved state with current state (preserve session ID)
        const currentSessionId = this._persistedState.sessionId;
        this._persistedState = {
          ...savedState,
          sessionId: currentSessionId, // Keep new session ID
          webviewVisible: false, // Reset visibility
          lastActivity: Date.now(), // Update activity
        };

        console.log(
          `🔄 Webview state restored (${this._persistedState.chatHistory.length} messages, previous session)`
        );
      } else {
        console.log("📝 No previous webview state found - starting fresh");
      }
    } catch (error) {
      console.error("❌ Failed to restore webview state:", error);
    }
  }

  /**
   * Update webview visibility and auto-save state
   */
  public setWebviewVisible(visible: boolean): void {
    this._persistedState.webviewVisible = visible;
    this._persistedState.lastActivity = Date.now();

    if (!visible) {
      // Webview is being hidden/disposed - save state immediately
      console.log("👁️ Webview hidden - saving state...");
      this.saveWebviewState();
    } else {
      console.log("👁️ Webview visible - state restored");
    }
  }

  /**
   * Update scroll position
   */
  public updateScrollPosition(position: number): void {
    this._persistedState.scrollPosition = position;
  }

  /**
   * Update input value
   */
  public updateInputValue(value: string): void {
    this._persistedState.inputValue = value;
  }

  /**
   * Update current screen
   */
  public updateCurrentScreen(screen: "indexing" | "chat" | "loading"): void {
    this._persistedState.currentScreen = screen;
    this.saveWebviewState(); // Auto-save on screen changes
  }

  /**
   * Add active process
   */
  public addActiveProcess(process: any): void {
    this._persistedState.activeProcesses.push(process);
    this.saveWebviewState(); // Auto-save process changes
  }

  /**
   * Remove active process
   */
  public removeActiveProcess(processId: string): void {
    this._persistedState.activeProcesses =
      this._persistedState.activeProcesses.filter((p) => p.id !== processId);
    this.saveWebviewState(); // Auto-save process changes
  }

  /**
   * Update queue status
   */
  public updateQueueStatus(status: any): void {
    this._persistedState.queueStatus = status;
  }

  /**
   * Update last server response
   */
  public updateLastServerResponse(response: any): void {
    this._persistedState.lastServerResponse = response;
  }

  /**
   * Get complete state for webview restoration
   */
  public getWebviewRestoreData(): any {
    return {
      chatHistory: this._persistedState.chatHistory,
      processingMessages: this._persistedState.processingMessages,
      currentScreen: this._persistedState.currentScreen,
      scrollPosition: this._persistedState.scrollPosition,
      inputValue: this._persistedState.inputValue,
      activeProcesses: this._persistedState.activeProcesses,
      queueStatus: this._persistedState.queueStatus,
      taskList: this._persistedState.taskList,
      pendingCodeApproval: this._persistedState.pendingCodeApproval,
      codeApplicationInProgress: this._persistedState.codeApplicationInProgress,
      isConnected: this._persistedState.isConnected,
      sessionId: this._persistedState.sessionId,
    };
  }

  /**
   * Clear all state for new conversation
   */
  public clearAllState(): void {
    console.log("🧹 Clearing all state for new conversation");

    // Reset to initial state but keep session ID
    const currentSessionId = this._persistedState.sessionId;

    this._persistedState = {
      // Chat and messaging
      chatHistory: [],
      processingMessages: [],
      currentMode: "chat",
      chatMode: "agent", // Default to agent mode

      // Connection and status
      isConnected: false,
      lastActivity: Date.now(),
      isIndexed: true, // Keep indexed state

      // Tool and processing state
      toolStatuses: {},
      taskList: null,
      pendingCodeApproval: null,
      codeApplicationInProgress: false,
      lastCodeApplicationTime: null,

      // Enhanced webview state
      currentScreen: "chat",
      scrollPosition: 0,
      inputValue: "",
      activeProcesses: [],
      queueStatus: null,
      lastServerResponse: null,
      webviewVisible: true,
      sessionId: currentSessionId, // Keep existing session ID
    };

    console.log("✅ All state cleared for new conversation");
  }
}
