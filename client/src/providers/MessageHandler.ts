import * as vscode from "vscode";
import { WebSocketManager } from "./WebSocketManager";
import { StateManager } from "./StateManager";
import { FileOperationsManager } from "./FileOperationsManager";
import { FileCommandManager } from "../services/FileCommandManager";
import { WebSocketMessageHandler } from "./WebSocketMessageHandler";
import { LSPService, LSPFileErrors } from "../services/LSPService";
import { WorkspaceIndexingManager } from "./WorkspaceIndexingManager";

export interface MessageSender {
  (message: any): void;
}

export class MessageHandler {
  private _webSocketManager: WebSocketManager;
  private _stateManager: StateManager;
  private _fileOpsManager: FileOperationsManager;
  private _fileCommandManager: FileCommandManager;
  private _webSocketMessageHandler: WebSocketMessageHandler;
  private _lspService: LSPService;
  private _sendMessage: MessageSender;
  private _workspaceIndexingManager: WorkspaceIndexingManager;

  // Synchronous feedback system
  private _pendingFeedbackTimeout: NodeJS.Timeout | null = null;
  private _pendingOperations: any[] = [];
  private _userResponseTimeout: number;

  // Track successfully edited files for accurate LSP analysis
  private successfullyEditedFiles: string[] = [];

  constructor(
    webSocketManager: WebSocketManager,
    stateManager: StateManager,
    fileOpsManager: FileOperationsManager,
    fileCommandManager: FileCommandManager,
    webSocketMessageHandler: WebSocketMessageHandler,
    workspaceIndexingManager: WorkspaceIndexingManager,
    sendMessage: MessageSender
  ) {
    this._webSocketManager = webSocketManager;
    this._stateManager = stateManager;
    this._fileOpsManager = fileOpsManager;
    this._fileCommandManager = fileCommandManager;
    this._webSocketMessageHandler = webSocketMessageHandler;
    this._workspaceIndexingManager = workspaceIndexingManager;
    this._lspService = new LSPService();
    this._sendMessage = sendMessage;

    // Set up file operation callback to track successfully edited files
    this._fileOpsManager.setFileOperationCallback({
      onStart: (operation: string, filePath: string) => {
        // Optional: could track start operations if needed
      },
      onComplete: (
        filePath: string,
        success: boolean,
        actualFilePath?: string
      ) => {
        if (success && actualFilePath) {
          // Track the actual file path that was successfully edited
          if (!this.successfullyEditedFiles.includes(actualFilePath)) {
            this.successfullyEditedFiles.push(actualFilePath);
            console.log(
              `📝 Tracked successfully edited file: ${actualFilePath}`
            );
          }
        }
      },
    });

    // Initialize user response timeout from environment
    this._userResponseTimeout =
      parseInt(process.env.RECODE_USER_RESPONSE_TIMEOUT || "600") * 1000; // Convert to milliseconds
    console.log(
      `⏰ User response timeout set to: ${
        this._userResponseTimeout / 1000
      } seconds`
    );
  }

  public async handleWebviewMessage(data: any) {
    switch (data.command) {
      case "getSettings":
        await this.handleGetSettings();
        break;
      case "saveSettings":
        await this.handleSaveSettings(data.settings);
        break;
      case "testConnection":
        await this.handleTestConnection();
        break;
      case "startIndexing":
        await this.handleStartIndexing(data.data);
        break;
      case "resetWorkspaceIndexing":
        await this.handleResetWorkspaceIndexing();
        break;
      case "debugWorkspaceState":
        await this.handleDebugWorkspaceState();
        break;
      case "sendMessage":
        await this.handleSendMessage(data.message, data.askMode);
        break;
      case "getWorkspaceName":
        await this.handleGetWorkspaceName();
        break;
      case "getOpenFiles":
        await this.handleGetOpenFiles();
        break;
      case "closeFile":
        await this.handleCloseFile(data.filePath);
        break;
      case "generateProjectSummary":
        await this.handleGenerateProjectSummary();
        break;
      case "codeApprovalResponse":
        // CRITICAL FIX: Update activity first if requested to prevent state restoration
        if (data.updateActivity) {
          console.log(
            "💾 Updating activity timestamp to prevent state restoration (from combined message)"
          );
          this._stateManager.updateActivity();
        }

        await this.handleUserCodeApprovalResponse(
          data.approved,
          data.operations
        );
        this._stateManager.clearPendingCodeApproval();
        break;
      case "addChatMessage":
        console.log("📨 Received addChatMessage from webview:", data.message);
        this._stateManager.addChatMessage(data.message);
        break;
      case "removeThinkingMessage":
        console.log("🗑️ Received removeThinkingMessage from webview");
        this._stateManager.removeThinkingMessage();
        break;
      case "clearPendingCodeApproval":
        console.log("🧹 Clearing pending code approval from state");
        this._stateManager.clearPendingCodeApproval();
        break;
      case "updateActivity":
        console.log(
          "💾 Updating activity timestamp to prevent state restoration"
        );
        this._stateManager.updateActivity();
        break;
      case "updateChatMode":
        console.log(`🤖 Updating chat mode to: ${data.chatMode}`);
        this._stateManager.updateChatMode(data.chatMode);
        break;
      case "openFile":
        await this.handleOpenFile(data.filePath);
        break;
      case "executeTerminalCommand":
        await this.handleExecuteTerminalCommand(
          data.terminalCommand || data.command
        );
        break;
      case "error":
        vscode.window.showErrorMessage(data.text);
        break;
      case "success":
        vscode.window.showInformationMessage(data.text);
        break;
      case "newConversation":
        await this.handleNewConversation();
        break;
    }
  }

  private async handleGetSettings() {
    const config = vscode.workspace.getConfiguration("recode");
    const settings = {
      provider: config.get("llmProvider"),
      model: config.get("llmModel"),
      apiKey: config.get("apiKey"),
    };
    this._sendMessage({ command: "settingsLoaded", settings });
  }

  private async handleSaveSettings(settings: any) {
    const config = vscode.workspace.getConfiguration("recode");
    await config.update(
      "llmProvider",
      settings.provider,
      vscode.ConfigurationTarget.Global
    );
    await config.update(
      "llmModel",
      settings.model,
      vscode.ConfigurationTarget.Global
    );
    await config.update(
      "apiKey",
      settings.apiKey,
      vscode.ConfigurationTarget.Global
    );

    vscode.window.showInformationMessage("Settings saved successfully!");
  }

  private async handleTestConnection() {
    // Simulate connection test
    setTimeout(() => {
      this._sendMessage({
        command: "connectionResult",
        success: true,
        message: "Connection successful!",
      });
    }, 1000);
  }

  private async handleNewConversation() {
    console.log("🆕 Handling new conversation request");

    try {
      // Import SessionManager dynamically to avoid circular dependencies
      const { SessionManager } = await import("../sessionManager");

      // Get context from StateManager since MessageHandler doesn't have direct access
      const context = (this._stateManager as any)._context;
      if (!context) {
        throw new Error("Extension context not available");
      }

      const sessionManager = SessionManager.getInstance(context);

      // Create a new session
      const projectName = vscode.workspace.workspaceFolders?.[0]?.name;
      await sessionManager.createNewSession(projectName);

      console.log("🆕 New session created successfully");

      // Clear state manager for fresh start
      this._stateManager.clearAllState();
    } catch (error) {
      console.error("❌ Failed to create new conversation:", error);
      vscode.window.showErrorMessage(
        `Failed to start new conversation: ${error}`
      );
    }
  }

  private async handleStartIndexing(data: any) {
    if (data.type === "workspace") {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        vscode.window.showErrorMessage("No workspace folder is open");
        return;
      }

      try {
        // Send progress update to webview
        this._sendMessage({
          command: "indexingProgress",
          message: "Initializing indexing service...",
        });

        // Import indexingService dynamically to avoid circular dependencies
        const { indexingService } = await import("../indexing");

        // Perform actual full indexing
        await indexingService.performFullIndex();

        // Update state using both old and new systems for compatibility
        this._stateManager.setIndexingComplete();

        // Mark workspace as indexed using the new smart system
        await this._workspaceIndexingManager.markWorkspaceAsIndexed();

        // Get workspace hash for persistent storage (keep for backward compatibility)
        const workspacePath = workspaceFolders[0].uri.fsPath;
        const workspaceHash = this.generateWorkspaceHash(workspacePath);

        // Save indexing state persistently (keep for backward compatibility)
        await this._stateManager.saveIndexingState(workspaceHash, true);

        console.log("✅ Workspace indexing completed and marked as indexed");

        // Send completion message to webview
        this._sendMessage({ command: "indexingComplete" });
      } catch (error) {
        console.error("Indexing failed:", error);
        vscode.window.showErrorMessage(`Indexing failed: ${error}`);
        this._sendMessage({
          command: "indexingError",
          error: error instanceof Error ? error.message : String(error),
        });
      }
    } else if (data.type === "github") {
      vscode.window.showInformationMessage(
        `Starting GitHub indexing for: ${data.url}`
      );
      // TODO: Implement GitHub indexing if needed
    }
  }

  private async handleResetWorkspaceIndexing() {
    try {
      console.log("🔄 Resetting workspace indexing status...");

      // Reset using the new smart system
      await this._workspaceIndexingManager.resetCurrentWorkspaceIndexing();

      // Also reset the old state manager for compatibility
      this._stateManager.resetIndexingState();

      console.log("✅ Workspace indexing status reset successfully");

      // Send success message to webview
      this._sendMessage({
        command: "workspaceIndexingReset",
        message:
          "Workspace indexing status has been reset. You can now re-index this workspace.",
      });

      // Show indexing screen
      this._sendMessage({ command: "showIndexingScreen" });
    } catch (error) {
      console.error("❌ Failed to reset workspace indexing:", error);
      this._sendMessage({
        command: "error",
        message: `Failed to reset workspace indexing: ${
          error instanceof Error ? error.message : String(error)
        }`,
      });
    }
  }

  private async handleDebugWorkspaceState() {
    try {
      console.log("🔍 === WORKSPACE STATE DEBUG ===");

      // Get debug info from workspace indexing manager
      const debugInfo = this._workspaceIndexingManager.getDebugInfo();
      console.log("🔍 Workspace Indexing Manager Debug Info:", debugInfo);

      // Get state manager info
      const stateInfo = this._stateManager.getStateSummary();
      console.log("🔍 State Manager Info:", stateInfo);

      // Check what screen should be shown
      const shouldShowIndexing =
        this._workspaceIndexingManager.shouldShowIndexingScreen();
      console.log("🔍 Should show indexing screen:", shouldShowIndexing);

      // Send debug info to webview
      this._sendMessage({
        command: "debugInfo",
        data: {
          workspaceIndexing: debugInfo,
          stateManager: stateInfo,
          shouldShowIndexing: shouldShowIndexing,
          timestamp: new Date().toISOString(),
        },
      });

      console.log("🔍 === END WORKSPACE STATE DEBUG ===");
    } catch (error) {
      console.error("❌ Failed to get debug workspace state:", error);
      this._sendMessage({
        command: "error",
        message: `Failed to get debug info: ${
          error instanceof Error ? error.message : String(error)
        }`,
      });
    }
  }

  private async handleSendMessage(message: string, askMode: boolean = false) {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        this._sendMessage({
          command: "aiResponse",
          response: "No workspace found. Please open a workspace first.",
        });
        return;
      }

      const workspaceName = workspaceFolders[0].name;

      if (askMode) {
        // Use project summary endpoint when chat mode is enabled
        const host = process.env.RECODE_SERVER_HOST || "127.0.0.1";
        const port = process.env.RECODE_SERVER_PORT || "8000";
        const endpoint =
          process.env.RECODE_PROJECT_SUMMARY_ENDPOINT || "/project/summary";
        const url = `http://${host}:${port}${endpoint}`;

        const response = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            project_name: workspaceName,
            question: message,
          }),
        });

        if (!response.ok) {
          throw new Error(`API call failed: ${response.status}`);
        }

        const result = await response.json();
        const aiResponse =
          result.summary ||
          result.response ||
          "I couldn't process your request.";

        this._sendMessage({
          command: "aiResponse",
          response: aiResponse,
        });
      } else {
        // Use WebSocket for coder agent when chat mode is disabled
        await this.handleCoderModeWebSocket(workspaceName, message);
      }
    } catch (error) {
      console.error("Error handling send message:", error);
      this._sendMessage({
        command: "aiResponse",
        response:
          "Sorry, I encountered an error processing your request. Please try again.",
      });
    }
  }

  private async handleCoderModeWebSocket(
    workspaceName: string,
    message: string
  ) {
    try {
      // Connect to WebSocket if not already connected
      if (!this._webSocketManager.isConnected()) {
        await this._webSocketManager.connect();
      }

      // Get current active file information
      const activeEditor = vscode.window.activeTextEditor;
      let enhancedMessage = message;

      if (activeEditor) {
        const fileName = activeEditor.document.uri.path.split("/").pop() || "";
        // Only append if not an output file
        if (!fileName.startsWith("extension-output-sarthikbhat.json-server")) {
          enhancedMessage = `user query ${message} working on ${fileName}`;
        }
      }

      // Send coder request via WebSocket with enhanced message
      this._webSocketManager.send({
        type: "coder_request",
        data: {
          project_name: workspaceName,
          question: enhancedMessage,
          // Do NOT send active_file
        },
      });
    } catch (error) {
      console.error("Error in coder mode WebSocket:", error);
      this._sendMessage({
        command: "aiResponse",
        response: `❌ Error connecting to coder agent: ${
          error instanceof Error ? error.message : String(error)
        }`,
      });
    }
  }

  private async handleGetWorkspaceName() {
    console.log("🔍 handleGetWorkspaceName called");
    const workspaceFolders = vscode.workspace.workspaceFolders;
    let workspaceName = "Current Workspace";

    console.log("🔍 Workspace folders:", workspaceFolders?.length || 0);

    if (workspaceFolders && workspaceFolders.length > 0) {
      workspaceName = workspaceFolders[0].name;
      console.log("🔍 Using workspace name:", workspaceName);
    } else {
      console.log("⚠️ No workspace folders found, using default name");
    }

    console.log("🔍 Sending workspace name to webview:", workspaceName);
    this._sendMessage({
      command: "workspaceName",
      name: workspaceName,
    });
  }

  private async handleGetOpenFiles() {
    const openFiles: string[] = [];

    // Get all open text documents
    vscode.workspace.textDocuments.forEach((doc) => {
      if (doc.uri.scheme === "file" && !doc.isUntitled) {
        openFiles.push(doc.uri.fsPath);
      }
    });

    this._sendMessage({
      command: "openFiles",
      files: openFiles,
    });
  }

  private async handleCloseFile(filePath: string) {
    // Find and close the document
    const doc = vscode.workspace.textDocuments.find(
      (d) => d.uri.fsPath === filePath
    );
    if (doc) {
      await vscode.window.showTextDocument(doc);
      await vscode.commands.executeCommand(
        "workbench.action.closeActiveEditor"
      );
    }
  }

  private async handleGenerateProjectSummary() {
    try {
      console.log("🤖 handleGenerateProjectSummary called");

      // Get workspace information
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return;
      }

      const workspaceName = workspaceFolders[0].name;

      // Call the actual API to generate project summary
      try {
        const host = process.env.RECODE_SERVER_HOST || "127.0.0.1";
        const port = process.env.RECODE_SERVER_PORT || "8000";
        const endpoint =
          process.env.RECODE_PROJECT_SUMMARY_ENDPOINT || "/project/summary";
        const url = `http://${host}:${port}${endpoint}`;

        console.log("🌐 Making API call to:", url);

        const response = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            project_name: workspaceName,
            question:
              "Generate a high-level summary of this code in 3 sentences.",
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error("❌ API call failed:", response.status, errorText);
          throw new Error(`API call failed: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        console.log("✅ API response received:", result);
        const summary = result.summary;

        console.log("📤 Sending projectSummary message to webview");
        this._sendMessage({
          command: "projectSummary",
          summary: summary,
        });

        return;
      } catch (error) {
        console.error(
          "Failed to get dynamic summary, falling back to static:",
          error
        );
        // Fall back to static summary if API fails
      }

      // Fallback: Generate mock project summary if API fails
      const summary = this.generateMockProjectSummary(workspaceName);

      this._sendMessage({
        command: "projectSummary",
        summary: summary,
      });
    } catch (error) {
      console.error("Failed to generate project summary:", error);
      this._sendMessage({
        command: "projectSummary",
        summary: "Sorry, I couldn't generate a project summary at this time.",
      });
    }
  }

  private generateMockProjectSummary(workspaceName: string): string {
    return `**Project Summary**

AI Assistant just finished syncing with your codebase! Here is a summary of what we saw:

**Project: ${workspaceName}**

This appears to be a VS Code extension project with the following key characteristics:

• **Extension Type**: VS Code extension with sidebar webview functionality
• **Main Features**: Code indexing, chat interface, and AI-powered code assistance
• **Technology Stack**: TypeScript, Node.js, VS Code Extension API
• **Architecture**: Provider-based architecture with webview integration

**Key Components:**
- Sidebar provider for the main interface
- Indexing functionality for workspace analysis
- Chat interface for user interaction
- File management and workspace integration

The project is structured to provide an AI coding assistant directly within VS Code, allowing users to interact with their codebase through a conversational interface.`;
  }

  private async handleUserCodeApprovalResponse(
    approved: boolean,
    operations: any[]
  ) {
    try {
      console.log(
        `🎯 [${new Date().toISOString()}] MessageHandler: handleUserCodeApprovalResponse called - approved: ${approved}`
      );

      // Mark code application as in progress to prevent UI reset
      this._stateManager.setCodeApplicationInProgress(true);
      console.log(
        `🎯 [${new Date().toISOString()}] MessageHandler: Set code application in progress`
      );

      if (!approved) {
        console.log(
          `🎯 [${new Date().toISOString()}] MessageHandler: User cancelled code application`
        );
        this._stateManager.setCodeApplicationInProgress(false);
        return;
      }

      console.log(
        `🎯 [${new Date().toISOString()}] MessageHandler: About to process ${
          operations.length
        } operations`
      );

      // Sort operations by line number in DESCENDING order
      const sortedOperations = [...operations].sort((a, b) => {
        if (a.file !== b.file) {
          return a.file.localeCompare(b.file);
        }
        return (b.start_line || 0) - (a.start_line || 0);
      });

      // Apply the operations in reverse line order
      let appliedCount = 0;
      const errors: string[] = [];

      // Clear previously tracked files for this operation batch
      this.successfullyEditedFiles = [];

      console.log(
        `🎯 [${new Date().toISOString()}] MessageHandler: Starting operation application loop`
      );

      for (const operation of sortedOperations) {
        try {
          console.log(
            `🎯 [${new Date().toISOString()}] MessageHandler: About to apply operation ${
              appliedCount + 1
            }/${sortedOperations.length} on ${operation.file}`
          );

          const success = await this._fileOpsManager.applyCodeOperation(
            operation
          );

          if (success) {
            appliedCount++;
            // Track the file that was successfully edited
            const editedFile =
              operation.file || operation.file_path || operation.filePath;
            if (
              editedFile &&
              !this.successfullyEditedFiles.includes(editedFile)
            ) {
              this.successfullyEditedFiles.push(editedFile);
            }
            console.log(
              `🎯 [${new Date().toISOString()}] MessageHandler: ✅ Successfully applied operation ${appliedCount} on ${editedFile}`
            );
          } else {
            const errorMsg = `Failed to apply operation on ${operation.file} (lines ${operation.start_line}-${operation.end_line})`;
            console.error(
              `🎯 [${new Date().toISOString()}] MessageHandler: ❌ ${errorMsg}`
            );
            errors.push(errorMsg);
          }
        } catch (error) {
          const errorMsg =
            error instanceof Error ? error.message : String(error);
          console.error(
            `🎯 [${new Date().toISOString()}] MessageHandler: ❌ Exception applying operation on ${
              operation.file
            }:`,
            error
          );
          errors.push(
            `Error applying operation on ${operation.file}: ${errorMsg}`
          );
        }
      }

      console.log(
        `🎯 [${new Date().toISOString()}] MessageHandler: Completed operation application loop - applied: ${appliedCount}, errors: ${
          errors.length
        }`
      );

      // Log status locally (no VSCode notifications to avoid webview focus loss)
      if (errors.length === 0) {
        console.log(
          `🎯 [${new Date().toISOString()}] MessageHandler: ✅ Applied ${appliedCount} code changes successfully!`
        );

        // Mark code application as completed
        this._stateManager.setCodeApplicationInProgress(false);
        console.log(
          `🎯 [${new Date().toISOString()}] MessageHandler: 🔄 Marked code application as completed with success`
        );

        // Start synchronous feedback timer - wait for user response
        console.log(
          `⏰ Starting synchronous feedback timer for ${
            this._userResponseTimeout / 1000
          } seconds...`
        );
        console.log(`🔍 Operations to analyze:`, operations);

        this.startSynchronousFeedbackTimer(operations);

        // CRITICAL: Trigger immediate feedback since user just applied the code
        // But ONLY if the timeout is still active (user applied within time limit)
        console.log(
          `⚡ Code was just applied by user - checking if within timeout...`
        );
        setTimeout(async () => {
          // Double-check that timeout is still active before sending feedback
          if (
            this._pendingFeedbackTimeout &&
            this._pendingOperations.length > 0
          ) {
            console.log(
              `⚡ User applied code WITHIN timeout - triggering immediate feedback!`
            );
            await this.handleImmediateFeedback();
          } else {
            console.log(
              `🚫 User applied code AFTER timeout expired - immediate feedback already sent or timeout expired`
            );
          }
        }, 2000); // 2 second delay to ensure files are saved and LSP has analyzed
      } else {
        console.error(
          `❌ Applied ${appliedCount}/${operations.length} changes. Some operations failed.`
        );
        // Mark code application as completed even with errors
        this._stateManager.setCodeApplicationInProgress(false);
        console.log("🔄 Marked code application as completed (with errors)");
      }
    } catch (error) {
      console.error("❌ Error handling code apply request:", error);
      console.error(
        `❌ Error details: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      // Mark code application as completed even on exception
      this._stateManager.setCodeApplicationInProgress(false);
      console.log(
        "🔄 Marked code application as completed (exception occurred)"
      );
    }
  }

  /**
   * Start synchronous feedback timer - wait for user response
   */
  private startSynchronousFeedbackTimer(operations: any[]): void {
    // Clear any existing timeout
    if (this._pendingFeedbackTimeout) {
      clearTimeout(this._pendingFeedbackTimeout);
    }

    // Store operations for later use
    this._pendingOperations = operations;

    console.log(
      `⏰ Waiting ${
        this._userResponseTimeout / 1000
      } seconds for user to apply code changes...`
    );

    // Set timeout for fallback to normal queued feedback
    this._pendingFeedbackTimeout = setTimeout(async () => {
      console.log(
        `⏰ TIMEOUT: User did not apply code within ${
          this._userResponseTimeout / 1000
        } seconds`
      );
      console.log(`🔄 Falling back to normal queued LSP feedback...`);

      // Trigger normal LSP feedback (queued)
      await this.triggerLSPFeedback(this._pendingOperations, false); // false = queued feedback

      // Clear pending state
      this._pendingOperations = [];
      this._pendingFeedbackTimeout = null;
    }, this._userResponseTimeout);
  }

  /**
   * Handle immediate feedback when user applies code within timeout
   */
  public async handleImmediateFeedback(): Promise<void> {
    // Check if timeout is still active (user applied within time limit)
    if (this._pendingFeedbackTimeout && this._pendingOperations.length > 0) {
      console.log(
        `⚡ User applied code within timeout! Sending immediate LSP feedback...`
      );

      // Store operations before clearing (in case we need fallback)
      const operationsToProcess = [...this._pendingOperations];

      // Clear the timeout
      clearTimeout(this._pendingFeedbackTimeout);
      this._pendingFeedbackTimeout = null;

      try {
        // Trigger immediate LSP feedback (synchronous)
        await this.triggerLSPFeedback(operationsToProcess, true); // true = immediate feedback
        console.log(`✅ Immediate LSP feedback sent successfully`);

        // Clear pending state only after successful immediate feedback
        this._pendingOperations = [];
      } catch (error) {
        console.error(`❌ Immediate LSP feedback failed:`, error);
        console.log(`🔄 FALLBACK: Sending queued LSP feedback instead...`);

        // CRITICAL: Fallback to queued feedback if immediate feedback fails
        try {
          await this.triggerLSPFeedback(operationsToProcess, false); // false = queued feedback
          console.log(`✅ Fallback queued LSP feedback sent successfully`);
        } catch (fallbackError) {
          console.error(
            `❌ Fallback queued LSP feedback also failed:`,
            fallbackError
          );
        }

        // Clear pending state after fallback attempt
        this._pendingOperations = [];
      }
    } else {
      // User applied code AFTER timeout - do not send any feedback
      console.log(
        `🚫 User applied code AFTER timeout expired - NOT sending LSP feedback to server`
      );
      console.log(
        `⏰ Timeout status: ${
          this._pendingFeedbackTimeout ? "ACTIVE" : "EXPIRED"
        }, Pending operations: ${this._pendingOperations.length}`
      );
    }
  }

  /**
   * Trigger LSP feedback analysis after code changes
   */
  private async triggerLSPFeedback(
    operations: any[],
    isImmediate: boolean = false
  ): Promise<void> {
    try {
      console.log("\n" + "🔍".repeat(20));
      console.log("🔍 **STARTING LSP FEEDBACK ANALYSIS**");
      console.log("🔍".repeat(20));

      // Use successfully edited files if available, otherwise extract from operations
      let changedFiles: string[];

      if (
        this.successfullyEditedFiles &&
        this.successfullyEditedFiles.length > 0
      ) {
        changedFiles = [...this.successfullyEditedFiles];
        console.log(
          `🔍 Using successfully edited files for LSP analysis: ${changedFiles.length} files`
        );
      } else {
        // Fallback: Extract changed files from operations - handle both file and file_path properties
        changedFiles = operations
          .map((op) => {
            // Try multiple possible file path properties
            const filePath = op.file || op.file_path || op.filePath;
            console.log(
              `🔍 Operation file extraction: op.file="${op.file}", op.file_path="${op.file_path}", op.filePath="${op.filePath}" → using: "${filePath}"`
            );
            return filePath;
          })
          .filter((file) => file && typeof file === "string");
      }

      console.log(
        `📁 **Analyzing ${changedFiles.length} changed files with LSP:**`
      );
      changedFiles.forEach((file, index) => {
        console.log(`   ${index + 1}. ${file}`);
      });

      // Get LSP diagnostics for changed files
      const lspResult = await this._lspService.getDiagnosticsForFiles(
        changedFiles
      );
      const fileErrors = lspResult.fileErrors;
      const hasFileAccessErrors = lspResult.hasFileAccessErrors;

      console.log(
        `📊 LSP analysis result: ${fileErrors.length} files with errors`
      );

      if (hasFileAccessErrors) {
        console.log(
          `⚠️ LSP analysis had file access errors - some files could not be analyzed`
        );
      }

      if (fileErrors.length > 0) {
        // Print nice formatted LSP feedback to console
        this.printLSPFeedbackToConsole(fileErrors);

        // Only send feedback to server if there are actual LSP errors, not file access errors
        if (!hasFileAccessErrors) {
          // Parse LSP feedback to clean string on client side
          const cleanedFeedback = this.parseLSPFeedbackToString(fileErrors);

          if (isImmediate) {
            console.log(
              `⚡ IMMEDIATE FEEDBACK: Sending synchronous LSP feedback to server...`
            );

            // Send immediate feedback to server (synchronous) - NO timestamp
            this._webSocketManager.send({
              type: "immediate_lsp_feedback",
              data: {
                cleaned_feedback: cleanedFeedback,
              },
            });
          } else {
            console.log(`🔄 QUEUED FEEDBACK: Sending LSP feedback to queue...`);

            // Send single clean feedback message to server (queued) - NO timestamp
            this._webSocketManager.send({
              type: "lsp_error_feedback",
              data: {
                project_name:
                  vscode.workspace.workspaceFolders?.[0]?.name || "unknown",
                question: cleanedFeedback,
              },
            });
          }
        } else {
          console.log(
            `🚫 NOT SENDING LSP FEEDBACK: File access errors detected - these are client-side path issues, not server problems`
          );
        }
      } else {
        if (hasFileAccessErrors) {
          console.log("\n" + "⚠️".repeat(20));
          console.log("⚠️ **LSP ANALYSIS INCOMPLETE - FILE ACCESS ERRORS**");
          console.log("⚠️ **SOME FILES COULD NOT BE ANALYZED**");
          console.log("⚠️".repeat(20) + "\n");

          if (isImmediate) {
            console.log(
              "⚡ FILE ACCESS ERRORS: Not sending immediate feedback - LSP could not analyze all files"
            );
          } else {
            console.log(
              "🔄 FILE ACCESS ERRORS: Not sending queued feedback - LSP could not analyze all files"
            );
          }
        } else {
          console.log("\n" + "✅".repeat(20));
          console.log("✅ **NO LSP ERRORS FOUND IN CHANGED FILES**");
          console.log("✅ **CODE QUALITY CHECK PASSED!**");
          console.log("✅".repeat(20) + "\n");

          if (isImmediate) {
            console.log(
              "⚡ NO ERRORS: Not sending immediate feedback - LSP found no issues"
            );
          } else {
            console.log(
              "🔄 NO ERRORS: Not sending queued feedback - LSP found no issues"
            );
          }
        }
      }
    } catch (error) {
      console.error("❌ Error during LSP feedback analysis:", error);
    }
  }

  /**
   * Send Agent mode failure feedback to server (only failures, not successes)
   * This is used when terminal commands or code operations fail in Agent mode
   */
  public async sendAgentModeFailureFeedback(
    failureType: "terminal_command" | "code_operation",
    failureMessage: string,
    context?: string
  ): Promise<void> {
    try {
      // Only send feedback if we're in Agent mode
      const isAgentMode = await this.isCurrentlyInAgentMode();
      if (!isAgentMode) {
        console.log(`🚫 Not in Agent mode - skipping failure feedback`);
        return;
      }

      console.log(`🤖 Agent mode failure detected: ${failureType}`);
      console.log(`   Error: ${failureMessage}`);
      if (context) {
        console.log(`   Context: ${context}`);
      }

      // Format the failure message for the server
      const formattedMessage = `Agent Mode ${failureType.replace(
        "_",
        " "
      )} Failure: ${failureMessage}${context ? ` (Context: ${context})` : ""}`;

      // Send failure feedback to server using the existing LSP feedback mechanism
      this._webSocketManager.send({
        type: "lsp_error_feedback",
        data: {
          project_name:
            vscode.workspace.workspaceFolders?.[0]?.name || "unknown",
          question: formattedMessage,
        },
      });

      console.log(`✅ Agent mode failure feedback sent to server`);
    } catch (error) {
      console.error(`❌ Failed to send Agent mode failure feedback:`, error);
    }
  }

  /**
   * Check if currently in Agent mode by querying the webview
   * For now, we'll use a simpler approach by checking the webview directly
   */
  private async isCurrentlyInAgentMode(): Promise<boolean> {
    try {
      // For now, we'll assume Agent mode if we're in an automated context
      // This can be enhanced later with proper webview communication
      // We'll use a simple heuristic: if there are pending operations being processed automatically
      return this._pendingOperations.length > 0;
    } catch (error) {
      console.error(`❌ Error checking Agent mode status:`, error);
      return false;
    }
  }

  private generateWorkspaceHash(workspacePath: string): string {
    // Simple hash function for workspace path
    let hash = 0;
    for (let i = 0; i < workspacePath.length; i++) {
      const char = workspacePath.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString();
  }

  private async handleOpenFile(filePath: string) {
    try {
      console.log(`📝 Opening file in editor: ${filePath}`);

      // Get the workspace folder
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        vscode.window.showErrorMessage("No workspace folder found");
        return;
      }

      // Create the full file URI
      const fileUri = vscode.Uri.joinPath(workspaceFolder.uri, filePath);

      // Check if file exists
      try {
        await vscode.workspace.fs.stat(fileUri);
      } catch (error) {
        vscode.window.showErrorMessage(`File not found: ${filePath}`);
        return;
      }

      // Open the file in the editor
      const document = await vscode.workspace.openTextDocument(fileUri);
      await vscode.window.showTextDocument(document, {
        preview: false,
        viewColumn: vscode.ViewColumn.One,
      });

      console.log(`✅ Successfully opened file: ${filePath}`);
    } catch (error) {
      console.error(`❌ Error opening file ${filePath}:`, error);
      vscode.window.showErrorMessage(`Failed to open file: ${filePath}`);
    }
  }

  private async handleExecuteTerminalCommand(command: string) {
    try {
      console.log(`⚡ Executing terminal command: ${command}`);

      // Get the workspace folder
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        vscode.window.showErrorMessage("No workspace folder found");
        return;
      }

      // Use Node.js child_process to execute commands directly
      const { exec } = require("child_process");
      const util = require("util");
      const execAsync = util.promisify(exec);

      try {
        console.log(
          `🔧 Executing command in directory: ${workspaceFolder.uri.fsPath}`
        );

        const { stdout, stderr } = await execAsync(command, {
          cwd: workspaceFolder.uri.fsPath,
          timeout: 30000, // 30 second timeout
          env: process.env,
        });

        if (stdout) {
          console.log(`📤 Command output: ${stdout}`);
        }

        if (stderr) {
          console.log(`⚠️ Command stderr: ${stderr}`);
        }

        console.log(`✅ Terminal command executed successfully: ${command}`);

        // Show output in VSCode output channel for user visibility
        const outputChannel =
          vscode.window.createOutputChannel("ReCode AI Commands");
        outputChannel.appendLine(`Command: ${command}`);
        outputChannel.appendLine(
          `Working Directory: ${workspaceFolder.uri.fsPath}`
        );
        if (stdout) outputChannel.appendLine(`Output: ${stdout}`);
        if (stderr) outputChannel.appendLine(`Stderr: ${stderr}`);
        outputChannel.appendLine("---");
        outputChannel.show(true);
      } catch (execError: any) {
        console.error(`❌ Command execution failed: ${command}`, execError);

        // Show detailed error information
        const errorMessage = execError.message || "Unknown error";
        const errorCode = execError.code || "Unknown code";

        console.error(
          `Command failed: ${command}\nError: ${errorMessage} (Code: ${errorCode})`
        );

        // Show error in output channel
        const outputChannel =
          vscode.window.createOutputChannel("ReCode AI Commands");
        outputChannel.appendLine(`FAILED Command: ${command}`);
        outputChannel.appendLine(
          `Working Directory: ${workspaceFolder.uri.fsPath}`
        );
        outputChannel.appendLine(`Error: ${errorMessage}`);
        outputChannel.appendLine(`Error Code: ${errorCode}`);
        if (execError.stdout)
          outputChannel.appendLine(`Stdout: ${execError.stdout}`);
        if (execError.stderr)
          outputChannel.appendLine(`Stderr: ${execError.stderr}`);
        outputChannel.appendLine("---");
        outputChannel.show(true);
      }
    } catch (error) {
      console.error(
        `❌ Error in handleExecuteTerminalCommand: ${command}`,
        error
      );
      console.error(`Failed to execute command: ${command}. Error: ${error}`);
    }
  }

  /**
   * Parse LSP feedback to clean, organized string format grouped by filename
   */
  private parseLSPFeedbackToString(fileErrors: LSPFileErrors[]): string {
    if (!fileErrors || fileErrors.length === 0) {
      return "FIX compilation issues - client LSP feedback received but no specific errors found";
    }

    const fileGroups: string[] = [];
    const fileAccessErrors: string[] = [];

    for (const fileError of fileErrors) {
      const fileName = fileError.relativeFilePath || "unknown file";

      // Check for file access errors first
      if (fileError.fileAccessError) {
        fileAccessErrors.push(`${fileName}: ${fileError.fileAccessError}`);
        continue;
      }

      const errors = fileError.errors || [];

      if (errors.length > 0) {
        const errorList: string[] = [];

        for (const error of errors) {
          const line = error.line || "?";
          const column = error.column || "?";
          const message = error.message || "unknown error";
          errorList.push(`Line ${line}:${column} - ${message}`);
        }

        // Group errors by filename
        fileGroups.push(`${fileName}: ${errorList.join(", ")}`);
      }
    }

    // Combine file access errors and regular errors
    const allErrorGroups = [...fileAccessErrors, ...fileGroups];

    if (allErrorGroups.length > 0) {
      if (fileAccessErrors.length > 0) {
        return `FIX these file access and compilation issues - ${allErrorGroups.join(
          " | "
        )}`;
      } else {
        return `FIX these compilation issues - ${allErrorGroups.join(" | ")}`;
      }
    } else {
      return "FIX compilation issues - client LSP feedback received but no specific errors found";
    }
  }

  /**
   * Print nicely formatted LSP feedback to console
   */
  private printLSPFeedbackToConsole(fileErrors: LSPFileErrors[]): void {
    console.log("\n" + "=".repeat(80));
    console.log("🔍 **LSP FEEDBACK ANALYSIS RESULTS**");
    console.log("=".repeat(80));

    let fileAccessErrorCount = 0;
    let regularErrorCount = 0;

    fileErrors.forEach((fileError, index) => {
      console.log(
        `\n📁 **FILE ${index + 1}/${fileErrors.length}: ${
          fileError.relativeFilePath
        }**`
      );
      console.log("-".repeat(60));

      // Check for file access errors first
      if (fileError.fileAccessError) {
        fileAccessErrorCount++;
        console.log(`\n   🚫 **FILE ACCESS ERROR**`);
        console.log(`   💬 **Message:** ${fileError.fileAccessError}`);
        console.log(`   🔧 **Source:** file-system`);
      } else {
        regularErrorCount++;
      }

      fileError.errors.forEach((error, errorIndex) => {
        const severity = error.severity.toUpperCase();
        const severityIcon = error.severity === "error" ? "❌" : "⚠️";

        console.log(`\n   ${severityIcon} **${severity} #${errorIndex + 1}**`);
        console.log(
          `   📍 **Location:** Line ${error.line}, Column ${error.column}`
        );
        console.log(`   💬 **Message:** ${error.message}`);

        if (error.source) {
          console.log(`   🔧 **Source:** ${error.source}`);
        }

        if (error.code) {
          console.log(`   🏷️  **Code:** ${error.code}`);
        }
      });

      console.log(
        `\n   📊 **Total Errors in File:** ${fileError.errors.length}`
      );
    });

    console.log("\n" + "=".repeat(80));
    if (fileAccessErrorCount > 0) {
      console.log(
        `🎯 **SUMMARY:** ${fileErrors.length} files with issues (${fileAccessErrorCount} file access errors, ${regularErrorCount} files with LSP errors)`
      );
    } else {
      console.log(
        `🎯 **SUMMARY:** ${fileErrors.length} files with errors, sending individual fix requests to server`
      );
    }
    console.log("=".repeat(80) + "\n");
  }

  /**
   * Dispose of resources
   */
  public dispose(): void {
    if (this._lspService) {
      this._lspService.dispose();
    }
  }
}
