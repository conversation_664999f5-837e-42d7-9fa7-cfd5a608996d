import * as vscode from "vscode";
import { WebSocketMessage, WebSocketManager } from "./WebSocketManager";
import { StateManager } from "./StateManager";
import { FileOperationsManager } from "./FileOperationsManager";
import { FileCommandManager } from "../services/FileCommandManager";
import {
  LanguageFeedbackManager,
  CompilationResult,
} from "../services/LanguageFeedbackManager";

export interface MessageSender {
  (message: any): void;
}

export class WebSocketMessageHandler {
  private _webSocketManager: WebSocketManager;
  private _stateManager: StateManager;
  private _fileOpsManager: FileOperationsManager;
  private _fileCommandManager: FileCommandManager;
  private _languageFeedbackManager?: LanguageFeedbackManager;
  private _sendMessage: MessageSender;
  private _compilationFeedbackSent: boolean = false;

  constructor(
    webSocketManager: WebSocketManager,
    stateManager: StateManager,
    fileOpsManager: FileOperationsManager,
    fileCommandManager: FileCommandManager,
    languageFeedbackManager: LanguageFeedbackManager | undefined,
    sendMessage: MessageSender
  ) {
    this._webSocketManager = webSocketManager;
    this._stateManager = stateManager;
    this._fileOpsManager = fileOpsManager;
    this._fileCommandManager = fileCommandManager;
    this._languageFeedbackManager = languageFeedbackManager;
    this._sendMessage = sendMessage;

    // Set up file operation callbacks
    this._fileOpsManager.setFileOperationCallback({
      onStart: (operation: string, filePath: string) => {
        this._sendMessage({
          command: "file_operation_start",
          operation: operation,
          filePath: filePath,
        });
      },
      onComplete: (
        filePath: string,
        success: boolean,
        actualFilePath?: string
      ) => {
        this._sendMessage({
          command: "file_operation_complete",
          filePath: filePath,
          success: success,
          actualFilePath: actualFilePath,
        });
      },
    });

    // Set this as the message handler for the WebSocket manager
    this._webSocketManager.setMessageHandler(this.handleMessage.bind(this));
  }

  public async handleMessage(message: WebSocketMessage): Promise<void> {
    const messageType = message.type;
    const messageData = message.data || {};

    switch (messageType) {
      case "console_output":
        // Stream console output to the webview
        console.log(`📡 Received console output: ${messageData.message}`);
        this._sendMessage({
          command: "consoleOutput",
          message: messageData.message,
          level: messageData.level,
          heading: messageData.heading,
        });

        // Auto-save state after console output (part of chat experience)
        this._stateManager.saveWebviewState();
        break;

      case "agent_status":
        // Update agent status in webview
        this._sendMessage({
          command: "agentStatus",
          status: messageData.status,
          details: messageData.details,
          animation: messageData.animation,
        });
        break;

      case "queue_status":
        // Handle queue status messages
        console.log(`📋 Queue status: ${messageData.status}`);
        this._sendMessage({
          command: "queueStatus",
          status: messageData.status,
          position: messageData.position,
          message: messageData.message,
        });
        break;

      case "agent_reasoning":
        // Display LLM reasoning in a professional way
        console.log(`🤖💭 Received agent reasoning:`, messageData);
        this._sendMessage({
          command: "agentReasoning",
          data: {
            next_tool: messageData.next_tool,
            reasoning: messageData.reasoning,
            professional_message: messageData.professional_message,
            timestamp: messageData.timestamp,
          },
        });
        break;

      case "task_list":
        // Forward task list to webview for collapsible display
        this._stateManager.setTaskList(messageData);
        this._sendMessage({
          command: "task_list",
          data: messageData,
        });
        break;

      case "execution_plan":
        // Forward execution plan to webview for display
        this._sendMessage({
          command: "execution_plan",
          data: messageData,
        });
        break;

      case "file_request":
        // Server is requesting a file from client
        await this.handleFileRequest(messageData.path);
        break;

      case "file_structure_request":
        // Server is requesting only file structure from client
        await this.handleFileStructureRequest(messageData.path);
        break;

      case "file_range_request":
        // Server is requesting a specific line range from a file
        await this.handleFileRangeRequest(
          messageData.path,
          messageData.line_from,
          messageData.line_to
        );
        break;

      case "dir_request":
        // Server is requesting directory listing
        await this.handleDirectoryRequest(messageData.path);
        break;

      case "file_operation":
        // Server is sending file operation to execute (legacy)
        await this.executeFileOperation(messageData);
        break;

      case "file_command":
        // Server is sending new file command to execute
        await this.handleFileCommand(messageData);
        break;

      case "file_version_request":
        // Server is requesting file version for conflict detection
        await this.handleFileVersionRequest(messageData.path);
        break;

      case "code_apply_request":
        // Server is requesting to apply code changes
        await this.handleCodeApplyRequest(messageData);
        break;

      case "coder_response":
        // Final response from coder agent
        this._sendMessage({
          command: "coderResponse",
          summary: messageData.summary,
          message: messageData.message,
          status: messageData.status,
        });

        // Auto-save state after server response
        this._stateManager.saveWebviewState();
        break;

      case "tool_status":
        // Forward tool status to webview for success/failure indicators
        console.log(
          `🔧 PROVIDER RECEIVED TOOL STATUS:`,
          JSON.stringify(messageData, null, 2)
        );

        this._stateManager.updateToolStatus(messageData.tool_name, messageData);
        this._sendMessage({
          command: "tool_status",
          data: messageData,
        });
        break;

      case "tasks_identified":
        // Tasks identified by request analyzer
        console.log(`📋 Tasks identified:`, messageData);
        this._sendMessage({
          command: "tasks_identified",
          data: messageData,
        });
        break;

      case "execution_plan_ready":
        // Execution plan ready from execution plan agent
        console.log(`📝 Execution plan ready:`, messageData);
        this._sendMessage({
          command: "execution_plan_ready",
          data: messageData,
        });
        break;

      case "file_operation_start":
        // File operation started
        console.log(`📝 File operation started:`, messageData);
        this._sendMessage({
          command: "file_operation_start",
          operation: messageData.operation,
          filePath: messageData.filePath,
        });
        break;

      case "file_operation_complete":
        // File operation completed
        console.log(`✅ File operation completed:`, messageData);
        this._sendMessage({
          command: "file_operation_complete",
          filePath: messageData.filePath,
          success: messageData.success,
        });
        break;

      case "immediate_feedback_ack":
        // Server acknowledges receipt of immediate LSP feedback
        console.log(
          `✅ Server acknowledged immediate LSP feedback: ${messageData.message}`
        );
        break;

      case "pong":
        // Keep-alive response
        break;

      case "error":
        // Forward structured error messages to the webview for user-friendly display and retry
        this._sendMessage({
          command: "error",
          message:
            messageData.message ||
            messageData.error ||
            "An unknown error occurred.",
        });
        break;

      default:
        console.warn(`⚠️ Unknown WebSocket message type: ${messageType}`);
    }
  }

  private async handleFileRequest(filePath: string) {
    try {
      const result = await this._fileOpsManager.handleFileRequest(filePath);
      this._webSocketManager.send({
        type: "file_response",
        data: result,
      });
    } catch (error) {
      console.error(`❌ Error handling file request for ${filePath}:`, error);
    }
  }

  private async handleFileStructureRequest(filePath: string) {
    try {
      console.log(`🏗️ Handling file structure request for: ${filePath}`);
      const result = await this._fileOpsManager.handleFileStructureRequest(
        filePath
      );

      // Log the result being sent to server
      if (result.success && result.structure_map) {
        console.log(
          `📤 Sending structure data to server for ${filePath}: ${result.structure_map.length} elements`
        );
      } else if (result.error) {
        console.log(
          `📤 Sending structure error to server for ${filePath}: ${result.error}`
        );
      }

      this._webSocketManager.send({
        type: "file_response",
        data: result,
      });
    } catch (error) {
      console.error(
        `❌ Error handling file structure request for ${filePath}:`,
        error
      );
      // Send error response to server
      this._webSocketManager.send({
        type: "file_response",
        data: {
          path: filePath,
          error: `Failed to get file structure: ${
            error instanceof Error ? error.message : String(error)
          }`,
          success: false,
        },
      });
    }
  }

  private async handleFileRangeRequest(
    filePath: string,
    lineFrom: number,
    lineTo: number
  ) {
    try {
      console.log(
        `📏 Handling file range request: ${filePath} lines ${lineFrom}-${lineTo}`
      );
      const result = await this._fileOpsManager.handleFileRangeRequest(
        filePath,
        lineFrom,
        lineTo
      );
      this._webSocketManager.send({
        type: "file_response",
        data: result,
      });
    } catch (error) {
      console.error(
        `❌ Error handling file range request for ${filePath} lines ${lineFrom}-${lineTo}:`,
        error
      );
      this._webSocketManager.send({
        type: "file_response",
        data: {
          path: filePath,
          content: "",
          error: `Error reading file range: ${error}`,
          success: false,
        },
      });
    }
  }

  private async handleDirectoryRequest(dirPath: string) {
    try {
      const result = await this._fileOpsManager.handleDirectoryRequest(dirPath);
      this._webSocketManager.send({
        type: "dir_response",
        data: result,
      });
    } catch (error) {
      console.error(
        `❌ Error handling directory request for ${dirPath}:`,
        error
      );
    }
  }

  private async handleFileCommand(commandData: any) {
    try {
      console.log(
        `🔧 Received file command: ${commandData.type} for ${commandData.file_path}`
      );

      // Execute the command using FileCommandManager
      const acknowledgment = await this._fileCommandManager.executeCommand(
        commandData
      );

      // Send acknowledgment back to server
      this._webSocketManager.send({
        type: "command_ack",
        data: acknowledgment,
      });

      console.log(
        `📤 Sent acknowledgment for command ${commandData.command_id}: ${acknowledgment.status}`
      );

      // Log file operation results (no VSCode notifications to avoid webview focus loss)
      if (acknowledgment.status === "success") {
        if (commandData.type === "create_file") {
          console.log(`✅ Created file: ${commandData.file_path}`);
        } else if (commandData.type === "delete_file") {
          console.log(`✅ Deleted file: ${commandData.file_path}`);
        }
      } else if (acknowledgment.status === "conflict") {
        console.warn(`⚠️ File conflict: ${acknowledgment.message}`);
      } else if (acknowledgment.status === "failure") {
        console.error(`❌ File operation failed: ${acknowledgment.message}`);
      }
    } catch (error) {
      console.error("❌ Error handling file command:", error);

      // Send error acknowledgment
      this._webSocketManager.send({
        type: "command_ack",
        data: {
          command_id: commandData.command_id || "unknown",
          status: "failure",
          message: `Error handling command: ${
            error instanceof Error ? error.message : String(error)
          }`,
          timestamp: Date.now(),
          error_details: error instanceof Error ? error.stack : String(error),
        },
      });
    }
  }

  private async handleFileVersionRequest(filePath: string) {
    try {
      console.log(`📋 Received file version request for: ${filePath}`);

      // Get file version using FileCommandManager
      const version = await this._fileCommandManager.getFileVersion(filePath);

      // Send version response back to server
      this._webSocketManager.send({
        type: "file_version_response",
        data: {
          path: filePath,
          version: version,
        },
      });

      console.log(
        `📤 Sent file version for ${filePath}: ${version || "not found"}`
      );
    } catch (error) {
      console.error(
        `❌ Error handling file version request for ${filePath}:`,
        error
      );

      // Send error response
      this._webSocketManager.send({
        type: "file_version_response",
        data: {
          path: filePath,
          version: null,
          error: error instanceof Error ? error.message : String(error),
        },
      });
    }
  }

  private async handleCodeApplyRequest(requestData: any) {
    try {
      console.log("🔧 Received code apply request from server");

      // Reset compilation feedback flag for new code application
      this._compilationFeedbackSent = false;

      const operations = requestData.operations || [];

      if (operations.length === 0) {
        console.log("⚠️ No operations to apply");
        return;
      }

      // Show code changes approval UI in the extension sidebar
      this.showCodeApprovalUI(operations);
    } catch (error) {
      console.error("❌ Error handling code apply request:", error);
    }
  }

  private showCodeApprovalUI(operations: any[]) {
    // Store the pending approval in state
    this._stateManager.setPendingCodeApproval({
      operations: operations,
      operationCount: operations.length,
    });

    // Send the code approval UI to the webview with agent mode info
    this._sendMessage({
      command: "showCodeApproval",
      data: {
        operations: operations,
        operationCount: operations.length,
        isAgentMode: this.isAgentMode(), // Add agent mode flag
      },
    });
  }

  private isAgentMode(): boolean {
    // Get the current chat mode from the state manager
    try {
      const webviewState = this._stateManager.getWebviewState();
      const chatMode = webviewState?.chatMode || "agent"; // Default to agent mode
      console.log(
        `🤖 WebSocketMessageHandler: Detected chat mode: ${chatMode}`
      );
      return chatMode === "agent";
    } catch (error) {
      console.log("Could not determine chat mode, defaulting to agent mode");
      return true; // Default to agent mode as per requirements
    }
  }

  private async executeFileOperation(operation: any) {
    try {
      const { operation: opType, file_path, content } = operation;

      switch (opType) {
        case "write":
          await this.writeFileFromServer(file_path, content);
          break;
        case "delete":
          await this.deleteFile(file_path);
          break;
        default:
          console.warn(`Unknown file operation: ${opType}`);
      }
    } catch (error) {
      console.error("❌ Error executing file operation:", error);
    }
  }

  private async writeFileFromServer(filePath: string, content: string) {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error("No workspace found");
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, filePath);

      // Create directory if it doesn't exist
      const dirUri = vscode.Uri.joinPath(fileUri, "..");
      await vscode.workspace.fs.createDirectory(dirUri);

      // Write the file
      const encoder = new TextEncoder();
      await vscode.workspace.fs.writeFile(fileUri, encoder.encode(content));

      // 💾 AUTO-SAVE: Automatically save the file after code changes
      await this.autoSaveFile(fileUri, filePath);

      console.log(`✅ File written and saved by server: ${filePath}`);
    } catch (error) {
      console.error(`❌ Failed to write file ${filePath}:`, error);
      throw error;
    }
  }

  private async deleteFile(filePath: string) {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error("No workspace found");
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const fileUri = vscode.Uri.joinPath(workspaceRoot, filePath);

      await vscode.workspace.fs.delete(fileUri);

      console.log(`✅ Deleted file: ${filePath}`);
    } catch (error) {
      console.error(`❌ Failed to delete file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * 💾 SAVE ALL MODIFIED FILES: Save all modified files before LSP feedback
   */
  private async saveAllModifiedFiles(changedFiles: string[]): Promise<void> {
    console.log(
      `💾 Saving ${changedFiles.length} modified files before LSP feedback...`
    );

    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      console.log("⚠️ No workspace folders found for saving files");
      return;
    }

    const workspaceRoot = workspaceFolders[0].uri;
    const savePromises = changedFiles.map(async (filePath) => {
      try {
        const fileUri = vscode.Uri.joinPath(workspaceRoot, filePath);
        await this.autoSaveFile(fileUri, filePath);
        console.log(`💾 Saved file before LSP: ${filePath}`);
      } catch (error) {
        console.error(`❌ Failed to save file before LSP ${filePath}:`, error);
      }
    });

    await Promise.all(savePromises);
    console.log(
      `💾 All ${changedFiles.length} files saved before LSP feedback`
    );
  }

  /**
   * 💾 AUTO-SAVE: Automatically save file after code changes (both Agent and Human-in-the-loop modes)
   */
  private async autoSaveFile(fileUri: vscode.Uri, filePath: string) {
    try {
      // Check if the file is currently open in an editor
      const document = vscode.workspace.textDocuments.find(
        (doc) => doc.uri.toString() === fileUri.toString()
      );

      if (document) {
        // File is open in editor - save it through the document
        if (document.isDirty) {
          await document.save();
          console.log(`💾 Auto-saved open document: ${filePath}`);
        } else {
          console.log(`💾 Document already saved: ${filePath}`);
        }
      } else {
        // File is not open - open it briefly to trigger save
        try {
          const doc = await vscode.workspace.openTextDocument(fileUri);
          if (doc.isDirty) {
            await doc.save();
            console.log(`💾 Auto-saved closed document: ${filePath}`);
          }
        } catch (openError) {
          // If we can't open the document, the file write itself should be sufficient
          console.log(
            `💾 File written to disk (no editor save needed): ${filePath}`
          );
        }
      }
    } catch (error) {
      console.error(`❌ Failed to auto-save file ${filePath}:`, error);
      // Don't throw - file write was successful, save failure shouldn't break the operation
    }
  }

  /**
   * Trigger language-specific compilation feedback after code changes are applied
   */
  public async triggerLanguageFeedback(operations: any[]) {
    console.log(
      "🔍 triggerLanguageFeedback called with operations:",
      operations
    );

    if (!this._languageFeedbackManager) {
      console.log("⚠️ Language feedback manager not initialized");
      return;
    }

    // Prevent multiple compilation feedback from being sent
    if (this._compilationFeedbackSent) {
      console.log("⚠️ Compilation feedback already sent, skipping");
      return;
    }

    try {
      console.log("🔍 Starting language feedback analysis...");
      this._compilationFeedbackSent = true;

      // Extract changed files from operations - handle both file and file_path properties
      const changedFiles = operations
        .map((op) => {
          // Try multiple possible file path properties
          const filePath = op.file || op.file_path || op.filePath;
          return filePath;
        })
        .filter((file) => file && typeof file === "string");

      console.log(
        `📁 Analyzing ${changedFiles.length} changed files:`,
        changedFiles
      );

      // 💾 CRITICAL: Save all modified files before LSP feedback (especially important for Agent mode)
      await this.saveAllModifiedFiles(changedFiles);

      // Get compilation feedback
      const compilationResult: CompilationResult =
        await this._languageFeedbackManager.getCompilationFeedback(
          changedFiles
        );

      console.log(`📊 Compilation result for ${compilationResult.language}:`, {
        success: compilationResult.success,
        errors: compilationResult.errors.length,
        warnings: compilationResult.warnings.length,
      });

      console.log(`📊 Detailed compilation result:`, compilationResult);
      console.log(`📊 Errors array:`, compilationResult.errors);
      console.log(`📊 Warnings array:`, compilationResult.warnings);

      // Log compilation feedback locally (no longer sending to server)
      console.log(`📊 Local compilation feedback:`, {
        language: compilationResult.language,
        success: compilationResult.success,
        errors: compilationResult.errors.length,
        warnings: compilationResult.warnings.length,
        output: compilationResult.output,
        timestamp: compilationResult.timestamp,
        changed_files: changedFiles,
      });

      // Handle user action prompts locally in the client
      if (
        compilationResult.requiresUserAction &&
        compilationResult.userActionCommands
      ) {
        console.log(
          "🔧 Compilation requires user action, showing terminal commands..."
        );
        await this.handleUserActionPrompt(compilationResult);
      } else if (
        compilationResult.userActionMessage &&
        compilationResult.userActionCommands
      ) {
        console.log(
          "✅ Compilation successful, showing testing suggestions..."
        );
        await this.handleUserActionPrompt(compilationResult);
      }

      // Compilation results are now shown in the webview via showUserActionPrompt
      // No VSCode native notifications to avoid webview focus loss
      console.log(`📊 Compilation completed - results sent to webview:`, {
        success: compilationResult.success,
        errors: compilationResult.errors.length,
        warnings: compilationResult.warnings.length,
        language: compilationResult.language,
      });
    } catch (error) {
      console.error("❌ Error during language feedback analysis:", error);

      // Log error locally (no longer sending to server)
      console.error("❌ Local compilation feedback error:", {
        language: "unknown",
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: Date.now(),
      });

      // Error logged to console only - no VSCode notification to avoid webview focus loss
      console.error(
        "❌ Language feedback analysis failed - check console for details"
      );
    } finally {
      // Reset flag after completion (success or error)
      setTimeout(() => {
        this._compilationFeedbackSent = false;
      }, 1000);
    }
  }

  /**
   * Handle user action prompts for Maven/Gradle installation or testing
   */
  private async handleUserActionPrompt(compilationResult: CompilationResult) {
    if (
      !compilationResult.userActionMessage ||
      !compilationResult.userActionCommands
    ) {
      return;
    }

    const message = compilationResult.userActionMessage;
    const commands = compilationResult.userActionCommands;

    // Filter commands based on current platform
    const platform = process.platform as "darwin" | "linux" | "win32";
    const relevantCommands = commands.filter(
      (cmd) => cmd.platform === "all" || cmd.platform === platform
    );

    if (relevantCommands.length === 0) {
      // Just show info message instead of QuickPick to avoid webview refresh
      vscode.window.showInformationMessage(message);
      return;
    }

    // Instead of showing QuickPick dialog which causes webview to refresh,
    // send the user action commands to the webview for display
    // Add a small delay to ensure any VSCode notifications are shown first
    console.log(
      "🔧 Sending user action commands to webview instead of showing QuickPick"
    );
    setTimeout(() => {
      // Update activity timestamp to prevent UI reset during user action prompt
      this._stateManager.updateActivity();

      this._sendMessage({
        command: "showUserActionPrompt",
        data: {
          message: message,
          commands: relevantCommands,
          compilationResult: {
            success: compilationResult.success,
            language: compilationResult.language,
            errors: compilationResult.errors,
            warnings: compilationResult.warnings,
          },
        },
      });
    }, 100); // Small delay to avoid timing conflicts with VSCode notifications
  }
}
