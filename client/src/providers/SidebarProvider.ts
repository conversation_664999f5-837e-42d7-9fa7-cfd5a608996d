import * as vscode from "vscode";
import { v4 as uuidv4 } from "uuid";
import { indexingService } from "../indexing";
import { getDiffSyncInterval } from "../indexing/utils";
import {
  FileCommandManager,
  FileCommand,
  CommandAcknowledgment,
} from "../services/FileCommandManager";
import {
  LanguageFeedbackManager,
  CompilationResult,
} from "../services/LanguageFeedbackManager";

// Import our new modular components
import { WebSocketManager } from "./WebSocketManager";
import { StateManager } from "./StateManager";
import { FileOperationsManager } from "./FileOperationsManager";
import { HTMLTemplateManager } from "./HTMLTemplateManager";
import { MessageHandler } from "./MessageHandler";
import { WebSocketMessageHandler } from "./WebSocketMessageHandler";
import { WorkspaceIndexingManager } from "./WorkspaceIndexingManager";

export class SidebarProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = "recode-sidebar";

  private _view?: vscode.WebviewView;
  private readonly _extensionUri: vscode.Uri;
  private _context: vscode.ExtensionContext;
  private _diffSyncTimer?: NodeJS.Timeout;
  private _summaryGenerated: boolean = false;
  private _isInitialCreation: boolean = true;

  // Modular components
  private _webSocketManager: WebSocketManager;
  private _stateManager: StateManager;
  private _fileOpsManager: FileOperationsManager;
  private _htmlTemplateManager: HTMLTemplateManager;
  private _messageHandler: MessageHandler;
  private _webSocketMessageHandler: WebSocketMessageHandler;
  private _fileCommandManager: FileCommandManager;
  private _languageFeedbackManager?: LanguageFeedbackManager;
  private _workspaceIndexingManager: WorkspaceIndexingManager;

  constructor(
    private readonly extensionUri: vscode.Uri,
    context: vscode.ExtensionContext
  ) {
    this._extensionUri = extensionUri;
    this._context = context;

    // Initialize modular components
    const clientId = this.generateClientId();
    this._webSocketManager = new WebSocketManager(clientId);
    this._stateManager = new StateManager(context);
    this._fileOpsManager = new FileOperationsManager();
    this._htmlTemplateManager = new HTMLTemplateManager(extensionUri);
    this._fileCommandManager = new FileCommandManager();
    this._workspaceIndexingManager = new WorkspaceIndexingManager(context);

    // Initialize LanguageFeedbackManager with workspace root
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders && workspaceFolders.length > 0) {
      this._languageFeedbackManager = new LanguageFeedbackManager(
        workspaceFolders[0].uri.fsPath
      );
      console.log(
        `🔍 LanguageFeedbackManager initialized with workspace: ${workspaceFolders[0].uri.fsPath}`
      );
    } else {
      console.log(
        "⚠️ No workspace folders found - LanguageFeedbackManager not initialized"
      );
    }

    // Initialize WebSocket message handler first
    this._webSocketMessageHandler = new WebSocketMessageHandler(
      this._webSocketManager,
      this._stateManager,
      this._fileOpsManager,
      this._fileCommandManager,
      this._languageFeedbackManager,
      this.sendMessage.bind(this)
    );

    // Initialize message handler with WebSocket message handler reference
    this._messageHandler = new MessageHandler(
      this._webSocketManager,
      this._stateManager,
      this._fileOpsManager,
      this._fileCommandManager,
      this._webSocketMessageHandler,
      this._workspaceIndexingManager,
      this.sendMessage.bind(this)
    );

    // Connect MessageHandler to FileOperationsManager for Agent mode failure feedback
    this._fileOpsManager.setMessageHandler(this._messageHandler);

    // Debug initial state
    console.log("🔧 SidebarProvider constructor - Initial state:", {
      stateManager: this._stateManager.getStateSummary(),
    });

    // Note: checkIndexingStatus() will be called after webview is initialized

    // Listen for active editor changes to update file tabs
    vscode.window.onDidChangeActiveTextEditor((editor) => {
      this.updateActiveFileTab(editor);
    });

    vscode.workspace.onDidChangeWorkspaceFolders((event) => {
      console.log("🔄 SidebarProvider: Workspace folders changed:", {
        added: event.added.length,
        removed: event.removed.length,
      });

      // Only reset state and send workspaceChanged if actual workspace folders were added/removed
      if (event.added.length > 0 || event.removed.length > 0) {
        console.log(
          "🔄 SidebarProvider: Resetting state due to folder add/remove"
        );
        this._stateManager.resetIndexingState();
        this.sendMessage({
          command: "workspaceChanged",
        });
        // Check indexing status for the new workspace
        setTimeout(() => {
          console.log("🔄 Checking indexing status for new workspace...");
          this.checkIndexingStatus();
        }, 200);
      } else {
        console.log(
          "🔄 SidebarProvider: Ignoring workspace change - no folders added/removed"
        );
      }
    });
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;
  }

  private async checkIndexingStatus() {
    console.log("🔍 === BACKEND WORKSPACE INDEXING CHECK - START ===");

    // Small delay to ensure workspace is fully loaded
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Check if we have workspace folders
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      console.log("⚠️ No workspace folders found!");
      this.sendMessage({
        command: "showIndexingScreen",
        error:
          "No workspace folder is open. Please open a folder to use the AI Coding Assistant.",
      });
      return;
    }

    const workspaceName = workspaceFolders[0].name;
    console.log("📁 Workspace folder found:", workspaceName);
    console.log("📁 Workspace path:", workspaceFolders[0].uri.fsPath);

    try {
      // Check backend for workspace indexing status
      console.log("🌐 Checking backend for workspace indexing status...");
      console.log(
        `🌐 Backend URL: http://127.0.0.1:8000/workspace/status/${workspaceName}`
      );

      const response = await fetch(
        `http://127.0.0.1:8000/workspace/status/${workspaceName}`
      );

      console.log(`🌐 Backend response status: ${response.status}`);
      console.log(`🌐 Backend response ok: ${response.ok}`);

      if (!response.ok) {
        throw new Error(
          `Backend request failed: ${response.status} ${response.statusText}`
        );
      }

      const workspaceStatus = await response.json();
      console.log(
        "🔍 Backend workspace status:",
        JSON.stringify(workspaceStatus, null, 2)
      );

      const isIndexed = workspaceStatus.indexed;
      console.log(`🔍 Backend says workspace indexed: ${isIndexed}`);
      console.log(
        `🔍 Backend file count: ${workspaceStatus.file_count || "N/A"}`
      );

      if (isIndexed) {
        console.log(
          `🔍 ✅ SHOWING CHAT INTERFACE - workspace indexed with ${
            workspaceStatus.file_count || 0
          } files`
        );
        this.sendMessage({ command: "showChatInterface" });
        // Start scheduler since indexing is complete
        this.startDifferentialSyncScheduler();
      } else {
        console.log(
          "🔍 ❌ SHOWING INDEXING SCREEN - workspace not indexed in backend"
        );
        this.sendMessage({ command: "showIndexingScreen" });
      }
    } catch (error) {
      console.error("❌ Failed to check backend workspace status:", error);
      console.log("🔄 Falling back to local workspace indexing manager...");

      // Fallback to local system
      const shouldShowIndexing =
        this._workspaceIndexingManager.shouldShowIndexingScreen();
      console.log(
        `🔍 Local fallback decision: shouldShowIndexing = ${shouldShowIndexing}`
      );

      if (shouldShowIndexing) {
        console.log("🔍 ❌ SHOWING INDEXING SCREEN - local fallback");
        this.sendMessage({ command: "showIndexingScreen" });
      } else {
        console.log("🔍 ✅ SHOWING CHAT INTERFACE - local fallback");
        this.sendMessage({ command: "showChatInterface" });
        this.startDifferentialSyncScheduler();
      }
    }
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ) {
    console.log("🔄 resolveWebviewView - START");
    console.log("🔄 Current state before webview creation:", {
      stateManager: this._stateManager.getStateSummary(),
    });

    this._view = webviewView;

    // Mark webview as visible
    this._stateManager.setWebviewVisible(true);

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        vscode.Uri.joinPath(this._extensionUri, "out"),
        vscode.Uri.joinPath(this._extensionUri, "dist"),
        vscode.Uri.joinPath(this._extensionUri, "src", "providers", "css"),
      ],
    };

    webviewView.webview.html = this._htmlTemplateManager.getHtmlForWebview(
      webviewView.webview
    );

    // Handle webview disposal (when user switches away)
    webviewView.onDidDispose(() => {
      console.log("🗑️ Webview disposed - saving state...");
      this._stateManager.setWebviewVisible(false);
    });

    // Send initial state after a short delay to ensure webview is ready
    setTimeout(() => {
      // DEBUG: Check what state we have
      const restoreData = this._stateManager.getWebviewRestoreData();
      console.log("🔍 INITIAL WEBVIEW CREATION DEBUG:", {
        chatHistory: restoreData.chatHistory.length,
        currentScreen: restoreData.currentScreen,
        sessionId: restoreData.sessionId,
        webviewVisible: restoreData.webviewVisible,
      });

      // On initial webview creation (refresh/restart), clear all old state and start fresh
      console.log(
        "🔄 Initial webview creation - clearing old state for fresh start..."
      );
      this._stateManager.resetIndexingState();

      // Clear webview state as well
      this.sendMessage({ command: "clearAllState" });

      // Check indexing status for current workspace
      console.log("🔄 Checking indexing status for fresh start...");
      this.checkIndexingStatus();

      // Mark initial creation as complete after a delay
      setTimeout(() => {
        this._isInitialCreation = false;
        console.log(
          "🔄 Initial creation complete - enabling state restoration"
        );
      }, 1000);
    }, 100);

    // Enhanced visibility change handler - preserve chat messages when switching back
    webviewView.onDidChangeVisibility(() => {
      if (webviewView.visible) {
        console.log("🔄 Webview became visible");
        this._stateManager.setWebviewVisible(true);

        if (!this._isInitialCreation) {
          // Check if we have existing chat messages to restore
          const restoreData = this._stateManager.getWebviewRestoreData();
          console.log("🔄 Visibility change - checking state:", {
            chatMessages: restoreData.chatHistory.length,
            currentScreen: restoreData.currentScreen,
            isIndexed: restoreData.isIndexed,
          });

          if (restoreData.chatHistory.length > 0) {
            // We have chat messages - restore complete state instead of just checking indexing
            console.log(
              "🔄 Restoring complete state with existing chat messages..."
            );
            setTimeout(() => {
              this.restoreWebviewState();
            }, 100);
          } else {
            // No chat messages - check indexing status as before
            console.log("🔄 No chat messages - checking indexing status...");
            setTimeout(() => {
              this.checkIndexingStatus();
            }, 100);
          }
        } else {
          console.log("🔄 Skipping during initial creation");
        }
      } else {
        console.log("👁️ Webview hidden - saving state...");
        this._stateManager.setWebviewVisible(false);
      }
    });

    webviewView.webview.onDidReceiveMessage(async (data) => {
      await this._messageHandler.handleWebviewMessage(data);
    });
  }

  public sendMessage(message: any) {
    if (this._view && this._view.webview) {
      const isVisible = this._view.visible;
      console.log(
        `📤 Sending message to webview (visible: ${isVisible}):`,
        message.command || message.type
      );

      try {
        this._view.webview.postMessage(message);
      } catch (error) {
        console.error("❌ Error sending message to webview:", error);
        return;
      }

      // Persist certain state changes
      this.persistStateIfNeeded(message);

      // If webview is not visible but we're sending important updates, log it
      if (
        !isVisible &&
        (message.command === "addMessage" ||
          message.command === "updateStatus" ||
          message.type === "console_output")
      ) {
        console.log(
          "📤 Important message sent to hidden webview - will be processed when visible"
        );
      }
    } else {
      console.error("❌ Cannot send message: webview not available");
      // Store message for later if webview becomes available
      if (
        message.command === "showIndexingScreen" ||
        message.command === "showChatInterface"
      ) {
        console.log(
          "📝 Storing UI state message for when webview becomes available"
        );
        // We'll send this message when webview becomes visible
        setTimeout(() => {
          if (this._view && this._view.webview) {
            console.log(
              "🔄 Retrying stored message after webview became available"
            );
            this.sendMessage(message);
          }
        }, 500);
      }
    }
  }

  private persistStateIfNeeded(message: any) {
    // Update persisted state based on message type
    console.log(
      "💾 persistStateIfNeeded - message:",
      message.command || message.type
    );

    switch (message.command || message.type) {
      case "addChatMessage":
        this._stateManager.addChatMessage(message.message);
        this._stateManager.saveWebviewState(); // Auto-save on chat messages
        break;
      case "updateMode":
        this._stateManager.updateState({ currentMode: message.mode });
        console.log("💾 Updated mode to:", message.mode);
        break;
      case "connectionStatus":
        this._stateManager.updateState({ isConnected: message.connected });
        console.log("💾 Updated connection status to:", message.connected);
        break;
      case "indexingComplete":
        this._stateManager.setIndexingComplete();
        this._stateManager.updateCurrentScreen("chat");
        // Transition to chat interface after indexing completes
        console.log("✅ Indexing completed - transitioning to chat interface");

        // Small delay to ensure webview is ready for the transition
        setTimeout(() => {
          this.sendMessage({ command: "showChatInterface" });
          console.log("📱 Sent showChatInterface command to webview");
        }, 100);

        // Start scheduler since indexing is complete
        this.startDifferentialSyncScheduler();
        break;
      case "showIndexingScreen":
        this._stateManager.updateCurrentScreen("indexing");
        break;
      case "showChatInterface":
        this._stateManager.updateCurrentScreen("chat");
        break;
      case "tool_status":
        // Persist tool status updates
        if (message.data && message.data.tool_name) {
          this._stateManager.updateToolStatus(
            message.data.tool_name,
            message.data
          );
        }
        break;
      case "console_output":
        // Persist processing/thinking messages
        this._stateManager.addProcessingMessage(message);
        break;
      case "task_list":
        // Persist task list updates
        this._stateManager.setTaskList(message.data);
        break;
      case "showCodeApproval":
        // Persist pending code approval prompt
        this._stateManager.setPendingCodeApproval(message.data);
        break;
      case "queue_status":
        // Persist queue status
        this._stateManager.updateQueueStatus(message.data);
        break;
      case "aiResponse":
      case "response":
        // Persist server responses
        this._stateManager.updateLastServerResponse(message);
        break;
      case "process_started":
        // Track active processes
        this._stateManager.addActiveProcess({
          id: message.processId || Date.now().toString(),
          type: message.processType || "unknown",
          startTime: Date.now(),
          data: message.data,
        });
        break;
      case "process_completed":
        // Remove completed processes
        if (message.processId) {
          this._stateManager.removeActiveProcess(message.processId);
        }
        break;
    }

    console.log(
      "💾 Current persisted state summary:",
      this._stateManager.getStateSummary()
    );
  }

  private restoreWebviewState() {
    const restoreData = this._stateManager.getWebviewRestoreData();

    console.log("🔄 Restoring complete webview state:", {
      chatMessages: restoreData.chatHistory.length,
      processingMessages: restoreData.processingMessages.length,
      activeProcesses: restoreData.activeProcesses.length,
      currentScreen: restoreData.currentScreen,
      sessionId: restoreData.sessionId,
    });

    // Send comprehensive state restoration
    this.sendMessage({
      command: "restoreCompleteState",
      restoreData: {
        // Core state
        ...restoreData,
        summaryGenerated: this._summaryGenerated,

        // Additional UI state
        scrollPosition: restoreData.scrollPosition,
        inputValue: restoreData.inputValue,

        // Timestamp for debugging
        restoredAt: Date.now(),
      },
    });

    // Also send the current active file
    this.updateActiveFileTab(vscode.window.activeTextEditor);

    console.log("✅ Complete webview state restoration sent to webview");
  }

  private syncLatestUpdates() {
    console.log("🔄 Syncing latest updates after becoming visible");

    // Check if there's an active WebSocket connection
    if (this._webSocketManager.isConnected()) {
      console.log(
        "🔄 WebSocket is active - processing should continue seamlessly"
      );

      // Send a ping to ensure connection is alive
      this._webSocketManager.ping();
    } else {
      console.log("🔄 No active WebSocket connection");
    }

    // DEBUG: Check current state before sending syncComplete
    const restoreData = this._stateManager.getWebviewRestoreData();
    console.log("🔍 DEBUG syncLatestUpdates - Current state:", {
      chatHistory: restoreData.chatHistory.length,
      currentScreen: restoreData.currentScreen,
      hasMessages: restoreData.chatHistory.length > 0,
    });

    // Ensure any pending UI updates are processed
    this.sendMessage({
      command: "syncComplete",
      timestamp: Date.now(),
    });

    console.log("📤 Sent syncComplete command to webview");
  }

  /**
   * Start the differential sync scheduler
   */
  private startDifferentialSyncScheduler() {
    // Clear any existing timer
    if (this._diffSyncTimer) {
      clearInterval(this._diffSyncTimer);
    }

    console.log("🔄 Starting differential sync scheduler...");

    // Get sync interval from configuration
    const syncInterval = getDiffSyncInterval();
    console.log(
      `⏱️ Differential sync interval: ${syncInterval / 60000} minutes`
    );

    this._diffSyncTimer = setInterval(async () => {
      try {
        console.log("⏰ Running scheduled differential sync...");

        // Send progress update to webview
        this.sendMessage({
          command: "syncProgress",
          message: "Running scheduled differential sync...",
        });

        await indexingService.performDifferentialSync();

        // Send completion message to webview
        this.sendMessage({
          command: "syncComplete",
          message: "Differential sync completed successfully",
        });
      } catch (error) {
        console.error("Scheduled differential sync failed:", error);
        // Don't show error messages for scheduled syncs to avoid spam
        // Just log the error
      }
    }, syncInterval);

    // Also run an initial sync after 30 seconds
    setTimeout(async () => {
      try {
        console.log("🚀 Running initial differential sync...");
        await indexingService.performDifferentialSync();
      } catch (error) {
        console.error("Initial differential sync failed:", error);
      }
    }, 30000); // 30 seconds
  }

  /**
   * Stop the differential sync scheduler
   */
  private stopDifferentialSyncScheduler() {
    if (this._diffSyncTimer) {
      clearInterval(this._diffSyncTimer);
      this._diffSyncTimer = undefined;
      console.log("⏹️ Differential sync scheduler stopped");
    }
  }

  private updateActiveFileTab(editor: vscode.TextEditor | undefined) {
    if (!this._view || !editor) {
      return;
    }

    const fileName = editor.document.fileName;
    const baseName =
      fileName.split("/").pop() || fileName.split("\\").pop() || "Unknown";

    // Send the active file info to the webview
    this.sendMessage({
      command: "updateActiveFile",
      fileName: baseName,
      filePath: fileName,
    });
  }

  /**
   * Dispose method to clean up resources
   */
  public dispose() {
    this.stopDifferentialSyncScheduler();
    this._webSocketManager.disconnect();
  }
}
