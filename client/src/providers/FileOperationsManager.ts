import * as vscode from "vscode";
import * as fs from "fs";
import * as os from "os";
import { MessageHandler } from "./MessageHandler";

export interface FileOperation {
  file?: string;
  action: string;
  start_line?: number;
  end_line?: number;
  replacement?: string;
  commands?: string[]; // For directory terminal operations
}

interface FileOperationCallback {
  onStart?: (operation: string, filePath: string) => void;
  onComplete?: (
    filePath: string,
    success: boolean,
    actualFilePath?: string
  ) => void;
}

export class FileOperationsManager {
  private fileOperationCallback?: FileOperationCallback;
  private _messageHandler?: MessageHandler;

  public setFileOperationCallback(callback: FileOperationCallback) {
    this.fileOperationCallback = callback;
  }

  public setMessageHandler(messageHandler: MessageHandler) {
    this._messageHandler = messageHandler;
  }

  /**
   * Extract document symbols using LSP and convert to compact structure_map format
   * ALWAYS returns an array - never undefined
   */
  private async extractDocumentStructure(
    fileUri: vscode.Uri
  ): Promise<Array<{ line: number; element: string }>> {
    try {
      console.log(`🔍 Extracting document structure for: ${fileUri.fsPath}`);

      // Request document symbols from LSP
      const symbols = await vscode.commands.executeCommand<
        vscode.DocumentSymbol[]
      >("vscode.executeDocumentSymbolProvider", fileUri);

      if (!symbols || symbols.length === 0) {
        console.log(
          `📄 No symbols found for: ${fileUri.fsPath} - returning empty structure_map`
        );
        return []; // Always return array, never undefined
      }

      console.log(`🔍 Found ${symbols.length} top-level symbols`);

      // Convert symbols to compact structure_map format
      const structureMap: Array<{ line: number; element: string }> = [];
      this.processSymbolsRecursively(symbols, structureMap);

      // Sort by line number
      structureMap.sort((a, b) => a.line - b.line);

      console.log(
        `📋 Generated structure_map with ${structureMap.length} elements`
      );

      // Log detailed structure_map content
      console.log(`📋 Structure map details for ${fileUri.fsPath}:`);
      structureMap.slice(0, 10).forEach((element, index) => {
        console.log(`  ${index + 1}. Line ${element.line}: ${element.element}`);
      });
      if (structureMap.length > 10) {
        console.log(`  ... and ${structureMap.length - 10} more elements`);
      }

      return structureMap;
    } catch (error) {
      console.warn(
        `⚠️ Failed to extract document structure for ${fileUri.fsPath}: ${error}`
      );
      console.log(`📄 Returning empty structure_map due to LSP error`);
      return []; // Always return array, never undefined
    }
  }

  /**
   * Recursively process document symbols and add to structure_map
   */
  private processSymbolsRecursively(
    symbols: vscode.DocumentSymbol[],
    structureMap: Array<{ line: number; element: string }>,
    parentName?: string
  ): void {
    for (const symbol of symbols) {
      const symbolKindName = this.getSymbolKindName(symbol.kind);
      const elementName = parentName
        ? `${parentName}.${symbol.name}`
        : symbol.name;

      // Add start marker
      structureMap.push({
        line: symbol.range.start.line + 1, // Convert to 1-based line numbers
        element: `${elementName} (${symbolKindName}) start`,
      });

      // Process children recursively
      if (symbol.children && symbol.children.length > 0) {
        this.processSymbolsRecursively(
          symbol.children,
          structureMap,
          elementName
        );
      }

      // Add end marker
      structureMap.push({
        line: symbol.range.end.line + 1, // Convert to 1-based line numbers
        element: `${elementName} (${symbolKindName}) end`,
      });
    }
  }

  /**
   * Convert VSCode SymbolKind enum to readable string
   */
  private getSymbolKindName(kind: vscode.SymbolKind): string {
    const kindMap: { [key: number]: string } = {
      [vscode.SymbolKind.File]: "file",
      [vscode.SymbolKind.Module]: "module",
      [vscode.SymbolKind.Namespace]: "namespace",
      [vscode.SymbolKind.Package]: "package",
      [vscode.SymbolKind.Class]: "class",
      [vscode.SymbolKind.Method]: "method",
      [vscode.SymbolKind.Property]: "property",
      [vscode.SymbolKind.Field]: "field",
      [vscode.SymbolKind.Constructor]: "constructor",
      [vscode.SymbolKind.Enum]: "enum",
      [vscode.SymbolKind.Interface]: "interface",
      [vscode.SymbolKind.Function]: "function",
      [vscode.SymbolKind.Variable]: "variable",
      [vscode.SymbolKind.Constant]: "constant",
      [vscode.SymbolKind.String]: "string",
      [vscode.SymbolKind.Number]: "number",
      [vscode.SymbolKind.Boolean]: "boolean",
      [vscode.SymbolKind.Array]: "array",
      [vscode.SymbolKind.Object]: "object",
      [vscode.SymbolKind.Key]: "key",
      [vscode.SymbolKind.Null]: "null",
      [vscode.SymbolKind.EnumMember]: "enum_member",
      [vscode.SymbolKind.Struct]: "struct",
      [vscode.SymbolKind.Event]: "event",
      [vscode.SymbolKind.Operator]: "operator",
      [vscode.SymbolKind.TypeParameter]: "type_parameter",
    };

    return kindMap[kind] || "unknown";
  }

  public async handleFileRequest(filePath: string): Promise<{
    path: string;
    content: string;
    structure_map?: Array<{ line: number; element: string }>;
    error?: string;
    success: boolean;
  }> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return {
          path: filePath,
          content: "",
          error: "No workspace found",
          success: false,
        };
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const workspaceRootPath = workspaceRoot.fsPath;

      // Handle both absolute and relative paths properly
      let fileUri: vscode.Uri;
      if (filePath.startsWith("/") || filePath.match(/^[A-Za-z]:/)) {
        // Absolute path - check if it's within workspace
        if (filePath.startsWith(workspaceRootPath)) {
          // Path is within workspace, use as-is
          fileUri = vscode.Uri.file(filePath);
          console.log(`📁 Using absolute path within workspace: ${filePath}`);
        } else {
          // Path is outside workspace - this shouldn't happen, but handle gracefully
          console.warn(`⚠️ Absolute path outside workspace: ${filePath}`);
          fileUri = vscode.Uri.file(filePath);
        }
      } else {
        // Relative path - but check if it starts with workspace folder name
        let cleanFilePath = filePath;
        const workspaceFolderName = workspaceRoot.path.split("/").pop();

        if (
          workspaceFolderName &&
          filePath.startsWith(workspaceFolderName + "/")
        ) {
          // Remove the workspace folder name from the beginning of the path
          cleanFilePath = filePath.substring(workspaceFolderName.length + 1);
          console.log(
            `📁 Removed workspace folder prefix: ${filePath} -> ${cleanFilePath}`
          );
        }

        fileUri = vscode.Uri.joinPath(workspaceRoot, cleanFilePath);
        console.log(`📁 Using relative path: ${filePath}`);
        console.log(`📁 Cleaned path: ${cleanFilePath}`);
        console.log(`📁 Workspace root: ${workspaceRootPath}`);
        console.log(`📁 Final file URI: ${fileUri.toString()}`);
        console.log(`📁 Final file path: ${fileUri.fsPath}`);
      }

      try {
        console.log(`📁 Attempting to read file: ${fileUri.fsPath}`);
        const fileContent = await vscode.workspace.fs.readFile(fileUri);
        const content = new TextDecoder().decode(fileContent);

        // Extract document structure using LSP - ALWAYS include structure_map
        const structureMap = await this.extractDocumentStructure(fileUri);
        const finalStructureMap = structureMap || []; // Ensure it's never undefined

        console.log(
          `📁 Sent file to server: ${filePath} (${content.length} chars, ${finalStructureMap.length} structure elements)`
        );

        return {
          path: filePath,
          content: content,
          structure_map: finalStructureMap, // Always include, even if empty
          success: true,
        };
      } catch (error) {
        console.log(`❌ File not found or error reading: ${filePath}`, error);

        // Try to provide more helpful debugging info
        try {
          const parentDir = vscode.Uri.joinPath(fileUri, "..");
          const parentContents = await vscode.workspace.fs.readDirectory(
            parentDir
          );
          console.log(
            `📁 Parent directory contents:`,
            parentContents.map(
              ([name, type]) =>
                `${name} (${
                  type === vscode.FileType.Directory ? "dir" : "file"
                })`
            )
          );
        } catch (dirError) {
          console.log(`❌ Could not read parent directory:`, dirError);
        }

        return {
          path: filePath,
          content: "",
          error: "File not found",
          success: false,
        };
      }
    } catch (error) {
      console.error(`❌ Error handling file request for ${filePath}:`, error);
      
      // Send Agent mode failure feedback if applicable
      if (this._messageHandler) {
        await this._messageHandler.sendAgentModeFailureFeedback(
          "code_operation",
          `Failed to read file: ${filePath}`,
          `Error: ${error instanceof Error ? error.message : String(error)}`
        );
      }
      
      return {
        path: filePath,
        content: "",
        error: "Error reading file",
        success: false,
      };
    }
  }

  public async handleFileRangeRequest(
    filePath: string,
    lineFrom: number,
    lineTo: number
  ): Promise<{
    path: string;
    content: string;
    structure_map?: Array<{ line: number; element: string }>;
    error?: string;
    success: boolean;
  }> {
    try {
      console.log(
        `📏 FileOperationsManager: Reading file range ${filePath} lines ${lineFrom}-${lineTo}`
      );

      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return {
          path: filePath,
          content: "",
          error: "No workspace found",
          success: false,
        };
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const workspaceRootPath = workspaceRoot.fsPath;

      // Handle both absolute and relative paths properly
      let fileUri: vscode.Uri;
      if (filePath.startsWith("/") || filePath.match(/^[A-Za-z]:/)) {
        // Absolute path - check if it's within workspace
        if (filePath.startsWith(workspaceRootPath)) {
          // Path is within workspace, use as-is
          fileUri = vscode.Uri.file(filePath);
          console.log(`📁 Using absolute path within workspace: ${filePath}`);
        } else {
          // Path is outside workspace - this shouldn't happen, but handle gracefully
          console.warn(`⚠️ Absolute path outside workspace: ${filePath}`);
          fileUri = vscode.Uri.file(filePath);
        }
      } else {
        // Relative path - but check if it starts with workspace folder name
        let cleanFilePath = filePath;
        const workspaceFolderName = workspaceRoot.path.split("/").pop();

        if (
          workspaceFolderName &&
          filePath.startsWith(workspaceFolderName + "/")
        ) {
          // Remove the workspace folder name from the beginning of the path
          cleanFilePath = filePath.substring(workspaceFolderName.length + 1);
          console.log(
            `📁 Removed workspace folder prefix: ${filePath} -> ${cleanFilePath}`
          );
        }

        fileUri = vscode.Uri.joinPath(workspaceRoot, cleanFilePath);
        console.log(`📁 Using relative path: ${filePath}`);
        console.log(`📁 Cleaned path: ${cleanFilePath}`);
        console.log(`📁 Workspace root: ${workspaceRootPath}`);
        console.log(`📁 Final file URI: ${fileUri.toString()}`);
        console.log(`📁 Final file path: ${fileUri.fsPath}`);
      }

      try {
        console.log(`📁 Attempting to read file: ${fileUri.fsPath}`);
        const fileContent = await vscode.workspace.fs.readFile(fileUri);
        const content = new TextDecoder().decode(fileContent);
        const lines = content.split("\n");

        // Validate and handle line range gracefully
        const totalLines = lines.length;

        // Handle edge cases gracefully
        let startLine = Math.max(1, lineFrom);
        let endLine = Math.max(startLine, lineTo);

        // If requested range exceeds file length, adjust gracefully
        if (startLine > totalLines) {
          console.warn(
            `⚠️ Requested start line ${startLine} exceeds file length ${totalLines}, returning entire file`
          );
          startLine = 1;
          endLine = totalLines;
        } else if (endLine > totalLines) {
          console.warn(
            `⚠️ Requested end line ${endLine} exceeds file length ${totalLines}, adjusting to ${totalLines}`
          );
          endLine = totalLines;
        }

        // Handle empty files
        if (totalLines === 0) {
          console.warn(`⚠️ File ${filePath} is empty`);
          return {
            path: filePath,
            content: "1: ",
            success: true,
          };
        }

        // Extract the requested line range (convert to 0-based indexing)
        const extractedLines = lines.slice(startLine - 1, endLine);

        // Format with line numbers (using original line numbers)
        const numberedLines = extractedLines.map(
          (line, index) => `${startLine + index}: ${line}`
        );
        const numberedContent = numberedLines.join("\n");

        // Extract document structure using LSP - ALWAYS include structure_map
        const structureMap = await this.extractDocumentStructure(fileUri);
        const finalStructureMap = structureMap || []; // Ensure it's never undefined

        console.log(
          `📏 Extracted file range: ${filePath} lines ${startLine}-${endLine} (${extractedLines.length} lines, ${numberedContent.length} chars, ${finalStructureMap.length} structure elements)`
        );

        // Log if we had to adjust the range
        if (startLine !== lineFrom || endLine !== lineTo) {
          console.log(
            `📏 Range adjusted from requested ${lineFrom}-${lineTo} to actual ${startLine}-${endLine}`
          );
        }

        return {
          path: filePath,
          content: numberedContent,
          structure_map: finalStructureMap, // Always include, even if empty
          success: true,
        };
      } catch (error) {
        console.log(`❌ File not found or error reading: ${filePath}`, error);

        // Try to provide more helpful debugging info
        try {
          const parentDir = vscode.Uri.joinPath(fileUri, "..");
          const parentContents = await vscode.workspace.fs.readDirectory(
            parentDir
          );
          console.log(
            `📁 Parent directory contents:`,
            parentContents.map(
              ([name, type]) =>
                `${name} (${
                  type === vscode.FileType.Directory ? "dir" : "file"
                })`
            )
          );
        } catch (dirError) {
          console.log(`❌ Could not read parent directory:`, dirError);
        }

        return {
          path: filePath,
          content: "",
          error: "File not found or could not be read",
          success: false,
        };
      }
    } catch (error) {
      console.error(
        `❌ Error handling file range request for ${filePath} lines ${lineFrom}-${lineTo}:`,
        error
      );
      return {
        path: filePath,
        content: "",
        error: "Error reading file range",
        success: false,
      };
    }
  }

  public async handleDirectoryRequest(
    dirPath: string
  ): Promise<{ path: string; files: any[]; error?: string; success: boolean }> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return {
          path: dirPath,
          files: [],
          error: "No workspace found",
          success: false,
        };
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const dirUri = vscode.Uri.joinPath(workspaceRoot, dirPath);

      try {
        // Build complete directory tree recursively
        const completeTree = await this.buildCompleteDirectoryTree(
          dirUri,
          dirPath
        );

        console.log(
          `📂 Sent complete directory tree to server: ${dirPath} (${completeTree.length} total items)`
        );
        return { path: dirPath, files: completeTree, success: true };
      } catch (error) {
        return {
          path: dirPath,
          files: [],
          error: "Directory not found",
          success: false,
        };
      }
    } catch (error) {
      console.error(
        `❌ Error handling directory request for ${dirPath}:`,
        error
      );
      return {
        path: dirPath,
        files: [],
        error: "Error reading directory",
        success: false,
      };
    }
  }

  private async buildCompleteDirectoryTree(
    dirUri: vscode.Uri,
    relativePath: string,
    maxDepth: number = 10,
    currentDepth: number = 0
  ): Promise<any[]> {
    const result: any[] = [];

    // Prevent infinite recursion
    if (currentDepth >= maxDepth) {
      return result;
    }

    try {
      const entries = await vscode.workspace.fs.readDirectory(dirUri);

      for (const [name, type] of entries) {
        // Skip hidden files and common ignore patterns
        if (this.shouldSkipFile(name)) {
          continue;
        }

        const itemPath =
          relativePath === "." ? name : `${relativePath}/${name}`;
        const itemUri = vscode.Uri.joinPath(dirUri, name);

        if (type === vscode.FileType.Directory) {
          // Add directory entry
          result.push({
            name: name,
            type: "directory",
            path: itemPath,
          });

          // Recursively get subdirectory contents
          const subItems = await this.buildCompleteDirectoryTree(
            itemUri,
            itemPath,
            maxDepth,
            currentDepth + 1
          );
          result.push(...subItems);
        } else {
          // Add file entry
          result.push({
            name: name,
            type: "file",
            path: itemPath,
          });
        }
      }
    } catch (error) {
      console.warn(`Could not read directory: ${dirUri.fsPath}`, error);
    }

    return result;
  }

  private shouldSkipFile(name: string): boolean {
    // Skip common files/directories that should be ignored
    const skipPatterns = [
      ".DS_Store",
      ".git",
      "node_modules",
      ".vscode",
      "dist",
      "build",
      ".next",
      ".nuxt",
      "coverage",
      ".nyc_output",
      "*.log",
      ".env",
      ".env.local",
      ".env.development.local",
      ".env.test.local",
      ".env.production.local",
    ];

    return skipPatterns.some((pattern) => {
      if (pattern.includes("*")) {
        const regex = new RegExp(pattern.replace("*", ".*"));
        return regex.test(name);
      }
      return name === pattern;
    });
  }

  public async applyCodeOperation(operation: FileOperation): Promise<boolean> {
    try {
      const { file, action, start_line, end_line, replacement, commands } =
        operation;

      console.log(
        `🎯 [${new Date().toISOString()}] FileOpsManager: 🚀 STARTING CODE OPERATION`
      );
      console.log(
        `🎯 [${new Date().toISOString()}] FileOpsManager: 🚀 Action: ${action}`
      );

      // Notify start of file operation
      if (file && this.fileOperationCallback?.onStart) {
        this.fileOperationCallback.onStart(action, file);
      }

      // Handle terminal/CMD operations (directory, file, move, copy, delete, etc.)
      if (
        action === "terminal_command" ||
        action === "create_directory_terminal"
      ) {
        console.log(
          `💻 Terminal operation with ${commands?.length || 0} commands`
        );
        // Execute the commands (approval already handled by WebSocket approval system)
        return await this.handleTerminalOperation(commands || []);
      }

      console.log(`🚀 File: ${file}`);
      console.log(`🚀 Lines: ${start_line}-${end_line}`);
      console.log(
        `🚀 Replacement length: ${replacement ? replacement.length : 0}`
      );

      // Validate file path for file operations
      if (!file) {
        throw new Error("File path is required for file operations");
      }

      // CRITICAL FIX: Handle both absolute and relative paths properly
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error("No workspace found");
      }

      const workspaceRoot = workspaceFolders[0].uri;
      const workspaceRootPath = workspaceRoot.fsPath;

      console.log(`📁 Original file path: ${file}`);
      console.log(`📁 Workspace root: ${workspaceRootPath}`);

      let fileUri: vscode.Uri;
      let cleanFilePath: string;

      // Check if the path is absolute
      if (file.startsWith("/") || file.match(/^[A-Za-z]:/)) {
        // Absolute path - check if it's within workspace
        if (file.startsWith(workspaceRootPath)) {
          // Path is within workspace, use as-is
          fileUri = vscode.Uri.file(file);
          // Convert to relative path for display
          cleanFilePath = file.substring(workspaceRootPath.length + 1);
          console.log(`📁 Using absolute path within workspace: ${file}`);
          console.log(`📁 Relative path for display: ${cleanFilePath}`);
        } else {
          // Path is outside workspace - this shouldn't happen, but handle gracefully
          console.warn(`⚠️ Absolute path outside workspace: ${file}`);
          fileUri = vscode.Uri.file(file);
          cleanFilePath = file;
        }
      } else {
        // Relative path - clean and join with workspace root
        cleanFilePath = file;

        // Remove leading "./" if present
        if (cleanFilePath.startsWith("./")) {
          cleanFilePath = cleanFilePath.substring(2);
        }

        fileUri = vscode.Uri.joinPath(workspaceRoot, cleanFilePath);
        console.log(`📁 Using relative path: ${cleanFilePath}`);
      }

      console.log(`📁 File URI: ${fileUri.toString()}`);

      // Create WorkspaceEdit
      const edit = new vscode.WorkspaceEdit();

      if (action === "delete") {
        // CRITICAL FIX: Delete specific lines, NOT the entire file!
        if (start_line && end_line) {
          console.log(
            `🗑️ Deleting lines ${start_line}-${end_line} in file: ${cleanFilePath}`
          );

          // Convert to 0-based indexing for VSCode API
          const startPos = new vscode.Position(start_line - 1, 0);
          const endPos = new vscode.Position(end_line, 0);
          const range = new vscode.Range(startPos, endPos);

          // Delete the specific line range
          edit.delete(fileUri, range);
        } else {
          console.error(
            `❌ Delete operation missing line numbers for file: ${cleanFilePath}`
          );
          console.error(
            `❌ This would have deleted the entire file! Operation aborted.`
          );
          return false;
        }
      } else if (action === "edit") {
        return await this.handleEditOperation(
          fileUri,
          cleanFilePath,
          start_line!,
          end_line!,
          replacement || "",
          edit
        );
      } else if (action === "create") {
        console.log(
          `📝 CREATE operation: Inserting new content at lines ${start_line}-${end_line} in file: ${cleanFilePath}`
        );
        // CREATE means INSERT new content at specified lines within existing file
        return await this.handleEditOperation(
          fileUri,
          cleanFilePath,
          start_line!,
          end_line!,
          replacement || "",
          edit
        );
      }

      // Apply the edit (common for delete and edit actions)
      console.log(
        `🎯 [${new Date().toISOString()}] FileOpsManager: 🔄 Applying WorkspaceEdit...`
      );
      console.log(
        `🎯 [${new Date().toISOString()}] FileOpsManager: 🔄 Edit details:`,
        {
          fileUri: fileUri.toString(),
          hasReplace: edit.has(fileUri),
          editSize: edit.size,
        }
      );

      try {
        console.log(
          `🎯 [${new Date().toISOString()}] FileOpsManager: 🔄 Applying the edit now...`
        );
        const success = await vscode.workspace.applyEdit(edit);
        console.log(`📊 WorkspaceEdit result: ${success}`);

        if (success) {
          console.log(
            `✅ Applied code operation: ${action} on ${cleanFilePath} (lines ${start_line}-${end_line})`
          );

          // Automatically save the file after applying changes
          console.log(
            `🎯 [${new Date().toISOString()}] FileOpsManager: 💾 About to save file: ${cleanFilePath}`
          );
          await this.saveFile(fileUri, cleanFilePath);
          console.log(
            `🎯 [${new Date().toISOString()}] FileOpsManager: 💾 File save completed: ${cleanFilePath}`
          );

          // Notify completion of file operation
          if (file && this.fileOperationCallback?.onComplete) {
            this.fileOperationCallback.onComplete(file, true, fileUri.fsPath);
          }

          return true;
        } else {
          console.error(
            `❌ Failed to apply code operation: ${action} on ${cleanFilePath} - VSCode rejected the edit`
          );
          console.error(
            `❌ This could be due to invalid range or file conflicts`
          );

          // Notify completion of file operation (failed)
          if (file && this.fileOperationCallback?.onComplete) {
            this.fileOperationCallback.onComplete(file, false);
          }

          return false;
        }
      } catch (editError) {
        console.error(
          `❌ Exception during WorkspaceEdit.applyEdit:`,
          editError
        );

        // Notify completion of file operation (failed) - use operation.file since file might not be in scope
        if (operation.file && this.fileOperationCallback?.onComplete) {
          this.fileOperationCallback.onComplete(operation.file, false);
        }

        return false;
      }
    } catch (error) {
      console.error(`❌ Error applying code operation:`, error);
      
      // Send Agent mode failure feedback if applicable
      if (this._messageHandler && operation.file) {
        await this._messageHandler.sendAgentModeFailureFeedback(
          "code_operation",
          `Failed to apply code operation to file: ${operation.file}`,
          `Error: ${error instanceof Error ? error.message : String(error)}`
        );
      }

      // Notify completion of file operation (failed) - use operation.file since file might not be in scope
      if (operation.file && this.fileOperationCallback?.onComplete) {
        this.fileOperationCallback.onComplete(operation.file, false);
      }

      return false;
    }
  }

  private async handleEditOperation(
    fileUri: vscode.Uri,
    cleanFilePath: string,
    start_line: number,
    end_line: number,
    replacement: string,
    edit: vscode.WorkspaceEdit
  ): Promise<boolean> {
    // Check if file exists, create if it doesn't
    try {
      await vscode.workspace.fs.stat(fileUri);
      console.log(`📁 File exists: ${cleanFilePath}`);
    } catch {
      // File doesn't exist - first try to find it in workspace
      console.log(
        `📝 File doesn't exist at expected location: ${cleanFilePath}`
      );

      // Try to find the file in the workspace
      const foundFile = await this.findFileInWorkspace(cleanFilePath);
      if (foundFile) {
        console.log(`📁 Found existing file at: ${foundFile.fsPath}`);
        // Use the found file instead
        return await this.handleEditOperation(
          foundFile,
          foundFile.fsPath,
          start_line,
          end_line,
          replacement,
          edit
        );
      }

      // CRITICAL: Client should NEVER create new files - this is server's responsibility
      console.error(
        `❌ CRITICAL ERROR: File not found and client cannot create files: ${cleanFilePath}`
      );
      console.error(
        `❌ The server should only send operations for existing files!`
      );
      console.error(
        `❌ This indicates a server-side issue with file path resolution.`
      );

      // Show error to user
      vscode.window.showErrorMessage(
        `File not found: ${cleanFilePath}. The server attempted to edit a non-existent file. This should not happen.`
      );

      return false;
    }

    // Open the document to get current content and validate line numbers
    console.log(`📖 Opening document: ${fileUri.toString()}`);
    const document = await vscode.workspace.openTextDocument(fileUri);
    const totalLines = document.lineCount;

    console.log(`📄 VSCode Document info:`);
    console.log(`  - Line count: ${totalLines}`);
    console.log(
      `📄 Document has ${totalLines} lines, editing lines ${start_line}-${end_line}`
    );
    console.log(`📄 Original file content preview (first 3 lines):`);
    for (let i = 0; i < Math.min(3, totalLines); i++) {
      console.log(`  Line ${i + 1}: "${document.lineAt(i).text}"`);
    }

    // Validate and adjust line numbers for server-client sync issues
    let adjustedStartLine = start_line;
    let adjustedEndLine = end_line;

    if (adjustedStartLine < 1) {
      throw new Error(`Invalid start_line ${adjustedStartLine}. Must be >= 1.`);
    }

    if (adjustedStartLine > totalLines) {
      console.warn(
        `⚠️ Server requested start_line ${adjustedStartLine} but document only has ${totalLines} lines`
      );

      // Check if this is an APPEND operation
      if (adjustedStartLine === totalLines + 1) {
        console.log(
          `🔧 APPEND OPERATION DETECTED: Server wants to add new lines starting at ${adjustedStartLine}`
        );
      } else {
        adjustedStartLine = totalLines;
        console.log(
          `🔧 Adjusted start_line from ${start_line} to ${adjustedStartLine}`
        );
      }
    }

    if (adjustedEndLine > totalLines) {
      if (adjustedStartLine === totalLines + 1) {
        console.log(
          `🔧 APPEND OPERATION: Allowing end_line ${adjustedEndLine} beyond current file length`
        );
        // For append operations, keep the original end_line
      } else {
        console.warn(
          `⚠️ Server requested end_line ${adjustedEndLine} but document only has ${totalLines} lines`
        );
        console.log(
          `🔧 This appears to be a replacement operation that extends beyond file end`
        );
        console.log(
          `🔧 Will replace from line ${adjustedStartLine} to end of file and append new content`
        );
        // Don't adjust adjustedEndLine here - handle it in the position calculation
      }
    }

    // Replace lines (convert to 0-based indexing)
    const startPos = new vscode.Position(adjustedStartLine - 1, 0);
    let endPos: vscode.Position;

    if (adjustedStartLine === adjustedEndLine) {
      // Single line replacement - replace the entire line
      if (adjustedEndLine <= totalLines) {
        // Replace from start of line to start of next line (or end of file)
        if (adjustedEndLine < totalLines) {
          endPos = new vscode.Position(adjustedEndLine, 0);
        } else {
          // Last line - replace to end of line
          const lastLineLength = document.lineAt(adjustedEndLine - 1).text
            .length;
          endPos = new vscode.Position(adjustedEndLine - 1, lastLineLength);
        }
      } else {
        // Beyond file end
        const lastLineLength = document.lineAt(totalLines - 1).text.length;
        endPos = new vscode.Position(totalLines - 1, lastLineLength);
      }
    } else {
      // Multi-line replacement - replace from start_line to end_line (inclusive)
      if (adjustedEndLine <= totalLines) {
        // Normal case: end position should be start of line AFTER the last line to replace
        // This makes the replacement inclusive of the end_line
        if (adjustedEndLine < totalLines) {
          // We can safely go to the start of the next line
          endPos = new vscode.Position(adjustedEndLine, 0); // Start of line after end_line
        } else {
          // Replacing up to the last line - go to end of last line
          // adjustedEndLine == totalLines means we're replacing the last line
          const lastLineLength = document.lineAt(adjustedEndLine - 1).text
            .length;
          endPos = new vscode.Position(adjustedEndLine - 1, lastLineLength);
        }
      } else {
        // Server wants to replace beyond file end - replace to end of file
        console.log(
          `🔧 End line ${adjustedEndLine} exceeds file length ${totalLines}, replacing to end of file`
        );
        const lastLineLength = document.lineAt(totalLines - 1).text.length;
        endPos = new vscode.Position(totalLines - 1, lastLineLength);
      }
    }

    const range = new vscode.Range(startPos, endPos);

    console.log(
      `🎯 Replacing range: Line ${adjustedStartLine} Col 0 to Line ${
        endPos.line + 1
      } Col ${endPos.character}`
    );
    console.log(
      `🎯 VSCode Range: Position(${startPos.line}, ${startPos.character}) to Position(${endPos.line}, ${endPos.character})`
    );

    // Show what content we're actually replacing
    try {
      const rangeToReplace = new vscode.Range(startPos, endPos);
      const originalContent = document.getText(rangeToReplace);
      console.log(`📄 Original content being replaced:`);
      console.log(`"${originalContent}"`);
      console.log(`📄 Original content lines:`);
      originalContent.split("\n").forEach((line, index) => {
        console.log(`  Line ${startPos.line + index + 1}: "${line}"`);
      });
    } catch (rangeError) {
      console.error(`❌ Could not read original content:`, rangeError);
    }

    // Validate the range - allow append operations where startPos.line equals totalLines
    if (startPos.line > totalLines) {
      console.error(
        `❌ Invalid start position: startPos.line=${startPos.line}, totalLines=${totalLines}`
      );
      return false;
    }

    // endPos can be equal to totalLines for append operations, but not greater
    if (endPos.line > totalLines) {
      console.error(
        `❌ Invalid end position: endPos.line=${endPos.line}, totalLines=${totalLines}`
      );
      return false;
    }

    // Additional validation: check if we can actually access the lines
    try {
      if (startPos.line < totalLines) {
        const startLine = document.lineAt(startPos.line);
        console.log(
          `✅ Start line ${startPos.line + 1} exists: "${startLine.text}"`
        );
      }
      if (endPos.line < totalLines) {
        const endLine = document.lineAt(endPos.line);
        console.log(`✅ End line ${endPos.line + 1} exists: "${endLine.text}"`);
      } else if (endPos.line === totalLines) {
        console.log(
          `✅ End position is at end of file (line ${endPos.line + 1})`
        );
      }
    } catch (lineError) {
      console.error(`❌ Error accessing lines for validation:`, lineError);
      return false;
    }

    if (range.isEmpty) {
      console.warn(`⚠️ Range is empty - this might not work as expected`);
    }

    let finalReplacement = replacement || "";

    // Handle replacement content properly for append operations
    if (adjustedStartLine > totalLines) {
      console.log(
        `🔧 APPEND OPERATION: Adding newline prefix to ensure proper line separation`
      );
      if (!finalReplacement.startsWith("\n")) {
        finalReplacement = "\n" + finalReplacement;
      }
    }

    // CRITICAL FIX: Only add newline if we're replacing to the middle of the file
    // AND the original content at the end position starts with a newline
    // This preserves the original file structure without adding extra newlines
    if (
      end_line <= totalLines &&
      adjustedEndLine < totalLines &&
      !finalReplacement.endsWith("\n")
    ) {
      // Check if the line we're replacing TO has content after it
      try {
        const lineAfterReplacement = document.lineAt(adjustedEndLine);
        if (lineAfterReplacement.text.trim().length > 0) {
          // There's content after our replacement, so we need a newline separator
          finalReplacement += "\n";
          console.log(
            `➕ Added newline to separate replacement from following content: "${lineAfterReplacement.text}"`
          );
        } else {
          console.log(
            `🔧 No newline added - following line is empty or whitespace: "${lineAfterReplacement.text}"`
          );
        }
      } catch (lineError) {
        // If we can't read the following line, don't add newline
        console.log(
          `🔧 Could not read line after replacement, not adding newline`
        );
      }
    }

    console.log(
      `🔧 Final replacement content:`,
      JSON.stringify(finalReplacement)
    );
    console.log(`🔧 Final replacement content lines:`);
    finalReplacement.split("\n").forEach((line, index) => {
      console.log(`  New Line ${index + 1}: "${line}"`);
    });
    console.log(
      `🔧 Replacement ends with newline: ${finalReplacement.endsWith("\n")}`
    );
    console.log(`🔧 Replacement length: ${finalReplacement.length} characters`);

    // Test: Create a simple edit to verify the range works
    console.log(`🧪 Testing range validity by creating WorkspaceEdit...`);
    try {
      edit.replace(fileUri, range, finalReplacement);
      console.log(`✅ WorkspaceEdit.replace() succeeded - range is valid`);
    } catch (replaceError) {
      console.error(`❌ WorkspaceEdit.replace() failed:`, replaceError);
      return false;
    }

    console.log(`🔄 Applying the edit now...`);
    console.log(`🔄 Edit details:`, {
      fileUri: fileUri.toString(),
      hasReplace: edit.has(fileUri),
      editSize: edit.size,
    });

    // Apply the edit directly here instead of returning to main logic
    try {
      const success = await vscode.workspace.applyEdit(edit);
      console.log(`📊 WorkspaceEdit result: ${success}`);

      if (success) {
        console.log(
          `✅ Applied edit operation on ${cleanFilePath} (lines ${start_line}-${end_line})`
        );

        // Automatically save the file after applying changes
        await this.saveFile(fileUri, cleanFilePath);
        return true;
      } else {
        console.error(
          `❌ Failed to apply edit operation on ${cleanFilePath} - VSCode rejected the edit`
        );
        console.error(
          `❌ This could be due to invalid range or file conflicts`
        );
        return false;
      }
    } catch (editError) {
      console.error(`❌ Exception during WorkspaceEdit.applyEdit:`, editError);
      return false;
    }
  }

  private async findFileInWorkspace(
    fileName: string
  ): Promise<vscode.Uri | null> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return null;
      }

      // Extract just the filename from the path
      const justFileName = fileName.split("/").pop() || fileName;
      console.log(`🔍 Searching for file: ${justFileName} in workspace`);

      // Search for files with this name in the workspace
      const files = await vscode.workspace.findFiles(
        `**/${justFileName}`,
        null,
        10
      );

      if (files.length > 0) {
        console.log(`📁 Found ${files.length} matching files:`);
        files.forEach((file, index) => {
          console.log(`  ${index + 1}. ${file.fsPath}`);
        });

        // Return the first match (could be enhanced to pick the best match)
        return files[0];
      }

      console.log(`❌ No files found with name: ${justFileName}`);
      return null;
    } catch (error) {
      console.error(`❌ Error searching for file ${fileName}:`, error);
      return null;
    }
  }

  private async saveFile(fileUri: vscode.Uri, filePath: string) {
    try {
      const document = await vscode.workspace.openTextDocument(fileUri);
      console.log(`💾 Saving file: ${filePath}`);
      const saveResult = await document.save();
      console.log(`💾 Save result: ${saveResult}, file saved: ${filePath}`);
    } catch (saveError) {
      console.error(`⚠️ Failed to auto-save file ${filePath}:`, saveError);
    }
  }

  private _persistentTerminal: vscode.Terminal | null = null;
  private _terminalCloseDisposable: vscode.Disposable | null = null;

  /**
   * Get a cross-platform default shell path
   */
  private getDefaultShell(): string {
    const platform = os.platform();
    if (platform === "win32") {
      // Try PowerShell, then cmd.exe
      const powershell =
        "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe";
      const cmd = "C:\\Windows\\System32\\cmd.exe";
      return fs.existsSync(powershell) ? powershell : cmd;
    } else {
      // Try zsh, then bash
      return fs.existsSync("/bin/zsh") ? "/bin/zsh" : "/bin/bash";
    }
  }

  /**
   * Check if a shell path is executable
   */
  private isShellExecutable(shellPath: string): boolean {
    try {
      return (
        fs.existsSync(shellPath) && (fs.statSync(shellPath).mode & 0o111) !== 0
      );
    } catch {
      return false;
    }
  }

  /**
   * Get or create a persistent terminal for all operations (cross-platform safe)
   */
  private getPersistentTerminal(): vscode.Terminal | null {
    try {
      if (!this._persistentTerminal || this._persistentTerminal.exitStatus) {
        if (this._persistentTerminal) {
          this._persistentTerminal.dispose();
        }
        const workspaceFolders = vscode.workspace.workspaceFolders;
        const workspaceRoot = workspaceFolders?.[0]?.uri.fsPath;
        let shellPath: string | undefined = undefined;
        let usedFallback = false;

        // Try to get the user's configured shell from VSCode settings
        const config = vscode.workspace.getConfiguration("terminal.integrated");
        const platform = os.platform();
        if (platform === "win32") {
          shellPath = config.get<string>("defaultProfile.windows");
          if (!shellPath) shellPath = config.get<string>("shell.windows");
        } else if (platform === "darwin") {
          shellPath = config.get<string>("defaultProfile.osx");
          if (!shellPath) shellPath = config.get<string>("shell.osx");
        } else {
          shellPath = config.get<string>("defaultProfile.linux");
          if (!shellPath) shellPath = config.get<string>("shell.linux");
        }

        // If not set or not executable, use fallback
        if (!shellPath || !this.isShellExecutable(shellPath)) {
          shellPath = this.getDefaultShell();
          usedFallback = true;
        }

        if (!this.isShellExecutable(shellPath)) {
          vscode.window.showErrorMessage(
            `ReCode: Could not find a valid shell to launch the terminal. Please check your VSCode terminal settings. Tried: ${shellPath}`
          );
          console.error(
            `[ReCode] Terminal launch failed: No valid shell found. Tried: ${shellPath}`
          );
          return null;
        }

        try {
          this._persistentTerminal = vscode.window.createTerminal({
            name: "ReCode Terminal Operation",
            cwd: workspaceRoot,
            shellPath,
            hideFromUser: false,
          });
          if (usedFallback) {
            vscode.window.showWarningMessage(
              `ReCode: Default shell was not found or not executable. Using fallback shell: ${shellPath}`
            );
            console.warn(`[ReCode] Using fallback shell: ${shellPath}`);
          }
        } catch (err: any) {
          vscode.window.showErrorMessage(
            `ReCode: Failed to launch terminal: ${err.message}. Please check your shell configuration in VSCode settings.`
          );
          console.error(`[ReCode] Exception during terminal creation:`, err);
          this._persistentTerminal = null;
          return null;
        }

        // Listen for terminal close and clean up
        if (this._terminalCloseDisposable) {
          this._terminalCloseDisposable.dispose();
        }
        this._terminalCloseDisposable = vscode.window.onDidCloseTerminal(
          (closedTerminal) => {
            if (closedTerminal === this._persistentTerminal) {
              console.log("🧹 Persistent terminal closed by user or system.");
              this._persistentTerminal = null;
            }
          }
        );
      }
      return this._persistentTerminal;
    } catch (err: any) {
      vscode.window.showErrorMessage(
        `ReCode: Unexpected error while creating terminal: ${err.message}`
      );
      console.error(`[ReCode] Unexpected error in getPersistentTerminal:`, err);
      this._persistentTerminal = null;
      return null;
    }
  }

  /**
   * Handle terminal/CMD operations (create, delete, move, copy files/directories)
   * This method should NOT handle approval - that's done by the WebSocket system
   * This method only executes the commands after approval
   */
  private async handleTerminalOperation(commands: string[]): Promise<boolean> {
    try {
      if (!commands || commands.length === 0) {
        console.log(`⚠️ No terminal commands to execute`);
        return true;
      }

      console.log(`💻 Terminal operation: ${commands.length} commands`);

      // Execute commands sequentially (approval already handled by WebSocket system)
      let allSuccess = true;
      for (const command of commands) {
        const success = await this.executeTerminalCommand(command);
        if (!success) {
          allSuccess = false;
          console.error(`❌ Failed to execute terminal command: ${command}`);
        }
      }

      if (allSuccess) {
        console.log(`✅ All terminal commands executed successfully`);
      } else {
        vscode.window.showErrorMessage(
          `ReCode: Some terminal commands failed. Please check the logs for details.`
        );
        console.error(`❌ Some terminal commands failed`);
      }

      return allSuccess;
    } catch (error: any) {
      vscode.window.showErrorMessage(
        `ReCode: Error in terminal operation: ${error.message}`
      );
      console.error(`❌ Error in terminal operation:`, error);
      return false;
    }
  }

  /**
   * Execute a single terminal command (create, delete, move, copy, etc.)
   * Uses a persistent terminal and generic verification logic with retry.
   */
  private async executeTerminalCommand(command: string): Promise<boolean> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      const workspaceRoot = workspaceFolders?.[0]?.uri.fsPath;
      if (!workspaceRoot) {
        vscode.window.showErrorMessage(
          `ReCode: No workspace found for terminal command: ${command}`
        );
        console.error(`❌ No workspace found for terminal command: ${command}`);
        return false;
      }
      const terminal = this.getPersistentTerminal();
      if (!terminal) {
        vscode.window.showErrorMessage(
          `ReCode: Could not create a terminal to execute: ${command}`
        );
        console.error(`❌ Could not create a terminal to execute: ${command}`);
        return false;
      }
      terminal.show();
      console.log(`🔧 Executing terminal command: ${command}`);
      console.log(`🔧 Working directory: ${workspaceRoot}`);
      terminal.sendText(command);
      // Generic verification logic
      let verifyFn: (() => Promise<boolean>) | null = null;
      // mkdir
      if (/^mkdir(\s+-p)?\s+(.+)/.test(command)) {
        const dirMatch = command.match(/^mkdir(?:\s+-p)?\s+(.+)/);
        if (dirMatch) {
          const dirPath = dirMatch[1].trim();
          verifyFn = async () => {
            try {
              const fullPath = vscode.Uri.joinPath(
                vscode.Uri.file(workspaceRoot),
                dirPath
              );
              const stat = await vscode.workspace.fs.stat(fullPath);
              return stat.type === vscode.FileType.Directory;
            } catch {
              return false;
            }
          };
        }
      }
      // touch
      else if (/^touch\s+(.+)/.test(command)) {
        const fileMatch = command.match(/^touch\s+(.+)/);
        if (fileMatch) {
          const filePath = fileMatch[1].trim();
          verifyFn = async () => {
            try {
              const fullPath = vscode.Uri.joinPath(
                vscode.Uri.file(workspaceRoot),
                filePath
              );
              const stat = await vscode.workspace.fs.stat(fullPath);
              return stat.type === vscode.FileType.File;
            } catch {
              return false;
            }
          };
        }
      }
      // rm
      else if (/^rm\s+(-rf\s+)?(.+)/.test(command)) {
        const rmMatch = command.match(/^rm\s+(?:-rf\s+)?(.+)/);
        if (rmMatch) {
          const targetPath = rmMatch[1].trim();
          verifyFn = async () => {
            try {
              const fullPath = vscode.Uri.joinPath(
                vscode.Uri.file(workspaceRoot),
                targetPath
              );
              await vscode.workspace.fs.stat(fullPath);
              return false; // If stat succeeds, file/dir still exists
            } catch {
              return true; // If stat fails, file/dir is gone
            }
          };
        }
      }
      // cp or mv
      else if (/^(cp|mv)\s+(.+)\s+(.+)/.test(command)) {
        const cpMvMatch = command.match(/^(cp|mv)\s+(.+)\s+(.+)/);
        if (cpMvMatch) {
          const destPath = cpMvMatch[3].trim();
          verifyFn = async () => {
            try {
              const fullPath = vscode.Uri.joinPath(
                vscode.Uri.file(workspaceRoot),
                destPath
              );
              const stat = await vscode.workspace.fs.stat(fullPath);
              return (
                stat.type === vscode.FileType.File ||
                stat.type === vscode.FileType.Directory
              );
            } catch {
              return false;
            }
          };
        }
      }
      // Add more command verifications as needed
      // For unknown commands, skip verification
      if (verifyFn) {
        let ok = false;
        const maxAttempts = 10;
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
          ok = await verifyFn();
          console.log(`🔍 Verification attempt ${attempt}: ${ok}`);
          if (ok) break;
          await new Promise((res) => setTimeout(res, 200));
        }
        if (ok) {
          console.log(`✅ Verified terminal command: ${command}`);
          return true;
        } else {
          console.error(
            `❌ Verification failed for terminal command: ${command}`
          );
          return false;
        }
      } else {
        // No verification possible, assume success
        console.log(`ℹ️ No verification for terminal command: ${command}`);
        await new Promise((res) => setTimeout(res, 2000));
        return true;
      }
    } catch (error) {
      console.error(`❌ Error executing terminal command: ${command}`, error);
      
      // Send Agent mode failure feedback if applicable
      if (this._messageHandler) {
        await this._messageHandler.sendAgentModeFailureFeedback(
          "terminal_command",
          `Terminal command failed: ${command}`,
          `Error: ${error instanceof Error ? error.message : String(error)}`
        );
      }
      
      // Dispose terminal on error
      if (this._persistentTerminal) {
        this._persistentTerminal.dispose();
        this._persistentTerminal = null;
      }
      if (this._terminalCloseDisposable) {
        this._terminalCloseDisposable.dispose();
        this._terminalCloseDisposable = null;
      }
      return false;
    }
  }
}
