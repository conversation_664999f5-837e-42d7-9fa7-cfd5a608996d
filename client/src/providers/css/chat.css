/* Chat interface styles */
.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.chat-toggle-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-toggle-container .chat-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(
    135deg,
    var(--vscode-button-background) 0%,
    var(--vscode-textLink-foreground) 100%
  );
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  opacity: 0.8;
}

.chat-toggle-container .chat-toggle:hover {
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.chat-toggle-dropdown {
  background: linear-gradient(
    135deg,
    var(--vscode-button-background) 0%,
    var(--vscode-textLink-foreground) 100%
  );
  color: var(--vscode-button-foreground);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 6px 24px 6px 10px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  opacity: 0.95;
  appearance: none;
  outline: none;
  margin-left: 8px;
  position: relative;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 12px;
  min-width: 140px;
}

.chat-toggle-dropdown:focus {
  outline: 2px solid var(--vscode-focusBorder);
  outline-offset: 1px;
  opacity: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.chat-toggle-dropdown:hover {
  opacity: 1;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.chat-toggle-dropdown option {
  background: var(--vscode-dropdown-background);
  color: var(--vscode-dropdown-foreground);
  padding: 8px 12px;
  font-size: 12px;
}

.new-conversation-btn {
  background: linear-gradient(
    135deg,
    var(--vscode-button-background) 0%,
    var(--vscode-textLink-foreground) 100%
  );
  color: var(--vscode-button-foreground);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 50px;
}

.new-conversation-btn:hover {
  opacity: 1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.new-conversation-btn:focus {
  outline: 2px solid var(--vscode-focusBorder);
  outline-offset: 1px;
}

.new-conversation-btn .btn-icon {
  font-size: 14px;
  font-weight: bold;
}

.new-conversation-btn .btn-text {
  font-size: 10px;
}

.chat-container {
  padding: 8px 4px;
  background-color: var(--vscode-sideBar-background);
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent container overflow */
  position: relative; /* Ensure proper positioning context */
}

.file-tabs {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  flex: 1;
  justify-content: flex-start;
}

.file-tab {
  background-color: var(--vscode-tab-inactiveBackground);
  color: var(--vscode-tab-inactiveForeground);
  border: 1px solid var(--vscode-tab-border);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.file-tab.active {
  background-color: var(--vscode-tab-activeBackground);
  color: var(--vscode-tab-activeForeground);
  border-color: var(--vscode-textLink-foreground);
}

.file-tab .close-btn {
  font-size: 10px;
  opacity: 0.7;
  cursor: pointer;
  margin-left: 4px;
}

.file-tab .close-btn:hover {
  opacity: 1;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 0; /* Remove margin - let container handle spacing */
  padding: 0 4px 160px 4px; /* Further increased bottom padding for fixed input area */
  min-height: 0; /* Allow flex shrinking */
  box-sizing: border-box;
}

.message {
  margin-bottom: 8px;
  font-size: 11px;
  line-height: 1.4;
  text-align: left;
}

.message.user {
  padding: 8px 12px;
  border-radius: 6px;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-panel-border);
  border-left: 3px solid var(--vscode-textLink-foreground);
  text-align: left;
}

.message.user .message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-size: 12px;
  font-weight: 500;
  color: var(--vscode-foreground);
}

.message.user .message-header .icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--vscode-textLink-foreground);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.message.user .message-content {
  color: var(--vscode-foreground);
  line-height: 1.5;
  font-size: 11px;
  text-align: left;
}

.message.assistant {
  background: transparent;
  border: none;
  padding: 0;
  margin-bottom: 8px;
  text-align: left;
}

.message.assistant .message-content {
  color: var(--vscode-foreground);
  line-height: 1.6;
  font-size: 11px;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  padding: 0;
  text-align: left;
}

/* Simple Reasoning Text */
.reasoning-text {
  font-size: 11px;
  color: var(--vscode-foreground);
  line-height: 1.5;
  margin: 8px 0;
  opacity: 0.9;
  text-align: left;
}

/* Normalize paragraph alignment in chat messages */
.chat-messages p {
  margin: 8px 0;
  padding: 0;
  text-align: left;
  color: var(--vscode-foreground);
  font-size: 11px;
  line-height: 1.5;
}

/* Override specific styling for code approval paragraphs in chat */
.chat-messages .reason-section p,
.chat-messages .details-section p {
  margin: 8px 0;
  padding: 8px;
  background-color: var(--vscode-textCodeBlock-background);
  border-radius: 4px;
  border-left: 3px solid var(--vscode-textLink-foreground);
  color: var(--vscode-descriptionForeground);
  font-size: 11px;
  line-height: 1.4;
}
