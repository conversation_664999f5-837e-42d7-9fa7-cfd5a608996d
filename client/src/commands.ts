/**
 * VS Code Extension Commands for Session Management
 * Provides user-facing commands for session control
 */

import * as vscode from 'vscode';
import { SessionAwareWebSocketClient } from './websocketClient';
import { SessionManager } from './sessionManager';

export class SessionCommands {
    private wsClient: SessionAwareWebSocketClient;
    private sessionManager: SessionManager;

    constructor(
        private context: vscode.ExtensionContext,
        wsClient: SessionAwareWebSocketClient
    ) {
        this.wsClient = wsClient;
        this.sessionManager = SessionManager.getInstance(context);
        this.registerCommands();
    }

    /**
     * Register all session-related commands
     */
    private registerCommands(): void {
        // Main coder request command
        const coderCommand = vscode.commands.registerCommand(
            'recode.ai.askCoder',
            () => this.askCoderCommand()
        );

        // New session command
        const newSessionCommand = vscode.commands.registerCommand(
            'recode.ai.newSession',
            () => this.newSessionCommand()
        );

        // Show session info command
        const sessionInfoCommand = vscode.commands.registerCommand(
            'recode.ai.showSessionInfo',
            () => this.showSessionInfoCommand()
        );

        // Clear session command (for debugging)
        const clearSessionCommand = vscode.commands.registerCommand(
            'recode.ai.clearSession',
            () => this.clearSessionCommand()
        );

        // Register disposables
        this.context.subscriptions.push(
            coderCommand,
            newSessionCommand,
            sessionInfoCommand,
            clearSessionCommand
        );
    }

    /**
     * Main command: Ask ReCode AI Coder
     */
    private async askCoderCommand(): Promise<void> {
        try {
            // Get project name from workspace
            const projectName = this.getProjectName();
            
            // Get user question
            const question = await vscode.window.showInputBox({
                prompt: 'What would you like ReCode AI to help you with?',
                placeHolder: 'e.g., Add authentication to my React app',
                ignoreFocusOut: true
            });

            if (!question) {
                return; // User cancelled
            }

            // Show options for session handling
            const sessionOption = await vscode.window.showQuickPick([
                {
                    label: '🔄 Continue Current Session',
                    description: 'Use existing conversation context',
                    value: 'continue'
                },
                {
                    label: '🆕 Start New Session',
                    description: 'Begin fresh conversation',
                    value: 'new'
                }
            ], {
                placeHolder: 'Choose session option',
                ignoreFocusOut: true
            });

            if (!sessionOption) {
                return; // User cancelled
            }

            // Send request with session handling
            await this.wsClient.sendCoderRequest(projectName, question, {
                forceNewSession: sessionOption.value === 'new',
                userId: this.getUserId(),
                workspaceId: this.getWorkspaceId()
            });

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to send request: ${error}`);
        }
    }

    /**
     * Command: Create New Session
     */
    private async newSessionCommand(): Promise<void> {
        try {
            const projectName = this.getProjectName();
            const sessionId = await this.wsClient.createNewSession(projectName);
            
            vscode.window.showInformationMessage(
                `🆕 New session created: ${sessionId.substring(0, 8)}`,
                'Show Details'
            ).then(selection => {
                if (selection === 'Show Details') {
                    this.showSessionInfoCommand();
                }
            });

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to create new session: ${error}`);
        }
    }

    /**
     * Command: Show Session Information
     */
    private async showSessionInfoCommand(): Promise<void> {
        try {
            const sessionInfo = await this.wsClient.getCurrentSessionInfo();
            
            if (!sessionInfo) {
                vscode.window.showInformationMessage('No active session');
                return;
            }

            const message = [
                `🔑 Session ID: ${sessionInfo.shortId}`,
                `📁 Project: ${sessionInfo.projectName || 'Unknown'}`,
                `⏰ Age: ${sessionInfo.age}`,
                `⏳ TTL Remaining: ${sessionInfo.ttl}`
            ].join('\n');

            vscode.window.showInformationMessage(message, 'Copy Full ID', 'New Session')
                .then(selection => {
                    if (selection === 'Copy Full ID') {
                        vscode.env.clipboard.writeText(sessionInfo.sessionId);
                        vscode.window.showInformationMessage('Session ID copied to clipboard');
                    } else if (selection === 'New Session') {
                        this.newSessionCommand();
                    }
                });

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to get session info: ${error}`);
        }
    }

    /**
     * Command: Clear Session (for debugging)
     */
    private async clearSessionCommand(): Promise<void> {
        const confirm = await vscode.window.showWarningMessage(
            'Are you sure you want to clear the current session? This will lose conversation history.',
            'Yes, Clear Session',
            'Cancel'
        );

        if (confirm === 'Yes, Clear Session') {
            try {
                await this.sessionManager.clearSession();
                vscode.window.showInformationMessage('🗑️ Session cleared');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to clear session: ${error}`);
            }
        }
    }

    /**
     * Get current project name from workspace
     */
    private getProjectName(): string {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].name;
        }
        return 'untitled-project';
    }

    /**
     * Get user ID (can be configured or derived)
     */
    private getUserId(): string | undefined {
        const config = vscode.workspace.getConfiguration('recode.ai');
        return config.get<string>('userId');
    }

    /**
     * Get workspace ID (derived from workspace folder)
     */
    private getWorkspaceId(): string | undefined {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            // Use workspace folder path as identifier
            return Buffer.from(workspaceFolders[0].uri.fsPath).toString('base64').substring(0, 16);
        }
        return undefined;
    }
}
