/**
 * Client-Side Session Manager for Cognee Memory Continuity
 * Handles UUID generation, TTL management, and session lifecycle
 */

import * as vscode from 'vscode';
import { v4 as uuidv4 } from 'uuid';

export interface SessionInfo {
    sessionId: string;
    createdAt: number;
    lastUsedAt: number;
    projectName?: string;
}

export class SessionManager {
    private static instance: SessionManager;
    private currentSession: SessionInfo | null = null;
    private readonly STORAGE_KEY = 'recode.ai.session';
    private readonly DEFAULT_TTL_HOURS = 24;

    private constructor(private context: vscode.ExtensionContext) {}

    public static getInstance(context: vscode.ExtensionContext): SessionManager {
        if (!SessionManager.instance) {
            SessionManager.instance = new SessionManager(context);
        }
        return SessionManager.instance;
    }

    /**
     * Get current session ID, creating new one if expired or missing
     */
    public async getCurrentSessionId(projectName?: string): Promise<string> {
        const session = await this.getCurrentSession(projectName);
        return session.sessionId;
    }

    /**
     * Get current session info, creating new one if needed
     */
    public async getCurrentSession(projectName?: string): Promise<SessionInfo> {
        // Load existing session from storage
        if (!this.currentSession) {
            await this.loadSessionFromStorage();
        }

        // Check if session is valid and not expired
        if (this.currentSession && this.isSessionValid(this.currentSession)) {
            // Update last used timestamp
            this.currentSession.lastUsedAt = Date.now();
            if (projectName) {
                this.currentSession.projectName = projectName;
            }
            await this.saveSessionToStorage();
            return this.currentSession;
        }

        // Create new session
        return await this.createNewSession(projectName);
    }

    /**
     * Explicitly create a new session (for "New Session" button)
     */
    public async createNewSession(projectName?: string): Promise<SessionInfo> {
        const sessionId = uuidv4();
        const now = Date.now();

        this.currentSession = {
            sessionId,
            createdAt: now,
            lastUsedAt: now,
            projectName
        };

        await this.saveSessionToStorage();
        
        // 🎯 VISIBLE LOGGING: New session creation
        this.logNewSessionCreation(sessionId, projectName);
        
        return this.currentSession;
    }

    /**
     * Check if session is still valid (not expired)
     */
    private isSessionValid(session: SessionInfo): boolean {
        const ttlHours = this.getTTLHours();
        const ttlMs = ttlHours * 60 * 60 * 1000;
        const now = Date.now();
        
        return (now - session.lastUsedAt) < ttlMs;
    }

    /**
     * Get TTL from configuration (environment variable first, then VS Code settings)
     */
    private getTTLHours(): number {
        // First try to get from environment variable (if available)
        const envTTL = process.env.SESSION_TTL_HOURS;
        if (envTTL) {
            const parsedTTL = parseInt(envTTL, 10);
            if (!isNaN(parsedTTL) && parsedTTL > 0) {
                return parsedTTL;
            }
        }
        
        // Fall back to VS Code settings
        const config = vscode.workspace.getConfiguration('recode.ai');
        return config.get<number>('sessionTTLHours', this.DEFAULT_TTL_HOURS);
    }

    /**
     * Load session from VS Code storage
     */
    private async loadSessionFromStorage(): Promise<void> {
        try {
            const sessionData = this.context.globalState.get<SessionInfo>(this.STORAGE_KEY);
            if (sessionData) {
                this.currentSession = sessionData;
            }
        } catch (error) {
            console.error('Failed to load session from storage:', error);
            this.currentSession = null;
        }
    }

    /**
     * Save session to VS Code storage
     */
    private async saveSessionToStorage(): Promise<void> {
        try {
            if (this.currentSession) {
                await this.context.globalState.update(this.STORAGE_KEY, this.currentSession);
            }
        } catch (error) {
            console.error('Failed to save session to storage:', error);
        }
    }

    /**
     * 🎯 VISIBLE LOGGING: Log new session creation with user notification
     */
    private logNewSessionCreation(sessionId: string, projectName?: string): void {
        const shortId = sessionId.substring(0, 8);
        const projectInfo = projectName ? ` for project "${projectName}"` : '';
        
        // Console logging
        console.log(`🆕 NEW SESSION CREATED: ${shortId}${projectInfo}`);
        
        // VS Code output channel logging
        this.getOutputChannel().appendLine(`🆕 NEW SESSION CREATED: ${shortId}${projectInfo}`);
        
        // User notification (non-intrusive)
        vscode.window.showInformationMessage(
            `🆕 New AI session started${projectInfo} (${shortId})`,
            { modal: false }
        );
        
        // Status bar update
        this.updateStatusBar(shortId);
    }

    /**
     * Get or create output channel for session logging
     */
    private getOutputChannel(): vscode.OutputChannel {
        return vscode.window.createOutputChannel('ReCode AI Sessions');
    }

    /**
     * Update status bar with current session info
     */
    private updateStatusBar(sessionId: string): void {
        const statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right, 
            100
        );
        statusBarItem.text = `$(account) Session: ${sessionId}`;
        statusBarItem.tooltip = 'Current ReCode AI session';
        statusBarItem.command = 'recode.ai.showSessionInfo';
        statusBarItem.show();
        
        // Store reference for cleanup
        this.context.subscriptions.push(statusBarItem);
    }

    /**
     * Get session statistics for debugging
     */
    public getSessionStats(): { sessionId: string; age: string; ttl: string } | null {
        if (!this.currentSession) {
            return null;
        }

        const now = Date.now();
        const ageMs = now - this.currentSession.createdAt;
        const lastUsedMs = now - this.currentSession.lastUsedAt;
        const ttlMs = this.getTTLHours() * 60 * 60 * 1000;
        const remainingMs = ttlMs - lastUsedMs;

        return {
            sessionId: this.currentSession.sessionId.substring(0, 8),
            age: this.formatDuration(ageMs),
            ttl: remainingMs > 0 ? this.formatDuration(remainingMs) : 'EXPIRED'
        };
    }

    /**
     * Format duration in human-readable format
     */
    private formatDuration(ms: number): string {
        const hours = Math.floor(ms / (1000 * 60 * 60));
        const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        }
        return `${minutes}m`;
    }

    /**
     * Clear current session (for testing or manual reset)
     */
    public async clearSession(): Promise<void> {
        this.currentSession = null;
        await this.context.globalState.update(this.STORAGE_KEY, undefined);
        console.log('🗑️ Session cleared');
    }
}
