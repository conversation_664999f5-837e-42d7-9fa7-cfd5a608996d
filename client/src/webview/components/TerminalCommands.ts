// Terminal commands UI components

import { DOMUtils } from '../utils/DOMUtils';
import { ValidationUtils } from '../utils/ValidationUtils';
import { BUTTON_STYLES, CSS_CLASSES } from '../core/constants';
import { <PERSON>bar<PERSON><PERSON>ode<PERSON><PERSON>, TerminalCommand, UserActionPrompt } from '../core/types';

export class TerminalCommands {
  private vscode: SidebarVsCodeApi;

  constructor(vscode: SidebarVsCodeApi) {
    this.vscode = vscode;
  }

  /**
   * Show terminal command
   */
  showTerminalCommand(command: TerminalCommand): void {
    const chatMessages = DOMUtils.getElementById("chat-messages");
    if (!chatMessages) return;

    const commandDiv = DOMUtils.createElement("div", "terminal-command");
    
    commandDiv.innerHTML = `
      <div class="command-header">
        <span class="command-icon">💻</span>
        <span class="command-description">${command.description}</span>
        <span class="command-status ${command.status}">${this.getStatusIcon(command.status)}</span>
      </div>
      <div class="command-details">
        <div class="command-text">
          <code>${command.command}</code>
        </div>
        <button class="terminal-command-btn" data-command="${command.command}">
          ${command.status === "running" ? "⏳" : "▶️"} ${command.status === "running" ? "Running" : "Run"}
        </button>
      </div>
    `;

    chatMessages.appendChild(commandDiv);
    this.scrollToBottom();

    // Add event listener for the button
    const button = commandDiv.querySelector(".terminal-command-btn") as HTMLElement;
    if (button) {
      button.addEventListener("click", () => {
        this.executeTerminalCommand(command.command, button);
      });
    }
  }

  /**
   * Execute terminal command
   */
  private executeTerminalCommand(command: string, buttonElement: HTMLElement): void {
    // Validate command safety
    if (!ValidationUtils.isSafeCommand(command)) {
      this.showCommandWarning(command, buttonElement);
      return;
    }

    // Update button to show executing state
    this.updateTerminalCommandStatus(buttonElement, "executing");

    // Send command to extension
    this.vscode.postMessage({
      command: "executeTerminalCommand",
      terminalCommand: command,
    });
  }

  /**
   * Show command warning
   */
  private showCommandWarning(command: string, buttonElement: HTMLElement): void {
    const warningDiv = DOMUtils.createElement("div", "command-warning");
    warningDiv.innerHTML = `
      <div class="warning-content">
        <span class="warning-icon">⚠️</span>
        <span class="warning-text">This command may be potentially dangerous. Are you sure you want to execute it?</span>
        <div class="warning-actions">
          <button class="confirm-button">✅ Execute Anyway</button>
          <button class="cancel-button">❌ Cancel</button>
        </div>
      </div>
    `;

    const commandContainer = buttonElement.closest(".terminal-command");
    if (commandContainer) {
      commandContainer.appendChild(warningDiv);
    }

    // Add event listeners
    const confirmButton = warningDiv.querySelector(".confirm-button") as HTMLElement;
    const cancelButton = warningDiv.querySelector(".cancel-button") as HTMLElement;

    if (confirmButton) {
      confirmButton.addEventListener("click", () => {
        warningDiv.remove();
        this.updateTerminalCommandStatus(buttonElement, "executing");
        this.vscode.postMessage({
          command: "executeTerminalCommand",
          terminalCommand: command,
          forceExecute: true,
        });
      });
    }

    if (cancelButton) {
      cancelButton.addEventListener("click", () => {
        warningDiv.remove();
      });
    }
  }

  /**
   * Update terminal command status
   */
  updateTerminalCommandStatus(buttonElement: HTMLElement, status: "executing" | "success" | "error"): void {
    const actionStatusDiv = buttonElement.closest(".terminal-command");

    switch (status) {
      case "executing":
        DOMUtils.setInnerHTML(buttonElement, "⏳");
        buttonElement.setAttribute("disabled", "true");
        DOMUtils.applyStyles(buttonElement, BUTTON_STYLES.EXECUTING);
        break;
      case "success":
        DOMUtils.setInnerHTML(buttonElement, "✅");
        DOMUtils.applyStyles(buttonElement, BUTTON_STYLES.SUCCESS);
        buttonElement.removeAttribute("disabled");

        // Add success message
        this.addStatusMessageToContainer(
          actionStatusDiv as HTMLElement,
          "Command executed successfully! 💻✨",
          "success"
        );
        break;
      case "error":
        DOMUtils.setInnerHTML(buttonElement, "❌");
        DOMUtils.applyStyles(buttonElement, BUTTON_STYLES.ERROR);
        buttonElement.removeAttribute("disabled");

        // Add error message
        this.addStatusMessageToContainer(
          actionStatusDiv as HTMLElement,
          "Command execution failed 💻❌",
          "error"
        );
        break;
    }
  }

  /**
   * Update terminal command buttons by command
   */
  updateTerminalCommandButtonsByCommand(command: string, status: "success" | "error"): void {
    const buttons = DOMUtils.querySelectorAll(`[data-command="${command}"]`);
    buttons.forEach((button) => {
      this.updateTerminalCommandStatus(button as HTMLElement, status);
    });
  }

  /**
   * Update all executing terminal command buttons
   */
  updateAllExecutingTerminalCommandButtons(status: "success" | "error"): void {
    const allButtons = DOMUtils.querySelectorAll(".terminal-command-btn");
    let updatedCount = 0;

    allButtons.forEach((button) => {
      const buttonElement = button as HTMLElement;

      // Check if button is in executing state
      const isDisabled = buttonElement.hasAttribute("disabled");
      const hasHourglass = buttonElement.innerHTML.includes("⏳");

      if (isDisabled || hasHourglass) {
        if (buttonElement.classList.contains("terminal-command-btn")) {
          this.updateTerminalCommandStatus(buttonElement, status);
          updatedCount++;
        }
      }
    });

    console.log(`💻 Updated ${updatedCount} executing terminal command buttons to ${status}`);
  }

  /**
   * Show user action prompt with terminal commands
   */
  showUserActionPrompt(data: UserActionPrompt): void {
    const chatMessages = DOMUtils.getElementById("chat-messages");
    if (!chatMessages) return;

    const messageDiv = DOMUtils.createElement("div", "message assistant user-action-prompt");

    // Create compilation status section if provided
    const compilationStatus = data.compilationStatus 
      ? `<div class="compilation-status">${data.compilationStatus}</div>`
      : "";

    // Create terminal commands section
    const commandsHtml = data.commands
      .map((cmd: TerminalCommand, index: number) => `
        <div class="terminal-command-item">
          <div class="command-description">${cmd.description}</div>
          <div class="command-detail">${cmd.command}</div>
          <button class="action-play-button terminal-command-btn" data-command="${cmd.command}">
            ▶️ Run
          </button>
        </div>
      `)
      .join("");

    messageDiv.innerHTML = `
      <div class="user-action-prompt">
        ${compilationStatus}
        <div class="action-message">${data.message}</div>
        <div class="terminal-commands">
          ${commandsHtml}
        </div>
      </div>
    `;

    chatMessages.appendChild(messageDiv);

    // Add event listeners for terminal command buttons
    const terminalButtons = messageDiv.querySelectorAll(".terminal-command-btn");
    terminalButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const target = e.target as HTMLElement;
        const command = target.getAttribute("data-command") || "";
        this.executeTerminalCommand(command, target);
      });
    });

    this.scrollToBottom();
  }

  /**
   * Add status message to container
   */
  private addStatusMessageToContainer(
    container: HTMLElement,
    message: string,
    type: "success" | "error"
  ): void {
    // Remove any existing status message
    const existingMessage = container.querySelector(".status-message");
    if (existingMessage) {
      existingMessage.remove();
    }

    const statusDiv = DOMUtils.createElement("div", `status-message ${type}`);
    statusDiv.innerHTML = `<span class="status-text">${message}</span>`;
    container.appendChild(statusDiv);
  }

  /**
   * Get status icon
   */
  private getStatusIcon(status: string): string {
    const icons: { [key: string]: string } = {
      "pending": "⏸️",
      "running": "⏳",
      "success": "✅",
      "error": "❌",
    };
    return icons[status] || "⏸️";
  }

  /**
   * Scroll to bottom
   */
  private scrollToBottom(): void {
    const chatMessages = DOMUtils.getElementById("chat-messages");
    if (chatMessages) {
      DOMUtils.scrollToBottom(chatMessages);
    }
  }

  /**
   * Show terminal commands list
   */
  showTerminalCommandsList(commands: TerminalCommand[]): void {
    const chatMessages = DOMUtils.getElementById("chat-messages");
    if (!chatMessages) return;

    const commandsDiv = DOMUtils.createElement("div", "terminal-commands-list");
    commandsDiv.innerHTML = `
      <div class="commands-header">
        <h3>💻 Terminal Commands</h3>
      </div>
      <div class="commands-content">
        ${commands.map(cmd => this.renderTerminalCommandItem(cmd)).join('')}
      </div>
    `;

    chatMessages.appendChild(commandsDiv);
    this.scrollToBottom();

    // Add event listeners for all buttons
    const buttons = commandsDiv.querySelectorAll(".terminal-command-btn");
    buttons.forEach(button => {
      button.addEventListener("click", (e) => {
        const target = e.target as HTMLElement;
        const command = target.getAttribute("data-command") || "";
        this.executeTerminalCommand(command, target);
      });
    });
  }

  /**
   * Render terminal command item
   */
  private renderTerminalCommandItem(command: TerminalCommand): string {
    const statusIcon = this.getStatusIcon(command.status);

    return `
      <div class="terminal-command-item" data-command="${command.command}">
        <div class="command-info">
          <span class="command-icon">💻</span>
          <span class="command-description">${command.description}</span>
          <span class="command-status ${command.status}">${statusIcon}</span>
        </div>
        <div class="command-details">
          <div class="command-text">
            <code>${command.command}</code>
          </div>
          <button class="terminal-command-btn" 
                  data-command="${command.command}"
                  ${command.status === "running" ? "disabled" : ""}>
            ${command.status === "running" ? "⏳ Running" : "▶️ Run"}
          </button>
        </div>
      </div>
    `;
  }
}
