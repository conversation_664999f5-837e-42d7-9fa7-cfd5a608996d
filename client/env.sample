# ReCode AI Backend Configuration
RECODE_SERVER_HOST=127.0.0.1
RECODE_SERVER_PORT=8000

# API Endpoints (paths only - host/port will be combined)
RECODE_INDEX_BATCH_ENDPOINT=/index/batch
RECODE_INDEX_LATEST_MERKLE_ENDPOINT=/index/latest_merkle_tree
RECODE_SYNC_DIFFERENTIAL_ENDPOINT=/sync/differential
RECODE_PROJECT_SUMMARY_ENDPOINT=/project/summary

# Configuration
RECODE_BATCH_SIZE=10
RECODE_MAX_RETRIES=3
RECODE_RETRY_DELAY=1000
RECODE_CHUNK_SIZE=2000
RECODE_CHUNK_OVERLAP=200
RECODE_DIFF_SYNC_INTERVAL_MINUTES=10

# User Response Timeout (seconds)
RECODE_USER_RESPONSE_TIMEOUT=600

# Session Management (Client-side)
# Session TTL in hours - how long sessions remain active before expiring
SESSION_TTL_HOURS=24 s