{"contributes": {"commands": [{"command": "recode.ai.askCoder", "title": "Ask ReCode AI", "category": "ReCode AI", "icon": "$(robot)"}, {"command": "recode.ai.newSession", "title": "New Session", "category": "ReCode AI", "icon": "$(add)"}, {"command": "recode.ai.showSessionInfo", "title": "Show Session Info", "category": "ReCode AI", "icon": "$(info)"}, {"command": "recode.ai.clearSession", "title": "Clear Session", "category": "ReCode AI", "icon": "$(trash)"}], "menus": {"commandPalette": [{"command": "recode.ai.askCoder", "when": "true"}, {"command": "recode.ai.newSession", "when": "true"}, {"command": "recode.ai.showSessionInfo", "when": "true"}, {"command": "recode.ai.clearSession", "when": "true"}], "editor/context": [{"command": "recode.ai.askCoder", "group": "recode@1", "when": "editorTextFocus"}]}, "keybindings": [{"command": "recode.ai.askCoder", "key": "ctrl+shift+a", "mac": "cmd+shift+a", "when": "editorTextFocus"}, {"command": "recode.ai.newSession", "key": "ctrl+shift+n", "mac": "cmd+shift+n"}], "configuration": {"title": "ReCode AI", "properties": {"recode.ai.serverUrl": {"type": "string", "default": "ws://localhost:8000", "description": "WebSocket server URL for ReCode AI"}, "recode.ai.sessionTTLHours": {"type": "number", "default": 24, "minimum": 1, "maximum": 168, "description": "Session time-to-live in hours (1-168)"}, "recode.ai.userId": {"type": "string", "default": "", "description": "Optional user identifier for deterministic sessions"}, "recode.ai.autoConnect": {"type": "boolean", "default": true, "description": "Automatically connect to server on startup"}, "recode.ai.showSessionNotifications": {"type": "boolean", "default": true, "description": "Show notifications when new sessions are created"}}}}}