# Cognee Session-Based Memory Integration

## Problem Solved
Previously, Cognee memory was tied to `client_id` (WebSocket connection ID), which meant conversation history was lost when users reconnected. This implementation introduces **session-based memory tracking** that persists across multiple WebSocket connections.

## Solution Overview

### 1. Session Manager (`services/session_manager.py`)
- **Stateless session tracking** - no database required
- **Multiple session strategies**:
  - **Deterministic**: Based on `user_id + project_name + date`
  - **Explicit**: Client provides `session_id` in requests
  - **Cached**: Reuses recent sessions for same client
  - **Random**: Fallback for anonymous sessions

### 2. Updated Cognee Service (`services/cognee_service.py`)
- All memory operations now use `session_id` instead of `client_id`
- Backward compatible - still accepts `client_id` but resolves to session
- Memory datasets named: `agent_memory_{session_id}`

### 3. Integration Points

#### WebSocket Request Format
```json
{
  "type": "coder_request",
  "data": {
    "project_name": "my-project",
    "question": "Add authentication",
    "session_id": "optional-explicit-session-id",
    "user_id": "optional-user-identifier",
    "workspace_id": "optional-workspace-identifier"
  }
}
```

#### Session ID Resolution Logic
1. **Explicit session_id** provided → Use it
2. **user_id + project_name** → Generate deterministic session
3. **Cached session** for client_id → Reuse existing
4. **Fallback** → Generate random session

## Configuration

Add to your `.env` file:
```bash
# Session Management
SESSION_TIMEOUT_HOURS=24  # How long sessions stay active
COGNEE_ENABLED=true       # Enable Cognee memory
```

## Usage Examples

### 1. Deterministic Sessions (Recommended)
```python
# Client sends user_id and project_name
request_data = {
    "user_id": "user123",
    "project_name": "ecommerce-app",
    "question": "Add payment integration"
}
# Results in session_id: "a1b2c3d4e5f6g7h8" (deterministic)
```

### 2. Explicit Session Control
```python
# Client manages session explicitly
request_data = {
    "session_id": "my-custom-session-2024",
    "project_name": "ecommerce-app", 
    "question": "Continue payment integration"
}
```

### 3. Anonymous Sessions
```python
# No user context provided
request_data = {
    "project_name": "ecommerce-app",
    "question": "Help with React components"
}
# Results in random session_id that gets cached for this client
```

## Memory Continuity

### Before (Client-based)
```
WebSocket Connection 1 (client_abc123) → Memory Dataset: agent_memory_abc123
[Connection drops]
WebSocket Connection 2 (client_def456) → Memory Dataset: agent_memory_def456
❌ Previous conversation history lost
```

### After (Session-based)
```
WebSocket Connection 1 (client_abc123) → Session: user123_project_2024-07-25 → Memory Dataset: agent_memory_a1b2c3d4
[Connection drops]  
WebSocket Connection 2 (client_def456) → Session: user123_project_2024-07-25 → Memory Dataset: agent_memory_a1b2c3d4
✅ Conversation history preserved
```

## API Changes

### CogneeService Methods
```python
# OLD: client_id based
await cognee_service.get_compact_memory(client_id, query)
await cognee_service.store_interaction_memory(client_id, data)

# NEW: session-aware (backward compatible)
await cognee_service.get_compact_memory(client_id, query, context, request_data)
await cognee_service.store_interaction_memory(client_id, data, request_data)
```

### Session Manager Methods
```python
from services.session_manager import session_manager

# Get or create session for request
session_id = session_manager.get_or_create_session(client_id, request_data)

# List active sessions
sessions = session_manager.list_active_sessions()

# Clean up expired sessions
session_manager.cleanup_expired_sessions()
```

## Benefits

1. **Conversation Continuity**: Memory persists across reconnections
2. **Multi-device Support**: Same session accessible from different devices
3. **Project Scoping**: Different projects maintain separate memory contexts
4. **Stateless Design**: No database required, works with existing infrastructure
5. **Backward Compatible**: Existing clients continue to work
6. **Flexible**: Supports multiple session strategies

## Migration

Existing client-based memories can be migrated to session-based:

```python
# Migration utility (to be implemented)
session_manager.migrate_client_to_session(
    old_client_id="abc123",
    new_session_id="user123_project_2024-07-25", 
    new_client_id="def456"
)
```

## Testing

Test different session scenarios:

```bash
# Test deterministic sessions
curl -X POST ws://localhost:8000/ws/coder/test123 \
  -d '{"type":"coder_request","data":{"user_id":"user1","project_name":"test","question":"Hello"}}'

# Test explicit sessions  
curl -X POST ws://localhost:8000/ws/coder/test456 \
  -d '{"type":"coder_request","data":{"session_id":"my-session","project_name":"test","question":"Continue"}}'
```

## Monitoring

Session activity can be monitored via logs:
```
🔑 Generated deterministic session ID: a1b2c3d4 for user1/test-project
🧠 🔍 Using session a1b2c3d4 for memory retrieval (client: test123...)
🧠 💾 Using session a1b2c3d4 for memory storage (client: test123...)
```
