SLINGSHOT_URL=http://localhost:4000/chat/completions
ENCODING_TOKEN_MODEL=o200k_base
 

 

EXTENSIONS_TO_SKIP=.github,.git,.project,.factorypath,.classpath,.MF,.xml,.dtd,.DS_Store,.jar,.mvn,.yml,.yam
l,<PERSON><PERSON><PERSON><PERSON>,.cmd,.class
FOLDERS_TO_SKIP=target
 
#CODE_EMBEDDING_MODEL=Qodo/Qodo-Embed-1-1.5B
CODE_EMBEDDING_MODEL=microsoft/codebert-base
CODE_EMBEDDING_MODEL_DIMENSION=768
CODE_EMBEDDING_MODEL_LOCATION=./lib/qodo
CODE_EMBEDDING_IGNORE_LIST=.xml,.dtd,.yaml,target,.MF,.jar
CHUNK_SIZE=2000
CHUNK_OVERLAP=200
EMBEDDING_DIMENSION=1536
TOP_K=300


CODE_FOLDER=/Users/<USER>/Documents/ai/code/samartianservice-master
 
DEVICE=cpu

 
TOTAL_ALLOWED_ITERATIONS=20
LANCEDB_LOCATION=./db

MERKLE_REBUILD_INTERVAL=5

# Cognee Memory Framework with Session Support
COGNEE_ENABLED=true
COGNEE_MAX_MEMORY_ITEMS=3
COGNEE_MAX_MEMORY_CHARS=500

# Session Management: Fully client-side managed
# Server is stateless - accepts session_id from client and uses it directly
# No server-side session configuration needed
# Note: Cognee SDK uses datasets per client, no need for separate tables

# User Response Timeout (seconds) - How long to wait for user approval of code changes
RECODE_USER_RESPONSE_TIMEOUT=600