#!/usr/bin/env python3
"""
Startup script for ReCode AI FastAPI server
"""
import os
import sys
import signal
import logging
import asyncio
from pathlib import Path
import traceback

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global reference to websocket manager for cleanup
websocket_manager = None

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logger.info(f"🛑 Received signal {signum}, shutting down gracefully...")

    # Close all WebSocket connections
    if websocket_manager:
        logger.info("🔌 Shutting down WebSocket manager...")
        try:
            # Create a new event loop if none exists
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Run the shutdown coroutine
            loop.run_until_complete(websocket_manager.shutdown())
        except Exception as e:
            logger.error(f"❌ Error during WebSocket cleanup: {e}")

    logger.info("✅ Graceful shutdown complete")
    sys.exit(0)

def main():
    """Start the FastAPI server"""
    global websocket_manager

    try:
        import uvicorn
        from api import app, websocket_manager as ws_manager

        # Set global reference for signal handler
        websocket_manager = ws_manager

        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Get configuration from environment
        host = os.getenv("API_HOST", "127.0.0.1")
        port = int(os.getenv("API_PORT", "8000"))
        
        logger.info(f"🚀 Starting ReCode AI API server on {host}:{port}")
        logger.info(f"📁 Working directory: {current_dir}")
        
        # Start the server
        uvicorn.run(
            "api:app",
            host=host,
            port=port,
            reload=True,
            log_level="info",
            access_log=True
        )
        
    except ImportError as e:
        logger.error(f"❌ Missing dependencies: {e}")
        logger.error("💡 Please install dependencies: pip install -r requirements_api.txt")
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
