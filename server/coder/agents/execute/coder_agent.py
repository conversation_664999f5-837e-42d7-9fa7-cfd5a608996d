import os
import asyncio

from tools.ws_base_node import WebSocketBaseNode
from rich.console import Console

from utils.prompt_template import PromptTemplate
from utils.history import History
from utils.llm import LLM
from utils.code_index import CodeCompress
from websocket.utils import send_ws_message


class CoderAgent(WebSocketBaseNode):
    
    def __init__(self):
        # Note: client_id and ws_manager will be set from shared context
        super().__init__(None, None)
        self.max_retries = 1  # Set as integer, not string

    async def prep_async(self, shared):
       
        Console().print(f"🤖💥 Current iteration - [bold green]{shared["total_iterations"]}[/bold green] ")
        prompt = PromptTemplate().coder_prompt(shared["user_query"], shared["project_name"])
        print(f"code_applied ---FLAGgg  {shared["code_applied"]}")
        
        if shared["immediate_lsp_feedback"]:
            shared["user_query"] = f"Original Task was  - {shared["user_query"]}  But while code application These are these compilation issues found - Fix them .. {shared["immediate_lsp_feedback"]}"
        elif not shared["code_applied"]:
            prompt = PromptTemplate().include_all_histories(prompt, shared.get("history", []))
        
        # NEW: Add Cognee memory context to prompt
        # memory_context = await self._get_memory_context(shared)
        # if memory_context:
        #     prompt = PromptTemplate().inject_memory_into_prompt(prompt, memory_context)
        
        shared["coder_prompt"] = prompt
        return prompt

    async def exec_async(self, prompt):
       
        response = LLM().call_litellm(prompt)
        token_count = CodeCompress().analyze_tokens_from_content(prompt)
        Console().print(f"🤖💥 Total Tokens Consumed in this call - [bold red]{token_count}[/bold red]")
        Console().print(f"🤖✨ [bold green]Coder Agent[/bold green] ✨ \n")

        return response
    
    async def post_async(self, shared, prep_res, exec_res):
        #print(f"Coder Agent - POST - START -- exec_res {exec_res}" )
        
        # NEW: Store interaction in Cognee memory
        #await self._store_memory_interaction(shared, exec_res)
        
        # Existing logic
        history = History().add(shared, exec_res)
        client_id = shared.get("client_id")
        shared["immediate_lsp_feedback"]=""
        shared["code_applied"]=False
        
        
        # Use default max iterations if environment variable is not set
        max_iterations = int(os.getenv("TOTAL_ALLOWED_ITERATIONS", "10"))  # Default to 10 iterations
        if(shared["total_iterations"] <= max_iterations):
            next_tool = history["tool"]
            reason = history["reason"]
            print(f"🤖💥 Next tool --- {next_tool}")
            print(f"🤖💭 LLM Reasoning: {reason}")

           
              
            if reason:
                # Get websocket manager and client_id from shared context
                ws_manager = shared.get('ws_manager')
                client_id = shared.get('client_id')
                if ws_manager and client_id:
                    await send_ws_message(ws_manager, client_id, "agent_reasoning", {
                        "next_tool": next_tool,
                        "reasoning": reason,
                        "professional_message": self._get_professional_status_message(next_tool, reason),
                        "timestamp": asyncio.get_event_loop().time()
                    })

                    # Also send professional status message for general status updates
                    status_message = self._get_professional_status_message(next_tool, reason)
                    await ws_manager.broadcast_console_output(
                        client_id, status_message, "info")
        

           

            shared["total_iterations"] = shared["total_iterations"] + 1
            return next_tool
        Console().print(f"🤖💥 [bold red]You have run out of available limit for iterations, increase it and retry if necessary. [/bold red]")
        return "complete"

    def _get_professional_status_message(self, tool_name: str, reason: str) -> str:
        """Convert tool names and reasons into professional status messages"""
        tool_messages = {
            "dir_tool": "Analyzing project structure",
            "file_read_tool": "Reading file content",
            "semantic_search_tool": "Searching codebase",
            "code_expert_tool": "Analyzing code patterns",
            "code_applier_tool": "Applying code changes",
            "file_code_struc_tool": "Understanding code structure",
            "summary_tool": "Generating summary",
            "complete": "Task completed successfully"
        }

        # Get professional message or fallback to generic message
        professional_message = tool_messages.get(tool_name, f"🔧 Processing {tool_name}")

        # Extract file name from reason if it contains file information
        if "file" in reason.lower() and any(ext in reason for ext in ['.java', '.py', '.js', '.ts', '.cpp', '.c', '.h']):
            # Try to extract filename from reason
            import re
            file_match = re.search(r'(\w+\.\w+)', reason)
            if file_match:
                filename = file_match.group(1)
                if tool_name == "file_read_tool":
                    return f"📄 Reading {filename}"
                elif tool_name == "code_applier_tool":
                    return f"✏️ Updating {filename}"
                elif tool_name == "file_code_struc_tool":
                    return f"🏗️ Understanding structure of {filename}"

        return professional_message
