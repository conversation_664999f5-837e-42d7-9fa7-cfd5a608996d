from rich.console import Console
from rich.markdown import Markdown
from rich import print

from utils.prompt_template import PromptTemplate
from utils.llm import LLM

class CodeEditAgent():

    def generate(self, prompt):
       
        response = LLM().call_litellm(prompt)
        
        print(f"🤖✨ [bold green]Code Edit Agent[/bold green] ✨")
        
      
        
        Console().print(Markdown(f"{response}\n"))
        return response
    
    
