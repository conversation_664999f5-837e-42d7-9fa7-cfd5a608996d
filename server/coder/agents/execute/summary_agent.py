from rich.console import Console
from rich.markdown import Markdown
from rich import print

from utils.llm import LLM

class SummaryAgent():

    def generate(self, prompt):
        response = LLM().call_litellm(prompt)
        # Log to server console for debugging (not sent to client)
        print(f"🤖✨ [bold green]Code Summary Agent[/bold green] ✨")
        Console().print(Markdown(f"{response}\n"))
        return response
