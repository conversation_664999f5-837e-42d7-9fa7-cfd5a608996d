import logging
from rich.console import Console
from utils.prompt_template import PromptTemplate
from tools.ws_base_node import WebSocketBaseNode
from utils.llm import LLM
import re
from utils.yaml_processor import Yam<PERSON>, YAMLFixer
from websocket.utils import send_ws_message

logger = logging.getLogger(__name__)
console = Console()

class ActionableRequestAnalyzer(WebSocketBaseNode):
    async def _prep_impl(self, shared):
       

        prompt = PromptTemplate().request_analyzer_prompt(shared["user_query"], shared["project_name"])
        console.print("🤔 [bold cyan]Analyzing user request...[/bold cyan]")
        
        # NEW: Add Cognee memory context to prompt
        memory_context = await self._get_memory_context(shared)
        if memory_context:
            prompt = PromptTemplate().inject_memory_into_prompt(prompt, memory_context)
        
        return prompt

    async def _exec_impl(self, prompt):
        response = LLM().call_litellm(prompt)
       
        try:
            yaml_response = Yaml().process(response)
            
        except Exception as e:
            console.print(":bug: [bold yellow]Invalid YAML received, attempting to fix...[bold yellow]")
            yaml_block_match = re.search(r"```yaml\n(.*?)\n```", response, re.DOTALL)
            if not yaml_block_match:
                raise ValueError("No YAML block found in the text.")
            yaml_content = yaml_block_match.group(1)
            fix_result = YAMLFixer().fix_yaml(yaml_content)
            if fix_result.success:
                yaml_response = fix_result.data
                console.print(f"✅ [green]YAML fixed successfully. Fixes applied: {fix_result.fixes_applied}")
            else:
                console.print(f"❌ [red]Failed to fix YAML: {fix_result.original_error}")
                raise ValueError(f"Failed to parse YAML even after fixing: {fix_result.original_error}")
        return yaml_response

    async def _post_impl(self, shared, prep_res, exec_res):
        # NEW: Store interaction in Cognee memory
        await self._store_memory_interaction(shared, exec_res)
        
        shared["user_request_context"] = f" User ASK : {exec_res.get('user_ask', {})} AND Project Assessment : {exec_res.get('project_assessment', '')}"
        shared["tasks"] = exec_res.get("tasks", [])
        shared["acknowledgement"] = exec_res.get("user_acknowledgement", "I am currently analyzing your requirements ..  ")

        # Send acknowledgement to client first
        if shared.get("ws_manager") and shared.get("client_id"):
            await send_ws_message(shared["ws_manager"], shared["client_id"], "console_output", {
                "message": shared["acknowledgement"],
                "level": "info"
            })

        # Send tasks to client for professional display
        if shared.get("ws_manager") and shared.get("client_id") and shared["tasks"]:
            await send_ws_message(shared["ws_manager"], shared["client_id"], "tasks_identified", {
                "phase": "request_analysis",
                "tasks": shared["tasks"],
                "user_acknowledgement": shared["acknowledgement"]
            })

        # Send completion status
        if shared.get("ws_manager") and shared.get("client_id"):
            await send_ws_message(shared["ws_manager"], shared["client_id"], "agent_status", {
                "status": "request_analysis_complete",
                "details": f"Request analysis complete - {len(shared['tasks'])} tasks identified"
            })

        console.print("📋 [bold blue]Request analysis complete. [/bold blue]")
        return "default"
       