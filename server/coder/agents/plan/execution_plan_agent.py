import logging
from rich.console import Console
from utils.prompt_template import PromptTemplate
from tools.ws_base_node import WebSocketBaseNode
from utils.llm import LLM
import re
from utils.yaml_processor import <PERSON><PERSON><PERSON>, YAMLFixer
from websocket.utils import send_ws_message
from utils.history import History
import os

logger = logging.getLogger(__name__)
console = Console()

class ExecutionPlanAgent(WebSocketBaseNode):
    async def _prep_impl(self, shared):
      
        console.print(f"📝 [bold cyan] ExecutionPlanAgent .[/bold cyan] ")
        prompt = PromptTemplate().execution_plan_prompt(shared["user_query"])
        prompt = PromptTemplate().include_history(prompt, History().get_latest(shared))
        
        # NEW: Add Cognee memory context to prompt
        memory_context = await self._get_memory_context(shared)
        if memory_context:
            prompt = PromptTemplate().inject_memory_into_prompt(prompt, memory_context)
        
        return prompt

    async def _exec_impl(self, prompt):
        response = LLM().call_litellm(prompt)
        console.print(f"🧠 [bold green]LLM response received for execution plan.[/bold green] ")
        
        
        return response

    async def _post_impl(self, shared, prep_res, exec_res):
        # NEW: Store interaction in Cognee memory
        await self._store_memory_interaction(shared, exec_res)
        
        history = History().add(shared, exec_res)
      
        next_tool = history["tool"]
        reason = history["reason"]
        print(f"🤖💥 Next tool --- {next_tool}")
        print(f"🤖💭 LLM Reasoning: {reason}")

        # Send the reasoning to the client
    
        if shared.get("ws_manager") and shared.get("client_id") and reason:
            await send_ws_message(shared["ws_manager"], shared["client_id"], "console_output", {
                "message": f"{reason}",
                "level": "info"
            })

       

        console.print("📋 [bold blue]Execution plan complete. Tasks updated.[/bold blue]")
        return next_tool
       