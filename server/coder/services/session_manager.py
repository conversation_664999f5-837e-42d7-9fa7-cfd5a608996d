"""
Session Manager for tracking conversation continuity across WebSocket connections
Provides stateless session ID management for Cognee memory integration
"""
import os
import hashlib
import json
import logging
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
import uuid

logger = logging.getLogger(__name__)

class SessionManager:
    """
    Manages session IDs for conversation continuity across multiple WebSocket connections.
    Provides stateless session tracking that persists beyond individual client connections.
    """
    
    def __init__(self):
        self.session_cache = {}  # In-memory cache for active sessions
        self.session_timeout_hours = int(os.getenv("SESSION_TIMEOUT_HOURS", "24"))
        
    def generate_session_id(self, user_identifier: str = None, project_name: str = None) -> str:
        """
        Generate a deterministic or random session ID based on user context.
        
        Args:
            user_identifier: Optional user/workspace identifier for deterministic sessions
            project_name: Optional project name for scoping sessions
            
        Returns:
            session_id: Unique session identifier
        """
        if user_identifier and project_name:
            # Deterministic session ID based on user + project
            session_data = f"{user_identifier}:{project_name}:{datetime.now().strftime('%Y-%m-%d')}"
            session_id = hashlib.sha256(session_data.encode()).hexdigest()[:16]
            logger.info(f"🔑 Generated deterministic session ID: {session_id} for {user_identifier}/{project_name}")
        else:
            # Random session ID for anonymous or one-off sessions
            session_id = str(uuid.uuid4())[:16]
            logger.info(f"🔑 Generated random session ID: {session_id}")
            
        return session_id
    
    def get_or_create_session(self, client_id: str, request_data: Dict[str, Any]) -> str:
        """
        Get existing session ID or create a new one based on request context.
        
        Args:
            client_id: Current WebSocket client ID
            request_data: Request data containing potential session context
            
        Returns:
            session_id: Session ID to use for memory operations
        """
        # Check if session_id is explicitly provided in request
        explicit_session_id = request_data.get("session_id")
        if explicit_session_id:
            self._update_session_cache(explicit_session_id, client_id, request_data)
            logger.info(f"🔑 Using explicit session ID: {explicit_session_id} for client {client_id[:8]}...")
            return explicit_session_id
        
        # Check if we can derive session from user/project context
        user_identifier = request_data.get("user_id") or request_data.get("workspace_id")
        project_name = request_data.get("project_name")
        
        if user_identifier and project_name:
            # Use deterministic session based on user + project + date
            session_id = self.generate_session_id(user_identifier, project_name)
            self._update_session_cache(session_id, client_id, request_data)
            return session_id
        
        # Fallback: check if client has an active session in cache
        for session_id, session_info in self.session_cache.items():
            if session_info.get("current_client_id") == client_id:
                if not self._is_session_expired(session_info):
                    logger.info(f"🔑 Reusing cached session ID: {session_id} for client {client_id[:8]}...")
                    return session_id
        
        # Last resort: create new random session
        session_id = self.generate_session_id()
        self._update_session_cache(session_id, client_id, request_data)
        return session_id
    
    def _update_session_cache(self, session_id: str, client_id: str, request_data: Dict[str, Any]):
        """Update session cache with current client and context"""
        self.session_cache[session_id] = {
            "current_client_id": client_id,
            "last_activity": datetime.now(),
            "project_name": request_data.get("project_name"),
            "user_identifier": request_data.get("user_id") or request_data.get("workspace_id"),
            "created_at": self.session_cache.get(session_id, {}).get("created_at", datetime.now())
        }
    
    def _is_session_expired(self, session_info: Dict[str, Any]) -> bool:
        """Check if session has expired based on last activity"""
        if "last_activity" not in session_info:
            return True
        
        expiry_time = session_info["last_activity"] + timedelta(hours=self.session_timeout_hours)
        return datetime.now() > expiry_time
    
    def cleanup_expired_sessions(self):
        """Remove expired sessions from cache"""
        expired_sessions = [
            session_id for session_id, session_info in self.session_cache.items()
            if self._is_session_expired(session_info)
        ]
        
        for session_id in expired_sessions:
            del self.session_cache[session_id]
            logger.info(f"🔑 Cleaned up expired session: {session_id}")
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        return self.session_cache.get(session_id)
    
    def list_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """List all active (non-expired) sessions"""
        self.cleanup_expired_sessions()
        return self.session_cache.copy()
    
    def migrate_client_to_session(self, old_client_id: str, new_session_id: str, new_client_id: str):
        """
        Migrate memory from old client_id to new session_id.
        This is useful for transitioning existing client-based memories to session-based.
        """
        # This would be implemented to work with CogneeService
        # to migrate existing client_id datasets to session_id datasets
        logger.info(f"🔄 Migration needed: {old_client_id} -> {new_session_id} (client: {new_client_id})")
        # Implementation would call CogneeService migration methods

# Global session manager instance
session_manager = SessionManager()
