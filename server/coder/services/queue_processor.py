"""
Queue processor service for handling queued coder requests
"""
import asyncio
import logging
from typing import Dict, Any
from websocket.manager import websocket_manager
from websocket.handlers import _process_coder_request

logger = logging.getLogger(__name__)

class QueueProcessor:
    """Processes queued requests in background"""
    
    def __init__(self):
        self.is_running = False
        self._task = None
    
    def start(self):
        """Start the queue processor"""
        if not self.is_running:
            self.is_running = True
            self._task = asyncio.create_task(self._process_queue())
           
    
    def stop(self):
        """Stop the queue processor"""
        self.is_running = False
        if self._task:
            self._task.cancel()
           
    
    async def _process_queue(self):
        """Main queue processing loop"""
        while self.is_running:
            try:
                # Check for queued requests
                queued_request = websocket_manager.get_next_queued_request()
                
                if queued_request:
                    client_id = queued_request['client_id']
                    request_data = queued_request['request_data']
                    
                    logger.info(f"📋 Processing queued request for client: {client_id}")
                    
                    # Send processing notification to client
                    await websocket_manager.send_message(client_id, {
                        "type": "queue_status",
                        "data": {
                            "status": "processing",
                            "message": "Your request is now being processed"
                        }
                    })
                    
                    # Process the request
                    try:
                        await _process_coder_request(client_id, request_data)
                    except Exception as e:
                        logger.error(f"❌ Error processing queued request for {client_id}: {e}")
                        # Send error notification
                        await websocket_manager.send_message(client_id, {
                            "type": "coder_response",
                            "data": {
                                "status": "error",
                                "message": f"Error processing request: {str(e)}"
                            }
                        })
                    finally:
                        # Mark request as completed
                        websocket_manager.mark_request_completed(client_id)
                
                # Sleep briefly before checking again
                await asyncio.sleep(1)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in queue processor: {e}")
                await asyncio.sleep(5)  # Wait longer on error

# Global queue processor instance
queue_processor = QueueProcessor()
