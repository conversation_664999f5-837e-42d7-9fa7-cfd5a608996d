"""
Indexing service for handling full codebase indexing operations
"""
import time
import logging
from typing import Dict, List, Optional
from datetime import datetime
from collections import defaultdict
import asyncio

from utils.kg import Kg

logger = logging.getLogger(__name__)

class IndexingService:
    """Service for handling full indexing operations"""

    def __init__(self):
        self.kg = Kg()
        # In-memory queue for batches - no need for stateful sessions
        self.batch_queue = defaultdict(list)  # project_id -> list of batches
        self.project_stats = defaultdict(dict)  # project_id -> stats

        logger.info("🔧 IndexingService initialized with in-memory batch queue")
    
    # REMOVED: initialize_session - no longer needed with stateless design
    
    async def process_batch(self, project_id: str, files: List[Dict], batch_index: int, merkle_tree: Dict) -> Dict:
        """Process a batch of files for indexing (stateless)"""
        try:
            logger.info(f"📦 Processing batch {batch_index + 1} for project: {project_id}")
            logger.info(f"📄 Files in batch: {len(files)}")

            # Initialize project stats if first time
            if project_id not in self.project_stats:
                self.project_stats[project_id] = {
                    "start_time": datetime.now().isoformat(),
                    "processed_batches": 0,
                    "total_files": 0,
                    "last_merkle_tree": None
                }

            # Update stats
            self.project_stats[project_id]["processed_batches"] = batch_index + 1
            self.project_stats[project_id]["total_files"] += len(files)
            self.project_stats[project_id]["last_merkle_tree"] = merkle_tree
            
            processed_files = 0
            
            for i, file_data in enumerate(files):
                print(f"🔄 Processing file {i+1}/{len(files)}  filePath: {file_data.get('filePath', 'NOT_FOUND')} of  type: {file_data.get('type', 'NOT_FOUND')}")
                
             
               
               
                print(f"  chunks count: {len(file_data.get('chunks', []))}")

                # Extract chunks content for MerkleIndexer
                chunks_content = []
                if file_data.get('chunks'):
                    for chunk in file_data['chunks']:
                        if isinstance(chunk, dict) and 'text' in chunk:
                            chunks_content.append(chunk['text'])
                        elif isinstance(chunk, str):
                            chunks_content.append(chunk)
                    print(f"  extracted chunks count: {len(chunks_content)}")
                  

                try:
                    if file_data.get("type") == "file":
                        file_path = file_data.get('filePath', 'unknown')
                       

                        # Create simplified structure for KG indexing
                        kg_data = {
                            'filePath': file_path,
                            'chunks': chunks_content
                        }

                        # Use KG for indexing with proper embeddings
                        kg = Kg()
                        success = kg.index_file_from_client_content(project_id, kg_data)
                        if success:
                            processed_files += 1
                            print(f"✅ Successfully processed file: {file_path}")
                        else:
                            print(f"❌ Failed to process file: {file_path}")

                except Exception as e:
                    file_path = file_data.get('filePath', file_data.get('obfuscatedPath', 'unknown'))
                    logger.error(f"❌ Failed to process file {file_path}: {e}")
                    print(f"❌ Exception details: {str(e)}")
                    continue
            
            # Update Merkle tree after processing batch using MerkleIndexer
            await self._store_merkle_tree_with_indexer(project_id, merkle_tree)
            
            logger.info(f"✅ Batch {batch_index + 1} completed: {processed_files}/{len(files)} files processed")
            
            return {
                "status": "success",
                "batch_index": batch_index,
                "processed_files": processed_files,
                "total_files": len(files)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to process batch {batch_index + 1} for {project_id}: {e}")
            raise
    


    async def _store_merkle_tree_with_indexer(self, project_id: str, merkle_tree: Dict):
        """Store Merkle tree using MerkleStore (not full MerkleIndexer to avoid file system operations)"""
        try:
            logger.info(f"💾 Start Storing Merkle tree for project: {project_id}")

            # Import MerkleStore directly to avoid file system operations in MerkleIndexer
            from utils.merkle.merkle_store import MerkleStore

            # Create MerkleStore instance for this project
            merkle_store = MerkleStore(project_id)

            # Extract root hash and DAG data from the client's merkle tree format
            root_hash = merkle_tree.get("root", "")

            # Convert client format to the format expected by MerkleStore
            dag_data = {
                "root": root_hash,
                "leaves": merkle_tree.get("leaves", []),
                "tree": merkle_tree.get("tree", {}),
                "fileMap": merkle_tree.get("fileMap", {}),
                "timestamp": merkle_tree.get("timestamp", int(time.time() * 1000))
            }

            # Store using MerkleStore directly (avoids file system operations)
            merkle_store.save_dag(root_hash, dag_data)

            logger.info(f"✅ Merkle tree stored successfully for project: {project_id}")
            logger.debug(f"🌳 Root hash: {root_hash}")

        except Exception as e:
            logger.error(f"❌ Failed to store Merkle tree for {project_id}: {e}")
            raise
    
    def get_session_status(self, project_id: str) -> Optional[Dict]:
        """Get the status of an indexing session"""
        return self.active_sessions.get(project_id)
    
    def complete_session(self, project_id: str):
        """Mark an indexing session as complete"""
        if project_id in self.active_sessions:
            self.active_sessions[project_id]["status"] = "completed"
            self.active_sessions[project_id]["end_time"] = datetime.now().isoformat()
            logger.info(f"🎉 Indexing session completed for project: {project_id}")
    
    def cleanup_session(self, project_id: str):
        """Clean up an indexing session"""
        if project_id in self.active_sessions:
            del self.active_sessions[project_id]
            logger.info(f"🧹 Cleaned up session for project: {project_id}")
    
    def get_active_sessions(self) -> Dict:
        """Get all active indexing sessions"""
        return self.active_sessions.copy()
    
    async def get_project_stats(self, project_id: str) -> Dict:
        """Get statistics for a project"""
        try:
            table = self.kg._get_table(project_id)
            if table is None:
                return {"total_chunks": 0, "total_files": 0}
            
            # Get total chunks
            total_chunks = table.count_rows()
            
            # Get unique files
            files_result = table.search().select(["file"]).to_list()
            unique_files = len(set(row["file"] for row in files_result))
            
            return {
                "total_chunks": total_chunks,
                "total_files": unique_files,
                "project_id": project_id
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get stats for project {project_id}: {e}")
            return {"total_chunks": 0, "total_files": 0, "error": str(e)}
