"""
# Cognee Memory Framework (using existing embedding infrastructure)
# Fixed for LiteLLM endpoint without authentication issues
"""
import os
import asyncio
from typing import Dict, List, Optional, Any
import logging
import json
from datetime import datetime
import networkx as nx
import cognee
from cognee.api.v1.search import SearchType

from dotenv import load_dotenv
load_dotenv(override=True)





logger = logging.getLogger(__name__)

class CogneeService:
    """Service for managing Cognee memory operations using official Cognee SDK"""
    
    def __init__(self):
        self.enabled = os.getenv("COGNEE_ENABLED", "false").lower() == "true" 
        self.max_memory_items = int(os.getenv("COGNEE_MAX_MEMORY_ITEMS", "3"))
        self.max_memory_chars = int(os.getenv("COGNEE_MAX_MEMORY_CHARS", "500"))
        
        if self.enabled:
            try:
                self._initialize_cognee()
                logger.info("🧠 ✅ Cognee SDK service initialized successfully")
            except Exception as e:
                logger.warning(f"🧠 Cognee SDK initialization failed: {e}")
                self.enabled = False
        else:
            logger.info("🧠 Cognee service disabled (check COGNEE_ENABLED and cognee installation)")
    
    def _initialize_cognee(self):
        """Initialize Cognee SDK with configuration for LiteLLM"""
        try:
     
            
            # Verify environment variables are set correctly
            llm_provider = os.getenv("LLM_PROVIDER")
            llm_endpoint = os.getenv("LLM_ENDPOINT") 
            llm_model = os.getenv("LLM_MODEL")
            llm_api_key = os.getenv("LLM_API_KEY")
            
           
            
            logger.info(f"🧠 Cognee environment config:")
            logger.info(f"🧠   LLM_PROVIDER: {llm_provider}")
            logger.info(f"🧠   LLM_ENDPOINT: {llm_endpoint}")
            logger.info(f"🧠   LLM_MODEL: {llm_model}")
           
           
            
            # Verify the configuration is correct
            if llm_provider != "custom":
                logger.warning(f"🧠 ⚠️  LLM_PROVIDER should be 'custom', found: {llm_provider}")
            if not llm_endpoint:
                logger.error(f"🧠 ❌ LLM_ENDPOINT not set!")
            if not llm_model:
                logger.error(f"🧠 ❌ LLM_MODEL not set!")
            if not llm_api_key:
                logger.error(f"🧠 ❌ LLM_API_KEY not set!")
                
           
            
            logger.info(f"🧠 Cognee will use custom provider at: {llm_endpoint}")
                
            # Suppress verbose LiteLLM and Cognee logs - keep only critical/milestone logs
            import logging
            
            # Suppress LiteLLM verbose logs completely
            logging.getLogger("LiteLLM").setLevel(logging.ERROR)
            logging.getLogger("litellm").setLevel(logging.ERROR)
            
            # Suppress Cognee internal logs but keep our milestone logs
            logging.getLogger("cognee").setLevel(logging.CRITICAL)
            logging.getLogger("cognee.shared.logging_utils").setLevel(logging.CRITICAL)
            logging.getLogger("cognee.api").setLevel(logging.CRITICAL)
            logging.getLogger("cognee.infrastructure").setLevel(logging.CRITICAL)
            logging.getLogger("cognee.shared").setLevel(logging.CRITICAL)
            
            # Also suppress any other verbose loggers that might appear
            logging.getLogger("httpx").setLevel(logging.WARNING)
            logging.getLogger("httpcore").setLevel(logging.WARNING)
            logging.getLogger("urllib3").setLevel(logging.WARNING)
            
            # Configure storage paths with proper directory creation
            lancedb_location = os.getenv("LANCEDB_LOCATION", "./db")
            
            # Ensure the directory exists and is absolute
            if not os.path.isabs(lancedb_location):
                lancedb_location = os.path.abspath(lancedb_location)
            
            # Create directory if it doesn't exist
            os.makedirs(lancedb_location, exist_ok=True)
            
            # Create a cognee-specific subdirectory to avoid conflicts
            cognee_data_dir = os.path.join(lancedb_location, "cognee")
            os.makedirs(cognee_data_dir, exist_ok=True)
            
            cognee.config.data_root_directory = cognee_data_dir
            logger.info(f"🧠 Cognee data directory: {cognee_data_dir}")
            
            # Test directory permissions
            test_file = os.path.join(cognee_data_dir, "test_write.tmp")
            try:
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                logger.info("🧠 Cognee directory write permissions verified")
            except Exception as perm_error:
                logger.error(f"🧠 Cognee directory write test failed: {perm_error}")
                raise
            
            # Initialize NetworkX for graph operations
            self.graph = nx.DiGraph()
            
            logger.info("🧠 Cognee SDK initialization complete")
    
            
        except Exception as e:
            logger.error(f"🧠 Failed to initialize Cognee: {e}")
            logger.error(f"🧠 Current working directory: {os.getcwd()}")
            logger.error(f"🧠 LANCEDB_LOCATION env var: {os.getenv('LANCEDB_LOCATION')}")
            raise
    
    async def get_compact_memory(self, client_id: str, query: str = "", context: dict = None, request_data: dict = None) -> dict:
        """Get compact memory context for agent prompts using Cognee SDK search"""
        if not self.enabled:
            return {"memory_items": [], "total_memories": 0}
        
        try:
            # Use client-provided session_id or fallback to client_id
            session_id = (request_data or {}).get("session_id", client_id)
            logger.info(f"🧠 🔍 Using session {session_id} for memory retrieval")
            
            # Search using Cognee SDK with session ID
            memory_results = await self._search_memory(session_id, query, context)
            
            if not memory_results:
                return {}
            
            # Format results for prompt injection
            memory_context = {
                "relevant_memories": memory_results[:self.max_memory_items],
                "memory_count": len(memory_results),
                "search_query": query
            }
            
            # Log milestone: memory content prepared
            if memory_results:
                content_preview = memory_context["relevant_memories"][0]["content"][:100].replace('\n', ' ') + '...' if len(memory_context["relevant_memories"][0]["content"]) > 100 else memory_context["relevant_memories"][0]["content"]
                logger.info(f"🧠 📝 Memory content: {content_preview}")
            return memory_context
            
        except Exception as e:
            logger.warning(f"🧠 Failed to retrieve memory context: {e}")
            return {}
    
    async def store_interaction_memory(self, client_id: str, interaction_data: dict, request_data: dict = None):
        """Store agent interaction in Cognee memory using SDK"""
        if not self.enabled:
            return
        
        try:
            # Use client-provided session_id or fallback to client_id
            session_id = (request_data or {}).get("session_id", client_id)
            logger.info(f"🧠 💾 Using session {session_id} for memory storage")
            
            await self._store_memory(session_id, interaction_data)
            
            logger.info(f"🧠 💾 Stored memory for session {session_id} (agent: {interaction_data.get('agent_type', 'unknown')})")
        except Exception as e:
            # Always log storage errors - this is critical
            logger.error(f"🧠 ❌ Memory storage FAILED for client {client_id[:8]}...: {e}")
            # Also log the specific error details for debugging
            logger.debug(f"🧠 Error details: {str(e)}")
            logger.debug(f"🧠 Cognee config - endpoint: {getattr(cognee.config, 'llm_endpoint', 'Not set')}")
            logger.debug(f"🧠 Cognee config - model: {getattr(cognee.config, 'llm_model', 'Not set')}")
    
    async def _search_memory(self, session_id: str, query: str, context: dict = None) -> List[Dict]:
        """Search memory using Cognee SDK with GRAPH_COMPLETION for relationship-aware retrieval"""
        try:
            dataset_name = f"agent_memory_{session_id}"
            
            # Only log milestone: memory search initiated
            logger.info(f"🧠 🔍 Memory search for session {session_id} (query: {query[:50]}...)")
            
            # Check if dataset exists first to avoid DatasetNotFoundError
            try:
                # Use GRAPH_COMPLETION for contextual, relationship-aware search
                results = await cognee.search(
                    query_type=SearchType.RAG_COMPLETION,
                    query_text=query,
                    datasets=[dataset_name]
                )
            except Exception as search_error:
                if "DatasetNotFoundError" in str(search_error) or "No datasets found" in str(search_error):
                    # This is expected for new clients - not an error, just info
                    logger.info(f"🧠 💭 No memories found for new session {session_id} (first interaction) -- {search_error}\n")
                    return []  # Return empty results for new clients
                elif "AuthenticationError" in str(search_error):
                    # Log authentication errors with more detail
                    logger.error(f"🧠 🔑 Authentication error in search: {search_error}")
                    logger.error(f"🧠 Check LiteLLM endpoint configuration: {cognee.config.llm_endpoint}")
                    return []
                else:
                    # Log other search errors but don't crash the system
                    logger.warning(f"🧠 ⚠️ Memory search issue for session {session_id}: {search_error}")
                    return []  # Gracefully return empty results
            
            # Log milestone: successful memory retrieval
            logger.info(f"🧠 ✅ Retrieved {len(results)} memories for session {session_id}")
            
            # Process and format results for agent consumption
            formatted_results = []
            
            # Handle different possible result formats
            if not results:
                logger.debug("🧠 No results returned from Cognee search")
                return []
            
            # Convert results to list if it's not already
            if not isinstance(results, list):
                results = [results] if results else []
            
            for i, result in enumerate(results[:self.max_memory_items]):
                logger.debug(f"🧠 Processing result {i}: type={type(result)}, value={result}")
                
                # Try to extract content in different ways
                content = ""
                if hasattr(result, 'content'):
                    content = str(result.content)
                elif hasattr(result, 'text'):
                    content = str(result.text)
                elif isinstance(result, dict):
                    content = result.get('content', result.get('text', str(result)))
                else:
                    content = str(result)
                
                # Try to extract timestamp
                timestamp = datetime.now().isoformat()
                if hasattr(result, 'timestamp'):
                    timestamp = str(result.timestamp)
                elif isinstance(result, dict) and 'timestamp' in result:
                    timestamp = str(result['timestamp'])
                
                # Try to extract relevance/score
                relevance = 1.0
                if hasattr(result, 'score'):
                    relevance = float(result.score)
                elif hasattr(result, 'relevance'):
                    relevance = float(result.relevance)
                elif isinstance(result, dict):
                    relevance = float(result.get('score', result.get('relevance', 1.0)))
                
                formatted_result = {
                    "content": content[:self.max_memory_chars],
                    "relevance": relevance,
                    "timestamp": timestamp,
                    "type": "graph_completion"
                }
                formatted_results.append(formatted_result)
                
                logger.debug(f"🧠 Formatted result {i}: {formatted_result}")
            
            # Log milestone: memory formatting complete
            if formatted_results:
                logger.info(f"🧠 📋 Formatted {len(formatted_results)} memories for injection")
            
            logger.info(f"🧠 Found {len(formatted_results)} graph completion results for session {session_id}")
            return formatted_results
            
        except Exception as e:
            # Always log errors - this is critical
            logger.error(f"🧠 ❌ Memory search FAILED for session {session_id}: {e}")
            return []
    
    async def _store_memory(self, session_id: str, data: Dict):
        """Store memory using Cognee SDK with dataset-based approach"""
        try:
            # Format interaction data for Cognee
            memory_content = self._format_memory_content(session_id, data)
            
            # Create dataset name for this session
            dataset_name = f"agent_memory_{session_id}"
            
            # Add to Cognee knowledge graph with dataset
            await cognee.add([memory_content], dataset_name)
            
            # Build/update knowledge graph for this dataset
            #await cognee.cognify([dataset_name])
            
            # Update local NetworkX graph for relationship analysis
            #self._update_local_graph(session_id, data)
            
            # Log success
            logger.info(f"🧠 ✅ Memory stored successfully for session {session_id}")
            
        except Exception as e:
            # Always log storage errors with more detail
            logger.error(f"🧠 ❌ Memory storage FAILED for session {session_id}: {e}")
            if "AuthenticationError" in str(e):
                logger.error(f"🧠 🔑 LiteLLM Authentication issue - check endpoint: {cognee.config.llm_endpoint}")
                logger.error(f"🧠 💡 Ensure your LiteLLM server accepts dummy keys or disable auth")
    
    def _format_memory_content(self, session_id: str, data: Dict) -> str:
        """Format interaction data for Cognee knowledge graph storage"""
        agent_type = data.get('agent_type', 'Unknown')
        query = data.get('query', '')
        response = data.get('response', '')
        project = data.get('project', 'default')
        timestamp = datetime.now().isoformat()
        
        # Create structured content optimized for knowledge graph extraction
        content = f"""# Agent Interaction Record

**Session ID**: {session_id}
**Agent Type**: {agent_type}
**Project**: {project}
**Timestamp**: {timestamp}

## User Query
{query}

## Agent Response
{response}

## Context
- This interaction involved the {agent_type} agent
- Working on project: {project}
- Session: {session_id}
- Recorded at: {timestamp}
"""
        
        return content
    
    def _update_local_graph(self, session_id: str, data: Dict):
        """Update local NetworkX graph with interaction data"""
        try:
            if not hasattr(self, 'graph'):
                self.graph = nx.DiGraph()
            
            # Create nodes
            session_node = f"session_{session_id}"
            agent_node = f"agent_{data.get('agent_type', 'Unknown')}"
            project_node = f"project_{data.get('project', 'default')}"
            
            # Add nodes with metadata
            self.graph.add_node(session_node, type="session", id=session_id)
            self.graph.add_node(agent_node, type="agent", name=data.get('agent_type', 'Unknown'))
            self.graph.add_node(project_node, type="project", name=data.get('project', 'default'))
            
            # Add relationships
            self.graph.add_edge(session_node, agent_node, relation="uses", timestamp=datetime.now())
            self.graph.add_edge(session_node, project_node, relation="works_on", timestamp=datetime.now())
            self.graph.add_edge(agent_node, project_node, relation="assists_with", timestamp=datetime.now())
            
        except Exception as e:
            logger.warning(f"🧠 Failed to update NetworkX graph: {e}")
    
    