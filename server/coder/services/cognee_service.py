"""
# Cognee Memory Framework (using existing embedding infrastructure)
# Fixed for LiteLLM endpoint without authentication issues
"""
import os
import asyncio
from typing import Dict, List, Optional, Any
import logging
import json
from datetime import datetime
import networkx as nx
import cognee
from cognee.api.v1.search import SearchType

from dotenv import load_dotenv
load_dotenv(override=True)





logger = logging.getLogger(__name__)

class CogneeService:
    """Service for managing Cognee memory operations using official Cognee SDK"""
    
    def __init__(self):
        self.enabled = os.getenv("COGNEE_ENABLED", "false").lower() == "true" 
        self.max_memory_items = int(os.getenv("COGNEE_MAX_MEMORY_ITEMS", "3"))
        self.max_memory_chars = int(os.getenv("COGNEE_MAX_MEMORY_CHARS", "500"))
        
        if self.enabled:
            try:
                self._initialize_cognee()
                logger.info("🧠 ✅ Cognee SDK service initialized successfully")
            except Exception as e:
                logger.warning(f"🧠 Cognee SDK initialization failed: {e}")
                self.enabled = False
        else:
            logger.info("🧠 Cognee service disabled (check COGNEE_ENABLED and cognee installation)")
    
    def _initialize_cognee(self):
        """Initialize Cognee SDK with configuration for LiteLLM"""
        try:
            # Check all environment variables in one shot
            env_vars = {
                'LLM_PROVIDER': os.getenv("LLM_PROVIDER"),
                'LLM_ENDPOINT': os.getenv("LLM_ENDPOINT"),
                'LLM_MODEL': os.getenv("LLM_MODEL"),
                'LLM_API_KEY': os.getenv("LLM_API_KEY"),
                'LANCEDB_LOCATION': os.getenv("LANCEDB_LOCATION", "./db")
            }

            # Validate required environment variables
            missing_vars = [k for k, v in env_vars.items() if not v and k != 'LANCEDB_LOCATION']
            if missing_vars:
                logger.error(f"🧠 ❌ Missing environment variables: {', '.join(missing_vars)}")
                raise ValueError(f"Missing required environment variables: {missing_vars}")

            if env_vars['LLM_PROVIDER'] != "custom":
                logger.warning(f"🧠 ⚠️ LLM_PROVIDER should be 'custom', found: {env_vars['LLM_PROVIDER']}")

            logger.info(f"🧠 Cognee initialized with endpoint: {env_vars['LLM_ENDPOINT']}")

            # Configure logging levels for external libraries
            log_configs = {
                "LiteLLM": logging.ERROR,
                "litellm": logging.ERROR,
                "cognee": logging.CRITICAL,
                "cognee.shared.logging_utils": logging.CRITICAL,
                "cognee.api": logging.CRITICAL,
                "cognee.infrastructure": logging.CRITICAL,
                "cognee.shared": logging.CRITICAL,
                "httpx": logging.WARNING,
                "httpcore": logging.WARNING,
                "urllib3": logging.WARNING
            }

            for logger_name, level in log_configs.items():
                logging.getLogger(logger_name).setLevel(level)
            
            # Setup storage directory
            lancedb_location = env_vars['LANCEDB_LOCATION']
            if not os.path.isabs(lancedb_location):
                lancedb_location = os.path.abspath(lancedb_location)

            cognee_data_dir = os.path.join(lancedb_location, "cognee")
            os.makedirs(cognee_data_dir, exist_ok=True)

            # Test directory permissions
            test_file = os.path.join(cognee_data_dir, "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)

            cognee.config.data_root_directory = cognee_data_dir
            self.graph = nx.DiGraph()

            logger.info(f"🧠 Cognee SDK initialized - data dir: {cognee_data_dir}")
    
            
        except Exception as e:
            logger.error(f"🧠 Failed to initialize Cognee: {e}")
            logger.error(f"🧠 Current working directory: {os.getcwd()}")
            logger.error(f"🧠 LANCEDB_LOCATION env var: {os.getenv('LANCEDB_LOCATION')}")
            raise
    
    async def get_compact_memory(self, client_id: str, query: str = "", context: dict = None, request_data: dict = None) -> dict:
        """Get compact memory context for agent prompts using Cognee SDK search"""
        if not self.enabled:
            return {"memory_items": [], "total_memories": 0}
        
        try:
            # Use client-provided session_id or fallback to client_id
            session_id = (request_data or {}).get("session_id", client_id)
            logger.info(f"🧠 🔍 Using session {session_id} for memory retrieval")
            
            # Search using Cognee SDK with session ID
            memory_results = await self._search_memory(session_id, query, context)
            
            if not memory_results:
                return {}
            
            # Format results for prompt injection
            memory_context = {
                "relevant_memories": memory_results[:self.max_memory_items],
                "memory_count": len(memory_results),
                "search_query": query
            }
            
            # Log milestone: memory content prepared
            if memory_results:
                content_preview = memory_context["relevant_memories"][0]["content"][:100].replace('\n', ' ') + '...' if len(memory_context["relevant_memories"][0]["content"]) > 100 else memory_context["relevant_memories"][0]["content"]
                logger.info(f"🧠 📝 Memory content: {content_preview}")
            return memory_context
            
        except Exception as e:
            logger.warning(f"🧠 Failed to retrieve memory context: {e}")
            return {}
    
    async def store_interaction_memory(self, client_id: str, interaction_data: dict, request_data: dict = None):
        """Store agent interaction in Cognee memory using SDK"""
        if not self.enabled:
            return

        try:
            session_id = (request_data or {}).get("session_id", client_id)

            # Format interaction data for Cognee
            memory_content = self._format_memory_content(session_id, interaction_data)
            dataset_name = f"agent_memory_{session_id}"

            # Add to Cognee knowledge graph
            await cognee.add([memory_content], dataset_name)

            logger.info(f"🧠 💾 Stored memory for session {session_id} (agent: {interaction_data.get('agent_type', 'unknown')})")

        except Exception as e:
            logger.error(f"🧠 ❌ Memory storage FAILED for client {client_id[:8]}...: {e}")
            if "AuthenticationError" in str(e):
                logger.error(f"🧠 🔑 LiteLLM Authentication issue - check endpoint configuration")

    async def _search_memory(self, session_id: str, query: str) -> List[Dict]:
        """Search memory using Cognee SDK"""
        try:
            dataset_name = f"agent_memory_{session_id}"
            logger.info(f"🧠 🔍 Memory search for session {session_id}")

            try:
                results = await cognee.search(
                    query_type=SearchType.RAG_COMPLETION,
                    query_text=query,
                    datasets=[dataset_name]
                )
            except Exception as search_error:
                error_msg = str(search_error)
                if "DatasetNotFoundError" in error_msg or "No datasets found" in error_msg:
                    logger.info(f"🧠 💭 No memories found for new session {session_id}")
                    return []
                elif "AuthenticationError" in error_msg:
                    logger.error(f"🧠 🔑 Authentication error in search")
                    return []
                else:
                    logger.warning(f"🧠 ⚠️ Memory search issue: {search_error}")
                    return []

            if not results:
                return []

            # Format results
            formatted_results = []
            for result in results[:self.max_memory_items]:
                # Extract content
                if hasattr(result, 'content'):
                    content = str(result.content)
                elif hasattr(result, 'text'):
                    content = str(result.text)
                elif isinstance(result, dict):
                    content = result.get('content', result.get('text', str(result)))
                else:
                    content = str(result)

                # Extract metadata
                timestamp = datetime.now().isoformat()
                if hasattr(result, 'timestamp'):
                    timestamp = str(result.timestamp)
                elif isinstance(result, dict) and 'timestamp' in result:
                    timestamp = str(result['timestamp'])

                relevance = 1.0
                if hasattr(result, 'score'):
                    relevance = float(result.score)
                elif isinstance(result, dict):
                    relevance = float(result.get('score', 1.0))

                formatted_results.append({
                    "content": content[:self.max_memory_chars],
                    "relevance": relevance,
                    "timestamp": timestamp,
                    "type": "graph_completion"
                })

            logger.info(f"🧠 ✅ Found {len(formatted_results)} memories for session {session_id}")
            return formatted_results
            
        except Exception as e:
            logger.error(f"🧠 ❌ Memory search FAILED for session {session_id}: {e}")
            return []
    
    def _format_memory_content(self, session_id: str, data: Dict) -> str:
        """Format interaction data for Cognee knowledge graph storage"""
        agent_type = data.get('agent_type', 'Unknown')
        query = data.get('query', '')
        response = data.get('response', '')
        project = data.get('project', 'default')
        timestamp = datetime.now().isoformat()
        
        # Create structured content optimized for knowledge graph extraction
        content = f"""# Agent Interaction Record

**Session ID**: {session_id}
**Agent Type**: {agent_type}
**Project**: {project}
**Timestamp**: {timestamp}

## User Query
{query}

## Agent Response
{response}

## Context
- This interaction involved the {agent_type} agent
- Working on project: {project}
- Session: {session_id}
- Recorded at: {timestamp}
"""
        
        return content
    
   