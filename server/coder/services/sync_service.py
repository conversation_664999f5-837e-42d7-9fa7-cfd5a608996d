"""
Sync service for handling differential sync operations
"""
import time
import logging
from typing import Dict, List, Optional
from datetime import datetime

from utils.kg import Kg

logger = logging.getLogger(__name__)

class SyncService:
    """Service for handling differential sync operations"""
    
    def __init__(self):
        self.kg = Kg()
        self.sync_sessions = {}  # Track active sync sessions

        logger.info("🔄 SyncService initialized")
    


    async def get_latest_merkle_tree(self, project_id: str) -> Optional[Dict]:
        """Get the latest Merkle tree for a project using MerkleStore directly"""
        try:
            #logger.info(f"🌳 Retrieving latest Merkle tree for project: {project_id}")

            # Import MerkleStore directly to avoid file system operations in MerkleIndexer
            from utils.merkle.merkle_store import MerkleStore

            # Create MerkleStore instance for this project
            merkle_store = MerkleStore(project_id)

            # Get current DAG from MerkleStore
            current_dag = merkle_store.get_current_dag()

            if not current_dag:
                logger.info(f"ℹ️ No Merkle tree found for project: {project_id}")
                return None

            # Convert from MerkleStore format to client format
            dag_data = current_dag.get("dag_data", {})

            merkle_tree = {
                "root": current_dag.get("root_hash", ""),
                "leaves": dag_data.get("leaves", []),
                "tree": dag_data.get("tree", {}),
                "fileMap": dag_data.get("fileMap", {}),
                "timestamp": int(current_dag.get("timestamp", 0))
            }

            logger.info(f"✅ Retrieved Merkle tree for project: {project_id}")
            logger.debug(f"🌳 Root hash: {merkle_tree['root']}")

            return merkle_tree

        except Exception as e:
            logger.error(f"❌ Failed to get Merkle tree for {project_id}: {e}")
            return None
    
    async def process_differential_sync(
        self, 
        project_id: str, 
        changes: Dict, 
        files: List[Dict], 
        merkle_tree: Dict,
        batch_index: int
    ) -> Dict:
        """Process differential sync request"""
        try:
            logger.info(f"🔄 Processing differential sync for project: {project_id}")
            
            added_files = changes.get("added", [])
            modified_files = changes.get("modified", [])
            deleted_files = changes.get("deleted", [])
            unchanged_files = changes.get("unchanged", [])
            
            logger.info(f"📊 Changes summary:")
            logger.info(f"   ➕ Added: {len(added_files)}")
            logger.info(f"   ✏️ Modified: {len(modified_files)}")
            logger.info(f"   🗑️ Deleted: {len(deleted_files)}")
            logger.info(f"   ⚪ Unchanged: {len(unchanged_files)}")
            
            # Track sync session
            session_id = f"{project_id}_{datetime.now().timestamp()}"
            self.sync_sessions[session_id] = {
                "project_id": project_id,
                "start_time": datetime.now().isoformat(),
                "changes": changes,
                "status": "processing"
            }
            
            processed_count = 0
            
            # Process deleted files first
            for deleted_file in deleted_files:
                try:
                    # Use KG's static method to remove file chunks
                    Kg.remove_file_chunks_static(project_id, deleted_file)
                    processed_count += 1
                    logger.debug(f"🗑️ Removed file: {deleted_file}")
                except Exception as e:
                    logger.error(f"❌ Failed to remove file {deleted_file}: {e}")
            
            # Process added and modified files
            logger.info(f"🔍 DEBUG: Processing {len(files)} files for sync")
            for i, file_data in enumerate(files):
                try:
                    logger.info(f"🔍 DEBUG: File {i+1}/{len(files)} structure:")
                    logger.info(f"  Available keys: {list(file_data.keys())}")
                    logger.info(f"  filePath: {file_data.get('filePath', 'NOT_FOUND')}")
                    logger.info(f"  type: {file_data.get('type', 'NOT_FOUND')}")
                    logger.info(f"  chunks count: {len(file_data.get('chunks', []))}")

                    if file_data.get("type") == "file":
                        # Extract chunks content for KG indexing
                        chunks_content = []
                        if file_data.get('chunks'):
                            for chunk in file_data['chunks']:
                                if isinstance(chunk, dict) and 'text' in chunk:
                                    chunks_content.append(chunk['text'])
                                elif isinstance(chunk, str):
                                    chunks_content.append(chunk)

                        # Create simplified structure for KG indexing
                        kg_data = {
                            'filePath': file_data.get('filePath'),
                            'chunks': chunks_content
                        }

                        success = self.kg.index_file_from_client_content(project_id, kg_data)
                        if success:
                            processed_count += 1
                            logger.info(f"✅ Successfully synced file: {file_data.get('filePath')}")
                        else:
                            logger.warning(f"⚠️ Failed to sync file: {file_data.get('filePath')}")
                except Exception as e:
                    logger.error(f"❌ Failed to sync file {file_data.get('filePath', 'unknown')}: {e}")
                    continue
            
            # Update Merkle tree using MerkleIndexer
            await self._store_merkle_tree_with_indexer(project_id, merkle_tree)
            
            # Update session status
            self.sync_sessions[session_id]["status"] = "completed"
            self.sync_sessions[session_id]["end_time"] = datetime.now().isoformat()
            self.sync_sessions[session_id]["processed_count"] = processed_count
            
            logger.info(f"✅ Differential sync completed for project: {project_id}")
            logger.info(f"📊 Processed {processed_count} changes")
            
            return {
                "status": "success",
                "session_id": session_id,
                "processed_count": processed_count,
                "changes_summary": {
                    "added": len(added_files),
                    "modified": len(modified_files),
                    "deleted": len(deleted_files)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Differential sync failed for {project_id}: {e}")
            raise
    

    async def _store_merkle_tree_with_indexer(self, project_id: str, merkle_tree: Dict):
        """Store updated Merkle tree using MerkleStore directly"""
        try:
            logger.info(f"💾 Storing updated Merkle tree for project: {project_id}")

            # Import MerkleStore directly to avoid file system operations in MerkleIndexer
            from utils.merkle.merkle_store import MerkleStore

            # Create MerkleStore instance for this project
            merkle_store = MerkleStore(project_id)

            # Extract root hash and DAG data from the client's merkle tree format
            root_hash = merkle_tree.get("root", "")

            # Convert client format to the format expected by MerkleStore
            dag_data = {
                "root": root_hash,
                "leaves": merkle_tree.get("leaves", []),
                "tree": merkle_tree.get("tree", {}),
                "fileMap": merkle_tree.get("fileMap", {}),
                "timestamp": merkle_tree.get("timestamp", int(time.time() * 1000))
            }

            # Store using MerkleStore directly (this automatically handles timestamping and history)
            merkle_store.save_dag(root_hash, dag_data)

            logger.info(f"✅ Updated Merkle tree stored successfully for project: {project_id}")
            logger.debug(f"🌳 New root hash: {root_hash}")

        except Exception as e:
            logger.error(f"❌ Failed to store updated Merkle tree for {project_id}: {e}")
            raise
    
    def get_sync_session_status(self, session_id: str) -> Optional[Dict]:
        """Get the status of a sync session"""
        return self.sync_sessions.get(session_id)
    
    def get_active_sync_sessions(self) -> Dict:
        """Get all active sync sessions"""
        return self.sync_sessions.copy()
    
    async def get_sync_history(self, project_id: str, limit: int = 10) -> List[Dict]:
        """Get sync history for a project using MerkleStore directly"""
        try:
            logger.info(f"📜 Getting sync history for project: {project_id} (limit: {limit})")

            # Import MerkleStore directly to avoid file system operations in MerkleIndexer
            from utils.merkle.merkle_store import MerkleStore

            # Create MerkleStore instance for this project
            merkle_store = MerkleStore(project_id)

            # Get DAG history from MerkleStore
            dag_history = merkle_store.get_dag_history(limit)

            # Convert to the expected format
            history = []
            for dag_info in dag_history:
                history.append({
                    "timestamp": datetime.fromtimestamp(dag_info["timestamp"]).isoformat(),
                    "root_hash": dag_info["root_hash"],
                    "project_id": project_id,
                    "id": dag_info["id"]
                })

            logger.info(f"✅ Retrieved {len(history)} sync history entries for project: {project_id}")
            return history

        except Exception as e:
            logger.error(f"❌ Failed to get sync history for {project_id}: {e}")
            return []
    
    async def cleanup_old_backups(self, project_id: str, keep_count: int = 50):
        """Clean up old Merkle tree backups using MerkleStore directly"""
        try:
            logger.info(f"🧹 Cleaning up old backups for project: {project_id} (keeping {keep_count})")

            # Import MerkleStore directly to avoid file system operations in MerkleIndexer
            from utils.merkle.merkle_store import MerkleStore

            # Create MerkleStore instance for this project
            merkle_store = MerkleStore(project_id)

            # Use MerkleStore's cleanup functionality
            merkle_store.cleanup_old_dags(max_history_per_project=keep_count)

            logger.info(f"✅ Cleaned up old backups for project: {project_id}")

        except Exception as e:
            logger.error(f"❌ Failed to cleanup backups for {project_id}: {e}")
