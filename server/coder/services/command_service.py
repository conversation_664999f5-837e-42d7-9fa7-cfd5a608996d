"""
Command-based file operation service to replace VFS
Generates file operation commands and handles acknowledgments
"""
import asyncio
import hashlib
import logging
import time
import uuid
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class CommandType(Enum):
    """File operation command types"""
    REPLACE_LINES = "replace_lines"
    INSERT_LINES = "insert_lines"
    DELETE_LINES = "delete_lines"
    CREATE_FILE = "create_file"
    DELETE_FILE = "delete_file"

class CommandStatus(Enum):
    """Command execution status"""
    PENDING = "pending"
    SUCCESS = "success"
    FAILURE = "failure"
    CONFLICT = "conflict"
    TIMEOUT = "timeout"

@dataclass
class FileCommand:
    """File operation command structure"""
    command_id: str
    type: CommandType
    file_path: str
    timestamp: float
    start_line: Optional[int] = None
    end_line: Optional[int] = None
    line_number: Optional[int] = None
    content: Optional[str] = None
    file_version: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        result = asdict(self)
        result['type'] = self.type.value
        return result
    
    def validate(self) -> bool:
        """Validate command structure"""
        if self.type == CommandType.REPLACE_LINES:
            return (self.start_line is not None and 
                   self.end_line is not None and 
                   self.content is not None and
                   self.start_line <= self.end_line)
        elif self.type == CommandType.INSERT_LINES:
            return (self.line_number is not None and 
                   self.content is not None)
        elif self.type == CommandType.DELETE_LINES:
            return (self.start_line is not None and 
                   self.end_line is not None and
                   self.start_line <= self.end_line)
        elif self.type == CommandType.CREATE_FILE:
            return self.content is not None
        elif self.type == CommandType.DELETE_FILE:
            return True
        return False

@dataclass
class CommandAcknowledgment:
    """Command acknowledgment from client"""
    command_id: str
    status: CommandStatus
    message: str
    timestamp: float
    new_file_version: Optional[str] = None
    error_details: Optional[str] = None
    conflict_type: Optional[str] = None
    expected_version: Optional[str] = None
    actual_version: Optional[str] = None

class CommandService:
    """Service for managing file operation commands"""
    
    def __init__(self, timeout_seconds: int = 30):
        self.pending_commands: Dict[str, FileCommand] = {}
        self.command_results: Dict[str, CommandAcknowledgment] = {}
        self.timeout_seconds = timeout_seconds
        self.logger = logger
        
    def generate_command_id(self) -> str:
        """Generate unique command ID"""
        return str(uuid.uuid4())
    
    def calculate_file_version(self, content: str) -> str:
        """Calculate file version using content hash"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()[:16]
    
    def create_replace_lines_command(
        self, 
        file_path: str, 
        start_line: int, 
        end_line: int, 
        content: str,
        file_version: Optional[str] = None
    ) -> FileCommand:
        """Create replace lines command"""
        command = FileCommand(
            command_id=self.generate_command_id(),
            type=CommandType.REPLACE_LINES,
            file_path=file_path,
            start_line=start_line,
            end_line=end_line,
            content=content,
            file_version=file_version,
            timestamp=time.time()
        )
        
        if not command.validate():
            raise ValueError(f"Invalid replace_lines command: {command}")
            
        self.pending_commands[command.command_id] = command
        self.logger.info(f"📝 Created replace_lines command: {file_path}:{start_line}-{end_line}")
        return command
    
    def create_insert_lines_command(
        self, 
        file_path: str, 
        line_number: int, 
        content: str,
        file_version: Optional[str] = None
    ) -> FileCommand:
        """Create insert lines command"""
        command = FileCommand(
            command_id=self.generate_command_id(),
            type=CommandType.INSERT_LINES,
            file_path=file_path,
            line_number=line_number,
            content=content,
            file_version=file_version,
            timestamp=time.time()
        )
        
        if not command.validate():
            raise ValueError(f"Invalid insert_lines command: {command}")
            
        self.pending_commands[command.command_id] = command
        self.logger.info(f"📝 Created insert_lines command: {file_path}:{line_number}")
        return command
    
    def create_delete_lines_command(
        self, 
        file_path: str, 
        start_line: int, 
        end_line: int,
        file_version: Optional[str] = None
    ) -> FileCommand:
        """Create delete lines command"""
        command = FileCommand(
            command_id=self.generate_command_id(),
            type=CommandType.DELETE_LINES,
            file_path=file_path,
            start_line=start_line,
            end_line=end_line,
            file_version=file_version,
            timestamp=time.time()
        )
        
        if not command.validate():
            raise ValueError(f"Invalid delete_lines command: {command}")
            
        self.pending_commands[command.command_id] = command
        self.logger.info(f"📝 Created delete_lines command: {file_path}:{start_line}-{end_line}")
        return command
    
    def create_create_file_command(
        self, 
        file_path: str, 
        content: str
    ) -> FileCommand:
        """Create file creation command"""
        command = FileCommand(
            command_id=self.generate_command_id(),
            type=CommandType.CREATE_FILE,
            file_path=file_path,
            content=content,
            timestamp=time.time()
        )
        
        if not command.validate():
            raise ValueError(f"Invalid create_file command: {command}")
            
        self.pending_commands[command.command_id] = command
        self.logger.info(f"📝 Created create_file command: {file_path}")
        return command
    
    def create_delete_file_command(
        self, 
        file_path: str
    ) -> FileCommand:
        """Create file deletion command"""
        command = FileCommand(
            command_id=self.generate_command_id(),
            type=CommandType.DELETE_FILE,
            file_path=file_path,
            timestamp=time.time()
        )
        
        if not command.validate():
            raise ValueError(f"Invalid delete_file command: {command}")
            
        self.pending_commands[command.command_id] = command
        self.logger.info(f"📝 Created delete_file command: {file_path}")
        return command
    
    def handle_acknowledgment(self, ack_data: Dict[str, Any]) -> bool:
        """Handle command acknowledgment from client"""
        try:
            command_id = ack_data.get("command_id")
            if not command_id or command_id not in self.pending_commands:
                self.logger.warning(f"⚠️ Received ack for unknown command: {command_id}")
                return False
            
            ack = CommandAcknowledgment(
                command_id=command_id,
                status=CommandStatus(ack_data.get("status", "failure")),
                message=ack_data.get("message", ""),
                timestamp=time.time(),
                new_file_version=ack_data.get("new_file_version"),
                error_details=ack_data.get("error_details"),
                conflict_type=ack_data.get("conflict_type"),
                expected_version=ack_data.get("expected_version"),
                actual_version=ack_data.get("actual_version")
            )
            
            # Store result and remove from pending
            self.command_results[command_id] = ack
            del self.pending_commands[command_id]
            
            status_emoji = "✅" if ack.status == CommandStatus.SUCCESS else "❌"
            self.logger.info(f"{status_emoji} Command {command_id} acknowledged: {ack.status.value}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error handling acknowledgment: {e}")
            return False
    
    async def wait_for_acknowledgment(
        self, 
        command_id: str, 
        timeout: Optional[int] = None
    ) -> Optional[CommandAcknowledgment]:
        """Wait for command acknowledgment with timeout"""
        timeout = timeout or self.timeout_seconds
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if command_id in self.command_results:
                return self.command_results[command_id]
            await asyncio.sleep(0.1)
        
        # Timeout occurred
        if command_id in self.pending_commands:
            del self.pending_commands[command_id]
            
        timeout_ack = CommandAcknowledgment(
            command_id=command_id,
            status=CommandStatus.TIMEOUT,
            message=f"Command timed out after {timeout} seconds",
            timestamp=time.time()
        )
        
        self.command_results[command_id] = timeout_ack
        self.logger.warning(f"⏰ Command {command_id} timed out")
        return timeout_ack
    
    def get_pending_commands(self) -> List[FileCommand]:
        """Get all pending commands"""
        return list(self.pending_commands.values())
    
    def get_command_result(self, command_id: str) -> Optional[CommandAcknowledgment]:
        """Get command result by ID"""
        return self.command_results.get(command_id)
    
    def cleanup_old_results(self, max_age_hours: int = 24):
        """Clean up old command results"""
        cutoff_time = time.time() - (max_age_hours * 3600)
        
        to_remove = [
            cmd_id for cmd_id, result in self.command_results.items()
            if result.timestamp < cutoff_time
        ]
        
        for cmd_id in to_remove:
            del self.command_results[cmd_id]
            
        if to_remove:
            self.logger.info(f"🧹 Cleaned up {len(to_remove)} old command results")
