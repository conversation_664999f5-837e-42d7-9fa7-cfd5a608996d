"""
WebSocket-enabled Coder Service that integrates with the multi-agentic flow
"""
import asyncio
import logging
import os
from typing import Dict, List, Any, Optional

from pocketflow import AsyncFlow
from utils.history import History
import traceback


from tools.dir_tool import DirTool
from tools.file_read_tool import File<PERSON><PERSON>Tool

from tools.semantic_search_tool import SemanticSearchTool
from tools.summary_tool import SummaryTool
from tools.code_expert_tool import CodeExpertTool
from tools.read_code_file_struct_tool import ReadCodeFileStructTool
from tools.code_applier_tool import CodeApplierTool
from tools.action_plan_summary_tool import ActionPlanSummaryTool

from agents.execute.coder_agent import CoderAgent




from agents.plan.request_analyzer_agent import ActionableRequestAnalyzer

from agents.plan.execution_plan_agent import ExecutionPlanAgent


from websocket.context import get_ws_context
from websocket.utils import send_ws_message

logger = logging.getLogger(__name__)

class WebSocketCoderService:
    """Coder service that uses WebSocket for real-time communication and command-based file system"""

    def __init__(self, client_id=None, ws_manager=None, command_fs=None):
        # Deprecated: client_id and ws_manager should be fetched from context
        self.command_fs = command_fs
        self.logger = logger
        
    def _is_client_connected(self) -> bool:
        ctx = get_ws_context()
        """Check if client is still connected"""
        return ctx.client_id in ctx.ws_manager.active_connections

    async def orchestrate_coding_agents(self, question, project_name):
        ctx = get_ws_context()
       
       # Step 1: Analyze  user request & create high level tasks
        request_analyzer = ActionableRequestAnalyzer()
        flow = AsyncFlow(start=request_analyzer)
        task_memory = {"user_query": question, "project_name": project_name, "client_id": ctx.client_id, "ws_manager": ctx.ws_manager}
        task_memory["history"] = [
            {
                "tool": "",
                "reason": "",
                "params": "",
                "result": "",
                "data": ""
            }
        ]
        await flow.run_async(shared=task_memory)
        
        
        tasks = task_memory.get('tasks', [])
       
      
        #Step 2: For every highlevel task identified -- Go and Plan action(s)
        shared = History().init("", project_name, ctx.client_id, ctx.ws_manager)
       
        for i, task in enumerate(tasks, 1):
                task_desc = task.get('description', f"Task {i}")
                self.logger.info(f" Task {i} -- {task_desc}")
                
                await send_ws_message(ctx.ws_manager, ctx.client_id, "console_output", {
                        "message": f"\n {i} . {task_desc}",
                        "level": "info",
                        "heading": "h2"
                    })
                
                exec_plan_agent = self.exec_plan_agent_flow()
                flow = AsyncFlow(start=exec_plan_agent)
                task_memory["user_query"] = task_desc
                # Execute the step through the coder agent flow
                await flow.run_async(shared=task_memory)
                
                actions = task_memory.get('actions', [])

                completed_tasks = 0
                
                
                
                
                #Step 3: For each planned action - invoke execute coder
                for j, action in enumerate(actions, 1):
                    self.logger.info(f" Action Plan {j} -- {action}")
                   
                   
                    await self.execute_phase(action, project_name,shared)
                    completed_tasks += 1
                
               
       
    

    async def execute_phase(self, action, project_name,shared):
        ctx = get_ws_context()
        """Execute each step from the execution plan using the coder agent flow"""
        if not action:
            self.logger.info("No Task to execute")
            return
        shared["user_query"] = action
        
        #shared = History().init(task, project_name, ctx.client_id, ctx.ws_manager)
        # Initialize the coder agent flow
        self.logger.info(f"Starting Action .. {action}")
        coder_agent = self.init_flow()
        flow = AsyncFlow(start=coder_agent)
        # Execute the step through the coder agent flow
        await flow.run_async(shared)

     
        self.logger.info(f"✅ Completed Action - {action}")

          

       

    async def process_coder_request(self, project_name: str, question: str) -> Dict[str, Any]:
        ctx = get_ws_context()
        try:
            if not self._is_client_connected():
                self.logger.warning(f"🔌 Client {ctx.client_id} disconnected before processing started")
                return {
                    "status": "error",
                    "message": "Client disconnected",
                    "summary": "Processing stopped due to client disconnection"
                }
            await self.orchestrate_coding_agents(question, project_name)
            

            return {
                "status": "success",
                "message": f"Execution phase completed successfully ",
               
            }
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.logger.error(f"❌ Failed to process coder request: {e}")
            self.logger.error(f"❌ Full traceback: {error_details}")
            # Send error message to client
            await ctx.ws_manager.send_message(ctx.client_id, {
                "type": "error",
                "message": "Internal server error. Please try again later."
            })
            await ctx.ws_manager.broadcast_console_output(ctx.client_id, f"❌ Error processing tasks: {str(e)}", "error")
            return {
                "status": "error",
                "message": f"Failed to process task-based coder request: {str(e)}",
                "project_name": project_name,
                "file_operations": [],
                "summary": "Error occurred during task processing"
            }
    
    
    def exec_plan_agent_flow(self):
        exec_plan_agent = ExecutionPlanAgent()
        dir_tool = DirTool()
        semantic_search_tool = SemanticSearchTool()
        action_plan_summary_tool = ActionPlanSummaryTool()
        
        exec_plan_agent - "dir_tool" >> dir_tool
        dir_tool >> exec_plan_agent
       
        exec_plan_agent - "semantic_search_tool" >> semantic_search_tool
        semantic_search_tool >> exec_plan_agent
       
        exec_plan_agent - "action_plan_summary_tool" >> action_plan_summary_tool
        return exec_plan_agent
    
    
    def init_flow(self):
        """Initialize simple flow like coder.py - no complex connections"""
        coder_agent = CoderAgent()
        dir_tool = DirTool()
        code_file_struc_tool = ReadCodeFileStructTool()
        file_read_tool = FileReadTool()
        code_expert_tool = CodeExpertTool()
        code_applier_tool = CodeApplierTool()
        semantic_search_tool = SemanticSearchTool()
        final_summary_tool = SummaryTool()
        coder_agent - "dir_tool" >> dir_tool
        dir_tool >> coder_agent
        coder_agent - "file_read_tool" >> file_read_tool
        file_read_tool >> coder_agent
        coder_agent - "semantic_search_tool" >> semantic_search_tool
        semantic_search_tool >> coder_agent
        coder_agent - "file_code_struc_tool" >> code_file_struc_tool
        code_file_struc_tool >> coder_agent
        coder_agent - "code_expert_tool" >> code_expert_tool
        code_expert_tool - "code_applier_tool" >> code_applier_tool
        code_applier_tool - "complete" >> final_summary_tool
        code_applier_tool - "coder_agent" >> coder_agent
        coder_agent - "complete" >> final_summary_tool
        return coder_agent

  
