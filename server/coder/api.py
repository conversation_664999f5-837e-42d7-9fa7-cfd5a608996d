"""
FastAPI server for ReCode AI indexing endpoints
"""
import os
import gzip
import base64
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import asyncio
import json

from services.indexing_service import IndexingService
from services.sync_service import SyncService
from utils.kg import Kg

from websocket.manager import websocket_manager, CommandBasedFileSystem
from services.ws_coder import WebSocketCoderService
from services.queue_processor import queue_processor
from utils.payload import decompress_payload
from websocket.handlers import (
    handle_coder_request, handle_file_response, handle_dir_response, handle_file_version_response,
    handle_command_acknowledgment, handle_compilation_feedback, handle_ping, handle_pong,
    handle_lsp_error_feedback, handle_immediate_lsp_feedback
)

from models.dto import (
    ChunkData, FileData, MerkleTree, IndexBatchRequest, DifferentialSyncRequest,
    IndexBatchResponse, LatestMerkleTreeResponse, DifferentialSyncResponse,
    ProjectSummaryRequest, ProjectSummaryResponse, ActiveFileInfo, CoderRequest,
    FileOperation, CoderResponse
)

# Configure logging with emojis
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    # Startup
    queue_processor.start()
    logger.info("🚀 Queue processor started")
    yield
    # Shutdown
    queue_processor.stop()
    logger.info("🛑 Queue processor stopped")

# Initialize FastAPI app
app = FastAPI(
    title="ReCode AI Indexing API",
    description="API for indexing and syncing codebase with ReCode AI",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
indexing_service = IndexingService()
sync_service = SyncService()

# API Endpoints

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "🚀 ReCode AI Indexing API is running!",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "indexing": "ready",
            "sync": "ready"
        }
    }

@app.get("/workspace/status/{workspace_name}")
async def get_workspace_status(workspace_name: str):
    """Check if workspace is indexed by looking at LanceDB tables"""
    try:
        logger.info(f"🔍 Checking indexing status for workspace: {workspace_name}")

        # Use the KG service to check if workspace has indexed data
        kg = Kg()

        # Check if codebase table exists and has data for this workspace
        try:
            # Try to get some data from the codebase table for this project
            table = kg._get_table(workspace_name, "codebase")
            if table is None:
                logger.info(f"📊 No codebase table found - workspace not indexed")
                return {
                    "indexed": False,
                    "workspace_name": workspace_name,
                    "message": "No codebase table found",
                    "file_count": 0,
                    "last_indexed": None
                }

            # Query for any files in this workspace (the table is already project-specific)
            results = table.search().limit(1).to_list()

            if results and len(results) > 0:
                # Get total file count for this workspace
                total_results = table.search().to_list()
                file_count = len(total_results)

                # Try to get last indexed timestamp (if available in metadata)
                last_indexed = None
                if results[0].get('metadata'):
                    last_indexed = results[0]['metadata'].get('indexed_at')

                logger.info(f"✅ Workspace {workspace_name} is indexed with {file_count} files")
                return {
                    "indexed": True,
                    "workspace_name": workspace_name,
                    "message": f"Workspace indexed with {file_count} files",
                    "file_count": file_count,
                    "last_indexed": last_indexed
                }
            else:
                logger.info(f"📊 No data found for workspace: {workspace_name}")
                return {
                    "indexed": False,
                    "workspace_name": workspace_name,
                    "message": "No indexed data found for workspace",
                    "file_count": 0,
                    "last_indexed": None
                }

        except Exception as table_error:
            logger.warning(f"⚠️ Error accessing codebase table: {table_error}")
            return {
                "indexed": False,
                "workspace_name": workspace_name,
                "message": f"Error checking codebase table: {str(table_error)}",
                "file_count": 0,
                "last_indexed": None
            }

    except Exception as e:
        logger.error(f"❌ Error checking workspace status for {workspace_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Error checking workspace status: {str(e)}")



@app.post("/index/batch", response_model=IndexBatchResponse)
async def process_index_batch(request: IndexBatchRequest, background_tasks: BackgroundTasks):
    """Process a batch of files for indexing"""
    try:
        # Decompress payload
        payload = decompress_payload(request.compressed)

        project_id = str(payload.get("project_id", ""))
        files = payload.get("files", [])
        batch_index = payload.get("batch_index", 0)
        merkle_tree = payload.get("merkle_tree") or {}

        logger.info(f"📦 Processing batch {batch_index + 1} for project: {project_id}")
        logger.info(f"📄 Files in batch: {len(files)}")

       
        # Process batch
        await indexing_service.process_batch(
            project_id,
            files,
            batch_index,
            merkle_tree
        )
        
        logger.info(f"✅ Batch {batch_index + 1} processed successfully")
        
        return IndexBatchResponse(
            status="success",
            message=f"Batch {batch_index + 1} processed successfully",
            batch_index=batch_index,
            processed_files=len(files)
        )
        
    except Exception as e:
        logger.error(f"❌ Batch processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Batch processing failed: {e}")

@app.get("/index/latest_merkle_tree", response_model=LatestMerkleTreeResponse)
async def get_latest_merkle_tree(project_id: str):
    """Get the latest Merkle tree for a project"""
    try:
        logger.info(f"🌳 Fetching latest Merkle tree for project: {project_id}")
        
        merkle_tree = await sync_service.get_latest_merkle_tree(project_id)
        
        if merkle_tree:
            #logger.info(f"✅ Found Merkle tree for project: {project_id}")
            return LatestMerkleTreeResponse(
                status="success",
                merkle_tree=merkle_tree,
                message="Merkle tree found"
            )
        else:
            logger.info(f"ℹ️ No Merkle tree found for project: {project_id}")
            return LatestMerkleTreeResponse(
                status="success",
                merkle_tree=None,
                message="No previous Merkle tree found"
            )
            
    except Exception as e:
        logger.error(f"❌ Failed to fetch Merkle tree: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch Merkle tree: {e}")

@app.post("/sync/differential", response_model=DifferentialSyncResponse)
async def process_differential_sync(request: DifferentialSyncRequest, background_tasks: BackgroundTasks):
    """Process differential sync request"""
    try:
        # Decompress payload
        payload = decompress_payload(request.compressed)
        
        project_id = str(payload.get("project_id", ""))
        changes = payload.get("changes", {})
        files = payload.get("files", [])
        merkle_tree = payload.get("merkle_tree") or {}
        batch_index = payload.get("batch_index", 0)
        
        logger.info(f"🔄 Processing differential sync for project: {project_id}")
      
        
        # Process differential sync
        result = await sync_service.process_differential_sync(
            project_id,
            changes,
            files,
            merkle_tree,
            batch_index
        )
        
        logger.info(f"✅ Differential sync completed for project: {project_id}")
        
        return DifferentialSyncResponse(
            status="success",
            message="Differential sync completed successfully",
            processed_changes={
                "added": len(changes.get('added', [])),
                "modified": len(changes.get('modified', [])),
                "deleted": len(changes.get('deleted', []))
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Differential sync failed: {e}")
        raise HTTPException(status_code=500, detail=f"Differential sync failed: {e}")

@app.post("/project/summary", response_model=ProjectSummaryResponse)
async def generate_project_summary(request: ProjectSummaryRequest):
    """Generate project summary using knowledge graph"""
    try:
        logger.info(f"🤖 Generating summary for project: {request.project_name}")
        logger.info(f"❓ Question: {request.question}")

        # Initialize knowledge graph
        kg = Kg()

        # Ask the question using the knowledge graph
        result = kg.ask(request.project_name, request.question)

        logger.info(f"✅ Summary generated successfully for project: {request.project_name}")

        return ProjectSummaryResponse(
            status="success",
            summary=result,
            project_name=request.project_name
        )

    except Exception as e:
        logger.error(f"❌ Failed to generate project summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate project summary: {e}")
    
@app.post("/project/search", response_model=[])
async def generate_project_search(request: ProjectSummaryRequest):
    """Generate project summary using knowledge graph"""
    try:
        logger.info(f"🤖 Generating summary for project: {request.project_name}")
        logger.info(f"❓ Question: {request.question}")

        # Initialize knowledge graph
        kg = Kg()

        # Ask the question using the enhanced knowledge graph search with line numbers
        enhanced_result = kg.search_codebase_with_lines(request.project_name, request.question)

        # Format results with line information if available
        if enhanced_result and any(chunk.get("line_start") for chunk in enhanced_result):
            formatted_results = []
            for chunk in enhanced_result:
                if chunk.get("line_start") and chunk.get("line_end"):
                    formatted_chunk = {
                        "content": chunk['content'],
                        "file": chunk['file'],
                        "line_start": chunk['line_start'],
                        "line_end": chunk['line_end'],
                        "language": chunk.get('language', 'text'),
                        "chunk_id": chunk.get('chunk_id', '')
                    }
                else:
                    formatted_chunk = {
                        "content": chunk['content'],
                        "file": chunk['file']
                    }
                formatted_results.append(formatted_chunk)
            result = formatted_results
        else:
            # Fallback to basic search if enhanced search fails
            result = kg.search_codebase(request.project_name, request.question)

        logger.info(f"✅ Summary generated successfully for project: {request.project_name}")

        return result

    except Exception as e:
        logger.error(f"❌ Failed to generate project summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate project summary: {e}")



@app.websocket("/ws/coder/{client_id}")
async def websocket_coder_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time coder agent communication"""
    await websocket_manager.connect(websocket, client_id)
    
    try:
        logger.info(f"🔌 Coder WebSocket session started for client: {client_id}")
        
        while True:
            try:
                # Receive message from client with timeout
                data = await asyncio.wait_for(websocket.receive_text(), timeout=300.0)  # 5 min timeout
                
                try:
                    message = json.loads(data)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON from {client_id}: {e}")
                    await websocket_manager.send_message(client_id, {
                        "type": "error",
                        "data": {"message": "Invalid JSON format"}
                    })
                    continue
                
                # Directly handle message types
                message_type = message.get("type")
                message_data = message.get("data", {})
                
                print(f"Acknowledging Receipt of Request -- {message_type} AND  {message_data}")

                if message_type == "coder_request":
                    await handle_coder_request(client_id, message_data)
                elif message_type == "file_response":
                    await handle_file_response(client_id, message_data)
                elif message_type == "dir_response":
                    await handle_dir_response(client_id, message_data)
                elif message_type == "file_version_response":
                    await handle_file_version_response(client_id, message_data)
                elif message_type == "command_ack":
                    await handle_command_acknowledgment(client_id, message_data)
                elif message_type == "compilation_feedback":
                    await handle_compilation_feedback(client_id, message_data)
                elif message_type == "ping":
                    await handle_ping(client_id, message_data)
                elif message_type == "pong":
                    await handle_pong(client_id, message_data)
                elif message_type == "lsp_error_feedback":
                    await handle_lsp_error_feedback(client_id, message_data)
                elif message_type == "immediate_lsp_feedback":
                    await handle_immediate_lsp_feedback(client_id, message_data)
                else:
                    logger.warning(f"⚠️ Unknown message type from {client_id}: {message_type}")
                    await websocket_manager.send_message(client_id, {
                        "type": "error",
                        "data": {"message": f"Unknown message type: {message_type}"}
                    })
            
            except asyncio.TimeoutError:
                # Send ping to keep connection alive
                await websocket_manager.send_message(client_id, {"type": "ping"})
                
            except WebSocketDisconnect:
                logger.info(f"🔌 Client {client_id} disconnected normally")
                break
                
            except Exception as e:
                logger.error(f"❌ Error processing message from {client_id}: {e}")
                # Check if client is still connected before sending error
                if client_id in websocket_manager.active_connections:
                    try:
                        await websocket_manager.send_message(client_id, {
                            "type": "error",
                            "data": {"message": f"Error processing message: {str(e)}"}
                        })
                    except Exception as send_error:
                        logger.error(f"❌ Failed to send error message to {client_id}: {send_error}")
                        break  # Exit the loop if we can't send messages
                
    except Exception as e:
        logger.error(f"❌ WebSocket error for client {client_id}: {e}")
    finally:
        websocket_manager.disconnect(client_id)
