<html>
<style>
    .wrapper {
        display:flex;
        flex-direction: row;
    }
    .first {
        width: 500px;
        margin-right: 100px;
    }
    .second {
        width: 500px;
    }

    .styled-table {
        border-collapse: collapse;
        margin: 25px 0;
        font-size: 0.9em;
        font-family: sans-serif;
        min-width: 400px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    }

    .styled-table thead tr {
        background-color: #009879;
        color: #ffffff;
        text-align: left;
    }

    .align-right {
        text-align: right;
    }
    .styled-table th,
    .styled-table td {
        padding: 12px 15px;
    }
    .styled-table tbody tr {
        border-bottom: 1px solid #dddddd;
    }

    .styled-table tbody tr:nth-of-type(even) {
        background-color: #f3f3f3;
    }

    .styled-table tbody tr:last-of-type {
        border-bottom: 2px solid #009879;
    }
    .styled-table tbody tr.active-row {
        font-weight: bold;
        color: #009879;
    }
</style>
<body>
<h2 id="title">Title</h2>
<table id="stats-table" class="styled-table">
    <thead>
    <tr>
        <td>Parameter</td>
        <td>Value</td>
        <td>Description</td>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>Nodes</td>
        <td id="nodes" class="align-right"></td>
        <td>Number of nodes in the graph (such as classes, methods, variables, etc.).</td>
    </tr>
    <tr>
        <td>Edges</td>
        <td id="edges" class="align-right"></td>
        <td>Number of edges in the graph. See the chart below for distribution.</td>
    </tr>
    <tr>
        <td>Total LOC</td>
        <td id="totalLoc" class="align-right"></td>
        <td>Total number of lines of code.</td>
    </tr>
    <tr>
        <td>Density</td>
        <td id="density" class="align-right"></td>
        <td>Density of the graph -- a value between 0 (low density) and 1 (high density). High SCG density can indicate higher code dependency complexity.</td>
    </tr>
    <tr>
        <td>Average Degree</td>
        <td id="averageDegree" class="align-right"></td>
        <td>Average number of edges. High average degree of nodes, especially when combined with high density, suggests high project coupling.</td>
    </tr>

    <tr>
        <td>Global Clustering Coefficient</td>
        <td id="globalClusteringCoefficient" class="align-right"></td>
        <td>High value indicates that the graph tends to create interconnected clusters, low value indicates the network being sparse or fragmented. For SCG, high GCC value can indicate well-modularized software with high module cohesion and low coupling.</td>
    </tr>
    <tr>
        <td>Degree Assortativity Coefficient</td>
        <td id="assortativityCoefficient" class="align-right"></td>
        <td>Positive value indicates that nodes with the same degree tends to be connected, negative value suggests mostly connections between nodes of different degree, value close to zero suggests random network. For SCG, low DAC can suggest tendency of the graph towards clustering and establishing hubs (connections between nodes with different degrees).</td>
    </tr>
    </tbody>
</table>

<div class="wrapper">
    <div class="first">
        <h2>Node distribution</h2>
        <canvas id="nodesDistribution"></canvas>
    </div>
    <div class="second">
        <h2>Edge distribution</h2>
        <canvas id="edgesDistribution"></canvas>
    </div>
</div>



<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="summary.js"></script>
<script>
    const nodesDistributionContext = document.getElementById('nodesDistribution');

    new Chart(nodesDistributionContext, {
        type: 'pie',
        data: {
            labels: summary.nodesDistribution.map(n => n.kind),
            datasets: [{
                label: 'Nodes distribution',
                data: summary.nodesDistribution.map(n => n.number),
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    const edgesDistributionContext = document.getElementById('edgesDistribution');

    new Chart(edgesDistributionContext, {
        type: 'pie',
        data: {
            labels: summary.edgesDistribution.map(n => n.type),
            datasets: [{
                label: 'Edges distribution',
                data: summary.edgesDistribution.map(n => n.number),
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    document.getElementById("title").innerText = summary.name + " project summary";
    document.getElementById("nodes").innerText = summary.nodes;
    document.getElementById("edges").innerText = summary.edges;
    document.getElementById("totalLoc").innerText = summary.totalLoc;
    document.getElementById("averageDegree").innerText = summary.averageDegree.toFixed(3);
    document.getElementById("density").innerText = summary.density.toFixed(8);
    document.getElementById("globalClusteringCoefficient").innerText = summary.globalClusteringCoefficient.toFixed(8);
    document.getElementById("assortativityCoefficient").innerText = summary.assortativityCoefficient.toFixed(8);
</script>
</body>
</html>
