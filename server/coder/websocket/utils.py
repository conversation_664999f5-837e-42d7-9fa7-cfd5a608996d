async def send_agent_status(ws_manager, client_id, status, details, animation=None):
    data = {
        "status": status,
        "details": details
    }
    if animation:
        data["animation"] = animation
    await ws_manager.send_message(client_id, {
        "type": "agent_status",
        "data": data
    })

async def send_console_output(ws_manager, client_id, message, level="info"):
    await ws_manager.send_message(client_id, {
        "type": "console_output",
        "data": {
            "message": message,
            "level": level
        }
    })

async def send_ws_message(ws_manager, client_id, message_type, data):
    await ws_manager.send_message(client_id, {
        "type": message_type,
        "data": data
    })

import asyncio
import json
import os
import traceback

def parse_file_paths(files_input):
    """Parse file paths from string or other input - use paths as-is to avoid mismatches"""
    if isinstance(files_input, str):
        paths = [path.strip() for path in files_input.split(",")]
    else:
        paths = [str(files_input)]
    print(f"📝 Using file paths as-is: {paths}")
    return paths

async def request_directory_from_client(ws_manager, client_id, dir_path):


    await ws_manager.send_message(client_id, {
        "type": "dir_request",
        "data": {"path": dir_path}
    })
    
    try:
        # Use the same pattern as file requests - simple store lookup
        response = await _wait_for_dir_response_simple(ws_manager, client_id, dir_path)

        if response and response.get("files") is not None:
            files = response["files"]
            print(f"\u2705 Directory loaded: {dir_path} ({len(files)} items)")
            return files
        else:
            print(f"\u274c Failed to read directory: {dir_path}")
            return []
    except Exception as e:
        print(f"\u274c Error reading directory {dir_path}: {e}")
        traceback.print_exc()
        return []

async def _wait_for_dir_response_simple(ws_manager, client_id, dir_path, timeout=30):
    """Simple store-based directory response waiting - one response per client with auditing"""
    try:
        start_time = asyncio.get_event_loop().time()

        while (asyncio.get_event_loop().time() - start_time) < timeout:
            # Check if client is still connected
            if client_id not in ws_manager.active_connections:
                print(f"\u274c Client {client_id} disconnected while waiting for directory response")
                return None

            # Check if there's a response for this client
            if hasattr(ws_manager, 'dir_response_store'):
                stored_data = ws_manager.dir_response_store.get(client_id)
                if stored_data and stored_data.get("response", {}).get("path") == dir_path:
                    # Extract the actual response
                    response = stored_data["response"]
                    tool_name = stored_data.get("tool_name", "unknown")
                    timestamp = stored_data.get("timestamp", "unknown")

                    # Clear the response from store
                    del ws_manager.dir_response_store[client_id]
                    print(f"\u2705 Found directory response for client {client_id} (tool: {tool_name}, stored: {timestamp})")
                    return response

            # Wait a short time before checking again
            await asyncio.sleep(0.5)

        print(f"\u23f0 Timeout waiting for directory response: {dir_path}")
        return None

    except Exception as e:
        print(f"\u274c Error waiting for directory response: {e}")
        return None

async def request_files_from_client(ws_manager, client_id, file_paths):
    """Request complete file content from client"""
    files_content = {}
    for file_path in file_paths:
        print(f"\U0001F4E4 Requesting file from client: {file_path}")
        await ws_manager.send_message(client_id, {
            "type": "file_request",
            "data": {"path": file_path}
        })
        try:
            response = await _wait_for_file_response_simple(ws_manager, client_id, file_path)
            if response:
                if response.get("error"):
                    files_content[file_path] = f"Error: {response['error']}"
                    print(f"\u274c Client error for {file_path}: {response['error']}")
                elif response.get("content") is not None:
                    content = response["content"]
                  

                    if content:
                        lines = content.splitlines()
                        numbered_lines = [f"{i+1}: {line}" for i, line in enumerate(lines)]
                        numbered_content = '\n'.join(numbered_lines)

                        # Only include content (no structure_map)
                        files_content[file_path] = {
                            "content": numbered_content
                        }
                        print(f"\u2705 File loaded: {file_path} ({len(lines)} lines )")

                   
                    else:
                        files_content[file_path] = f"Error: File {file_path} is empty or could not be read"
                else:
                    files_content[file_path] = f"Error: No content received for {file_path}"
                    print(f"\u274c No content received for {file_path}")
            else:
                files_content[file_path] = f"Error: No response from client for {file_path}"
                print(f"\u274c No response from client for {file_path}")
        except Exception as e:
            files_content[file_path] = f"Error: {str(e)}"
            print(f"\u274c Error requesting {file_path}: {e}")
    return files_content

async def request_file_range_from_client(ws_manager, client_id, file_paths, line_from, line_to):
    """Request specific line range from client files"""
    files_content = {}
    for file_path in file_paths:
        print(f"\U0001F4E4 Requesting file range from client: {file_path} (lines {line_from}-{line_to})")
        await ws_manager.send_message(client_id, {
            "type": "file_range_request",
            "data": {
                "path": file_path,
                "line_from": line_from,
                "line_to": line_to
            }
        })
        try:
            response = await _wait_for_file_response_simple(ws_manager, client_id, file_path)
            if response:
                if response.get("error"):
                    files_content[file_path] = f"Error: {response['error']}"
                    print(f"\u274c Client error for {file_path} range {line_from}-{line_to}: {response['error']}")
                elif response.get("content") is not None:
                    content = response["content"]

                    if content:
                        # Content should already be formatted with line numbers by client
                        lines_count = len(content.splitlines())

                        # Only include content (no structure_map)
                        files_content[file_path] = {
                            "content": content
                        }
                        print(f"\u2705 File range loaded: {file_path} lines {line_from}-{line_to} ({lines_count} lines)")
                    else:
                        files_content[file_path] = f"Error: File range {file_path} lines {line_from}-{line_to} is empty or could not be read"
                else:
                    files_content[file_path] = f"Error: No content received for {file_path} range {line_from}-{line_to}"
                    print(f"\u274c No content received for {file_path} range {line_from}-{line_to}")
            else:
                files_content[file_path] = f"Error: No response from client for {file_path} range {line_from}-{line_to}"
                print(f"\u274c No response from client for {file_path} range {line_from}-{line_to}")
        except Exception as e:
            files_content[file_path] = f"Error: {str(e)}"
            print(f"\u274c Error requesting {file_path} range {line_from}-{line_to}: {e}")
    return files_content

async def _wait_for_file_response_simple(ws_manager, client_id, file_path, timeout=10):
    """Simple store-based file response waiting - one response per client with auditing"""
    try:
        start_time = asyncio.get_event_loop().time()

        while (asyncio.get_event_loop().time() - start_time) < timeout:
            # Check if client is still connected
            if client_id not in ws_manager.active_connections:
                print(f"\u274c Client {client_id} disconnected while waiting for file response")
                return None

            # Check if there's a response for this client
            if hasattr(ws_manager, 'file_response_store'):
                stored_data = ws_manager.file_response_store.get(client_id)
                if stored_data and stored_data.get("response", {}).get("path") == file_path:
                    # Extract the actual response
                    response = stored_data["response"]
                    tool_name = stored_data.get("tool_name", "unknown")
                    timestamp = stored_data.get("timestamp", "unknown")

                    # Clear the response from store
                    del ws_manager.file_response_store[client_id]
                    print(f"\u2705 Found file response for client {client_id} (tool: {tool_name}, stored: {timestamp})")
                    return response

            # Wait a short time before checking again
            await asyncio.sleep(0.5)

        print(f"\u23f0 Timeout waiting for file response: {file_path}")
        return None

    except Exception as e:
        print(f"\u274c Error waiting for file response: {e}")
        return None

async def _wait_for_file_response_old(ws_manager, client_id, file_path, timeout=10):
    websocket = ws_manager.active_connections.get(client_id)
    if not websocket:
        print("\u274c No WebSocket connection found for client")
        return None
    try:
        start_time = asyncio.get_event_loop().time()
        while (asyncio.get_event_loop().time() - start_time) < timeout:
            try:
                response = await asyncio.wait_for(websocket.receive_text(), timeout=2.0)
                data = json.loads(response)
                if data.get("type") == "file_response":
                    response_path = data.get("data", {}).get("path")
                    if response_path == file_path:
                        return data.get("data", {})
                    else:
                        print(f"\u26a0\ufe0f Received response for different file: {response_path} (expected: {file_path})")
                        continue
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"\u274c Error receiving file response: {e}")
                break
        print(f"\u23f0 Timeout waiting for file response: {file_path}")
        return None
    except Exception as e:
        print(f"\u274c Error waiting for file response: {e}")
        return None

async def send_code_apply_request(ws_manager, client_id, operations):
    await ws_manager.send_message(client_id, {
        "type": "code_apply_request",
        "data": {
            "operations": operations
        }
    })
    print("\U0001F4E4 Code application request sent to client. Waiting for user approval and application...")

async def wait_for_code_apply_response(ws_manager, client_id, timeout=None):
    if timeout is None:
        timeout = int(os.getenv("RECODE_USER_RESPONSE_TIMEOUT", "10"))
    print(f"\u23f0 Waiting for user response (timeout: {timeout}s)...")
    websocket = ws_manager.active_connections.get(client_id)
    if not websocket:
        print("\u274c No WebSocket connection found for client")
        return None
    if hasattr(websocket, 'client_state') and websocket.client_state.name != "CONNECTED":
        print(f"\u274c WebSocket is not connected (state: {websocket.client_state.name})")
        return None
    try:
        start_time = asyncio.get_event_loop().time()
        while (asyncio.get_event_loop().time() - start_time) < timeout:
            elapsed_time = asyncio.get_event_loop().time() - start_time
            if elapsed_time >= timeout:
                print(f"\u23f0 User response timeout after {elapsed_time:.1f}s - treating as 'cancelled' (NO)")
                return {
                    "status": "cancelled",
                    "message": f"User did not respond within {timeout} seconds - treated as rejection",
                    "applied_count": 0,
                    "timeout": True
                }
            try:
                if websocket.client_state.name != "CONNECTED":
                    print(f"\u274c WebSocket is no longer connected (state: {websocket.client_state.name}), breaking out of loop")
                    break
                response = await asyncio.wait_for(websocket.receive_text(), timeout=5.0)
                data = json.loads(response)
                message_type = data.get("type")
                if message_type == "code_apply_response":
                    code_apply_response = data.get("data", {})
                    status = code_apply_response.get('status', 'unknown')
                    print(f"\u2705 Received code application response: {status}")
                    # Return immediately after receiving code application response
                    return code_apply_response
                else:
                    # Ignore other message types (including compilation_feedback)
                    print(f"\u2139\ufe0f Ignoring message type: {message_type}")
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                error_msg = str(e)
                print(f"\u274c Error receiving message: {error_msg}")
                if ("disconnect" in error_msg.lower() or
                    "connection" in error_msg.lower() or
                    "closed" in error_msg.lower()):
                    print("\U0001F50C WebSocket connection lost, breaking out of loop")
                    break
                await asyncio.sleep(0.1)
                continue
        elapsed_time = asyncio.get_event_loop().time() - start_time
        if elapsed_time >= timeout:
            print(f"\u23f0 User response timeout after {elapsed_time:.1f}s - treating as 'cancelled' (NO)")
            return {
                "status": "cancelled",
                "message": f"User did not respond within {timeout} seconds - treated as rejection",
                "applied_count": 0
            }
        else:
            print("\u274c No code application response received - connection may have been lost")
            return {
                "status": "error",
                "message": "Connection lost or unexpected error",
                "applied_count": 0
            }
    except Exception as e:
        print(f"\u274c Error waiting for code application response: {e}")
        return None 