import contextvars

class WebSocketContext:
    def __init__(self, client_id=None, ws_manager=None):
        self.client_id = client_id
        self.ws_manager = ws_manager

_ws_context_var = contextvars.ContextVar('ws_context', default=WebSocketContext())

def set_ws_context(client_id, ws_manager):
    ctx = WebSocketContext(client_id, ws_manager)
    _ws_context_var.set(ctx)
    return ctx

def get_ws_context():
    return _ws_context_var.get() 