import asyncio
import logging
from typing import Dict, Any
from websocket.manager import websocket_manager
from services.ws_coder import WebSocketCoderService
from websocket.context import set_ws_context
from utils.command_logger import command_logger

logger = logging.getLogger(__name__)

async def handle_coder_request(client_id: str, request_data: Dict[str, Any]):
    """Handle coder agent request via WebSocket with queue support"""
    # Check if request should be queued
    queue_position = websocket_manager.add_to_queue(client_id, request_data)

    if queue_position > 0:
        # Request was queued
        await websocket_manager.send_message(client_id, {
            "type": "queue_status",
            "data": {
                "status": "queued",
                "position": queue_position,
                "message": f"Your request has been added to queue (position: {queue_position})"
            }
        })
        return

    # Process immediately (queue_position == 0) - but don't block the main WebSocket loop
    asyncio.create_task(_process_coder_request(client_id, request_data))

async def _process_coder_request(client_id: str, request_data: Dict[str, Any]):
    """Internal method to process coder request"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler
    try:
        # Validate required fields
        project_name = request_data.get("project_name", "").strip()
        question = request_data.get("question", "").strip()

        if not project_name or not question:
            await websocket_manager.send_message(client_id, {
                "type": "coder_response",
                "data": {
                    "status": "error",
                    "message": "Missing project_name or question"
                }
            })
            return

        # FIRST STEP: Log user query to history/commands.txt
        await command_logger.log_user_query(client_id, project_name, question)

        logger.info(f"🤖 Processing coder request for project: {project_name} question - {question }")
        await websocket_manager.broadcast_console_output(
            client_id, f"✨ Analyzing your request...", "info"
        )

      
        # Create WebSocket coder service (can be refactored to not require client_id/ws_manager)
        websocket_coder_service = WebSocketCoderService(client_id, websocket_manager, None) # command_fs is removed

        # Process the coder request with active file context
        result = await websocket_coder_service.process_coder_request(project_name, question)
        
        # Send final response
        await websocket_manager.send_message(client_id, {
            "type": "coder_response",
            "data": {
                "status": result["status"],
                "message": result["message"],
                "summary": result["summary"],
                "file_operations": result.get("file_operations", [])
            }
        })

        # await websocket_manager.broadcast_console_output(
        #     client_id, "✅ Coder processing completed!", "success"
        # )

    except Exception as e:
        logger.error(f"❌ Error processing coder request for {client_id}: {e}")
        # Send error response
        await websocket_manager.send_message(client_id, {
            "type": "coder_response",
            "data": {
                "status": "error",
                "message": f"Error processing request: {str(e)}"
            }
        })
    finally:
        # Mark request as completed to allow processing next request
        websocket_manager.mark_request_completed(client_id)
        

async def handle_file_response(client_id: str, message_data: Dict[str, Any]):
    """Handle file response from client"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler
    file_path = message_data.get("path", "unknown")
    success = message_data.get("success", False)

    # Store the response in the shared store for tools to pick up with auditing info
    import time
    websocket_manager.file_response_store[client_id] = {
        "response": message_data,
        "tool_name": "file_read_tool",
        "client_id": client_id,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "path": file_path
    }

    if success:
        content_length = len(message_data.get("content", ""))
        logger.info(f"📁 [file_read_tool] Received file response from {client_id}: {file_path} ({content_length} chars)")
    else:
        error = message_data.get("error", "Unknown error")
        logger.warning(f"📁 [file_read_tool] File response error from {client_id} for {file_path}: {error}")


async def handle_dir_response(client_id: str, message_data: Dict[str, Any]):
    """Handle directory response from client"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler
    dir_path = message_data.get("path", "unknown")
    success = message_data.get("success", False)

    # Store the response in the shared store for tools to pick up with auditing info
    import time
    websocket_manager.dir_response_store[client_id] = {
        "response": message_data,
        "tool_name": "dir_tool",
        "client_id": client_id,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "path": dir_path
    }

    if success:
        files_count = len(message_data.get("files", []))
        logger.info(f"📂 [dir_tool] Received directory response from {client_id}: {dir_path} ({files_count} items)")
    else:
        error = message_data.get("error", "Unknown error")
        logger.warning(f"📂 [dir_tool] Directory response error from {client_id} for {dir_path}: {error}")


async def handle_file_version_response(client_id: str, message_data: Dict[str, Any]):
    """Handle file version response from client"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler
    file_path = message_data.get("path", "unknown")
    version = message_data.get("version", "unknown")
    logger.info(f"📋 Received file version response: {file_path} -> {version}")


async def handle_command_acknowledgment(client_id: str, message_data: Dict[str, Any]):
    """Handle command acknowledgment from client"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler
    command_id = message_data.get("command_id", "unknown")
    status = message_data.get("status", "unknown")

    success = await websocket_manager.handle_command_acknowledgment(client_id, message_data)

    if success:
        logger.info(f"📨 Command acknowledged: {command_id} -> {status}")
    else:
        logger.warning(f"📨 Failed to process command ack: {command_id}")


async def handle_compilation_feedback(client_id: str, message_data: Dict[str, Any]):
    """Handle compilation feedback from client"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler
    language = message_data.get("language", "unknown")
    success = message_data.get("success", False)
    errors = message_data.get("errors", [])
    warnings = message_data.get("warnings", [])
    changed_files = message_data.get("changed_files", [])

    logger.info(f"🔍 Received compilation feedback from {client_id}:")
    logger.info(f"   Language: {language}")
    logger.info(f"   Success: {success}")
    logger.info(f"   Errors: {len(errors)}")
    logger.info(f"   Warnings: {len(warnings)}")
    logger.info(f"   Changed files: {len(changed_files)}")

    # Log detailed error information for debugging
    if errors:
        logger.info("❌ Compilation errors:")
        for i, error in enumerate(errors[:3], 1):  # Log first 3 errors
            logger.info(f"   {i}. {error.get('file', 'unknown')}:{error.get('line', 0)} - {error.get('message', 'Unknown error')}")
        if len(errors) > 3:
            logger.info(f"   ... and {len(errors) - 3} more errors")

    if warnings:
        logger.info("⚠️ Compilation warnings:")
        for i, warning in enumerate(warnings[:2], 1):  # Log first 2 warnings
            logger.info(f"   {i}. {warning.get('file', 'unknown')}:{warning.get('line', 0)} - {warning.get('message', 'Unknown warning')}")
        if len(warnings) > 2:
            logger.info(f"   ... and {len(warnings) - 2} more warnings")

    # The compilation feedback will be processed by the WebSocketCompilationFeedbackTool
    # This handler just logs the feedback for monitoring purposes


async def handle_ping(client_id: str, message_data: Dict[str, Any]):
    """Handle ping from client"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler
    await websocket_manager.send_message(client_id, {"type": "pong"})


async def handle_pong(client_id: str, message_data: Dict[str, Any]):
    """Handle pong from client"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler
    logger.debug(f"🏓 Received pong from {client_id}")

async def handle_lsp_error_feedback(client_id: str, request_data: Dict[str, Any]):
    """Handle LSP error feedback from client - queue individual requests per file"""
    try:
        project_name = request_data.get("project_name", "").strip()
        question = request_data.get("question", "").strip()
        file_path = request_data.get("file_path", "").strip()
        errors = request_data.get("errors", [])

        if not project_name or not question or not file_path:
            logger.warning(f"❌ Invalid LSP error feedback from {client_id}: missing required fields")
            return

        logger.info(f"🔍 Received LSP error feedback from {client_id}:")
        logger.info(f"   File: {file_path}")
        logger.info(f"   Errors: {len(errors)}")

        # Create enhanced question with error details
        error_details = []
        for error in errors:
            error_details.append(f"Line {error.get('line', '?')}: {error.get('message', 'Unknown error')}")

        enhanced_question = f"{question}\n\nError details:\n" + "\n".join(error_details)

        # Create request data for queuing
        lsp_request_data = {
            "project_name": project_name,
            "question": enhanced_question
        }

        # Add to queue (this will either process immediately or queue it)
        queue_position = websocket_manager.add_to_queue(client_id, lsp_request_data)

        if queue_position > 0:
            # Request was queued
            await websocket_manager.send_message(client_id, {
                "type": "queue_status",
                "data": {
                    "status": "queued",
                    "position": queue_position,
                    "message": f"LSP error fix request for {file_path} added to queue (position: {queue_position})"
                }
            })
            logger.info(f"📋 LSP error feedback queued for {client_id} - file: {file_path}, position: {queue_position}")
        else:
            # Process immediately
            logger.info(f"🔄 Processing LSP error feedback immediately for {client_id} - file: {file_path}")
            await _process_coder_request(client_id, lsp_request_data)

    except Exception as e:
        logger.error(f"❌ Error handling LSP error feedback from {client_id}: {e}")

async def handle_immediate_lsp_feedback(client_id: str, message_data: Dict[str, Any]):
    """Handle immediate LSP feedback from client and store in shared state"""
    set_ws_context(client_id, websocket_manager)  # Set context for this handler

    cleaned_feedback = message_data.get("cleaned_feedback", "No feedback")
    logger.info(f"⚡ Received immediate LSP feedback from {client_id}: {cleaned_feedback}")

    # Store in immediate feedback store for code_applier_tool to pick up
    feedback_key = f"immediate_feedback_{client_id}"
    websocket_manager.immediate_feedback_store[feedback_key] = cleaned_feedback

    logger.info(f"⚡ Stored immediate LSP feedback for {client_id}")

    # Send acknowledgment back to client
    await websocket_manager.send_message(client_id, {
        "type": "immediate_feedback_ack",
        "data": {"status": "received", "message": "Immediate LSP feedback received"}
    })