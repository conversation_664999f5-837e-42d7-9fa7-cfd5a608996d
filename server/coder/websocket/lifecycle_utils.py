from websocket.utils import send_ws_message
import asyncio
import re

def camel_to_snake(name):
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

async def send_tool_status(ws_manager, client_id, tool_name, status, message="", file_path=None, file_name=None):
    if not client_id or not ws_manager:
        print(f"❌ MISSING WebSocket context for tool status: client_id={client_id}, ws_manager={ws_manager}")
        return
    # Convert class name to snake_case if needed
    if not isinstance(tool_name, str):
        tool_name = tool_name.__class__.__name__
    tool_name = camel_to_snake(tool_name)
    payload = {
        "tool_name": tool_name,
        "status": status,
        "message": message,
        "timestamp": asyncio.get_event_loop().time()
    }
    if file_path:
        payload["file_path"] = file_path
    if file_name:
        payload["file_name"] = file_name
    await send_ws_message(ws_manager, client_id, "tool_status", payload) 