"""
WebSocket service for real-time coder agent communication with command-based file operations
"""
import asyncio
import json
import logging
import queue
import threading
import time
from typing import Dict, Any, Optional
from fastapi import WebSocket

from services.command_service import CommandService, FileCommand

logger = logging.getLogger(__name__)

class RequestQueue:
    """Simple in-memory queue for handling client requests"""

    def __init__(self):
        self.queue = queue.Queue()
        self.processing_status: Dict[str, bool] = {}  # client_id -> is_processing
        self.lock = threading.Lock()

    def add_request(self, client_id: str, request_data: Dict[str, Any]) -> int:
        """Add request to queue and return queue position"""
        with self.lock:
            # Check if client is already processing
            if self.processing_status.get(client_id, False):
                # Add to queue
                self.queue.put({
                    'client_id': client_id,
                    'request_data': request_data,
                    'timestamp': time.time()
                })
                return self.queue.qsize()
            else:
                # Mark as processing and return 0 (immediate processing)
                self.processing_status[client_id] = True
                return 0

    def get_next_request(self) -> Optional[Dict[str, Any]]:
        """Get next request from queue"""
        try:
            return self.queue.get_nowait()
        except queue.Empty:
            return None

    def mark_completed(self, client_id: str):
        """Mark client request as completed"""
        with self.lock:
            self.processing_status[client_id] = False

    def get_queue_size(self) -> int:
        """Get current queue size"""
        return self.queue.qsize()

class WebSocketManager:
    """Manages WebSocket connections for coder agent communication"""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.command_services: Dict[str, CommandService] = {}  # client_id -> CommandService
        self.connection_timestamps: Dict[str, float] = {}  # client_id -> connection timestamp
        self.immediate_feedback_store: Dict[str, str] = {}  # Store immediate LSP feedback
        self.dir_response_store: Dict[str, Dict[str, Any]] = {}  # Store directory responses
        self.file_response_store: Dict[str, Dict[str, Any]] = {}  # Store file responses
        self.request_queue = RequestQueue()  # Add request queue
        self._cleanup_task = None
        self._start_cleanup_task()

    def _start_cleanup_task(self):
        """Start the background cleanup task"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            self._cleanup_task = loop.create_task(self._periodic_cleanup())
        except RuntimeError:
            # No event loop running, cleanup task will be started when needed
            pass

    async def _periodic_cleanup(self):
        """Periodically check and cleanup stale connections"""
        import asyncio
        import time

        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                current_time = time.time()
                stale_clients = []

                for client_id, websocket in list(self.active_connections.items()):
                    try:
                        # Check if WebSocket is still connected
                        if websocket.client_state.name in ['DISCONNECTED', 'CLOSED']:
                            stale_clients.append(client_id)
                            continue

                        # Check for connections older than 1 hour without activity
                        connection_time = self.connection_timestamps.get(client_id, current_time)
                        if current_time - connection_time > 3600:  # 1 hour
                            logger.warning(f"🕐 Connection {client_id} has been idle for over 1 hour")
                            # Send ping to check if still alive
                            try:
                                await self.send_message(client_id, {"type": "ping"})
                            except Exception:
                                stale_clients.append(client_id)

                    except Exception as e:
                        logger.error(f"❌ Error checking connection {client_id}: {e}")
                        stale_clients.append(client_id)

                # Clean up stale connections
                for client_id in stale_clients:
                    logger.info(f"🧹 Cleaning up stale connection: {client_id}")
                    self.disconnect(client_id)

            except Exception as e:
                logger.error(f"❌ Error in periodic cleanup: {e}")

    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a new WebSocket connection"""
        import time
        await websocket.accept()
        self.active_connections[client_id] = websocket
        # Create command service for this client
        self.command_services[client_id] = CommandService()
        self.connection_timestamps[client_id] = time.time()
        #logger.info(f"🔌 WebSocket connected: {client_id}")

        # Start cleanup task if not already running
        if self._cleanup_task is None or self._cleanup_task.done():
            self._start_cleanup_task()

    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                # Try to close the WebSocket if it's still open
                if websocket.client_state.name not in ['DISCONNECTED', 'CLOSED']:
                    import asyncio
                    try:
                        asyncio.create_task(websocket.close())
                    except Exception as e:
                        logger.warning(f"⚠️ Error closing WebSocket for {client_id}: {e}")
            except Exception as e:
                logger.warning(f"⚠️ Error during WebSocket cleanup for {client_id}: {e}")
            finally:
                del self.active_connections[client_id]

        if client_id in self.command_services:
            del self.command_services[client_id]
        if client_id in self.connection_timestamps:
            del self.connection_timestamps[client_id]

        # Clean up any orphaned responses for this client
        self._cleanup_client_responses(client_id)
        logger.info(f"🔌 WebSocket disconnected: {client_id}")

    def _cleanup_client_responses(self, client_id: str):
        """Clean up orphaned responses for disconnected client"""
        # Clean up directory responses (simple: just remove client entry)
        if hasattr(self, 'dir_response_store') and client_id in self.dir_response_store:
            del self.dir_response_store[client_id]
            logger.debug(f"🧹 Cleaned up orphaned directory response for client: {client_id}")

        # Clean up file responses
        if hasattr(self, 'file_response_store') and client_id in self.file_response_store:
            del self.file_response_store[client_id]
            logger.debug(f"🧹 Cleaned up orphaned file response for client: {client_id}")

        # Clean up immediate feedback
        if hasattr(self, 'immediate_feedback_store'):
            feedback_key = f"immediate_feedback_{client_id}"
            if feedback_key in self.immediate_feedback_store:
                del self.immediate_feedback_store[feedback_key]
                logger.debug(f"🧹 Cleaned up orphaned feedback: {feedback_key}")

    def get_command_service(self, client_id: str) -> Optional[CommandService]:
        """Get command service for client"""
        return self.command_services.get(client_id)
    
    async def send_message(self, client_id: str, message: Dict[str, Any]):
        """Send a message to a specific client"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                # Check connection state before sending
                if websocket.client_state.name in ['DISCONNECTED', 'CLOSED']:
                    logger.warning(f"⚠️ Attempting to send message to disconnected client {client_id}")
                    self.disconnect(client_id)
                    return False

                await websocket.send_text(json.dumps(message))

                # Update timestamp on successful message send
                import time
                self.connection_timestamps[client_id] = time.time()
                return True

            except Exception as e:
                # Check if it's a disconnection error
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['disconnect', 'closed', 'connection', 'receive']):
                    logger.info(f"🔌 Client {client_id} disconnected during message send: {e}")
                else:
                    logger.error(f"❌ Failed to send message to {client_id}: {e}")

                # Clean up the connection
                self.disconnect(client_id)
                return False
        else:
            logger.warning(f"⚠️ Attempted to send message to non-existent client: {client_id}")
            return False

    async def shutdown(self):
        """Gracefully shutdown all connections"""
        logger.info("🛑 Shutting down WebSocket manager...")

        # Cancel cleanup task
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        # Close all active connections
        for client_id in list(self.active_connections.keys()):
            try:
                websocket = self.active_connections[client_id]
                await websocket.close()
                logger.info(f"🔌 Closed WebSocket connection: {client_id}")
            except Exception as e:
                logger.error(f"❌ Error closing connection {client_id}: {e}")
            finally:
                self.disconnect(client_id)

        logger.info("✅ WebSocket manager shutdown complete")
    
    async def broadcast_console_output(self, client_id: str, message: str, level: str = "info"):
        """Send console output to client for streaming display"""
        console_message = {
            "type": "console_output",
            "data": {
                "message": message,
                "level": level,
                "timestamp": asyncio.get_event_loop().time()
            }
        }
        
        await self.send_message(client_id, console_message)
    
    async def request_file(self, client_id: str, file_path: str) -> Optional[str]:
        """Request a file from the client and wait for response"""
        try:
            # Send file request
            await self.send_message(client_id, {
                "type": "file_request",
                "data": {"path": file_path}
            })

            # Wait for response (with timeout)
            websocket = self.active_connections.get(client_id)
            if not websocket:
                return None

            # Wait for file response
            try:
                response = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                data = json.loads(response)

                if data.get("type") == "file_response" and data.get("data", {}).get("path") == file_path:
                    content = data["data"].get("content", "")
                    logger.info(f"📁 Received file: {file_path} ({len(content)} chars)")
                    return content

            except asyncio.TimeoutError:
                logger.warning(f"⏰ Timeout waiting for file: {file_path}")
                return None

        except Exception as e:
            logger.error(f"❌ Error requesting file {file_path}: {e}")
            return None
    
    async def send_file_command(self, client_id: str, command: FileCommand) -> bool:
        """Send a file command to the client"""
        try:
            await self.send_message(client_id, {
                "type": "file_command",
                "data": command.to_dict()
            })
            logger.info(f"📤 Sent {command.type.value} command: {command.file_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to send command {command.command_id}: {e}")
            return False

    async def handle_command_acknowledgment(self, client_id: str, ack_data: Dict[str, Any]) -> bool:
        """Handle command acknowledgment from client"""
        command_service = self.get_command_service(client_id)
        if command_service:
            return command_service.handle_acknowledgment(ack_data)
        return False
    
    async def update_agent_status(self, client_id: str, status: str, details: str = ""):
        """Update the agent status on the client"""
        await self.send_message(client_id, {
            "type": "agent_status",
            "data": {
                "status": status,
                "details": details
            }
        })
    
    async def request_file_version(self, client_id: str, file_path: str) -> Optional[str]:
        """Request file version/checksum from client"""
        try:
            await self.send_message(client_id, {
                "type": "file_version_request",
                "data": {"path": file_path}
            })

            websocket = self.active_connections.get(client_id)
            if not websocket:
                return None

            try:
                response = await asyncio.wait_for(websocket.receive_text(), timeout=10.0)
                data = json.loads(response)

                if data.get("type") == "file_version_response" and data.get("data", {}).get("path") == file_path:
                    return data["data"].get("version")

            except asyncio.TimeoutError:
                logger.warning(f"⏰ Timeout waiting for file version: {file_path}")
                return None

        except Exception as e:
            logger.error(f"❌ Error requesting file version {file_path}: {e}")
            return None

    def add_to_queue(self, client_id: str, request_data: Dict[str, Any]) -> int:
        """Add request to queue and return queue position (0 means immediate processing)"""
        return self.request_queue.add_request(client_id, request_data)

    def get_next_queued_request(self) -> Optional[Dict[str, Any]]:
        """Get next request from queue"""
        return self.request_queue.get_next_request()

    def mark_request_completed(self, client_id: str):
        """Mark client request as completed and process next in queue"""
        self.request_queue.mark_completed(client_id)

    def get_queue_size(self) -> int:
        """Get current queue size"""
        return self.request_queue.get_queue_size()

# Global WebSocket manager instance
websocket_manager = WebSocketManager()

class CommandBasedFileSystem:
    """Command-based file system that sends file operation commands to client"""

    def __init__(self, client_id: str, ws_manager: WebSocketManager):
        self.client_id = client_id
        self.ws_manager = ws_manager

    async def read_file(self, file_path: str) -> str:
        """Read a file by requesting from client"""
        content = await self.ws_manager.request_file(self.client_id, file_path)
        return content or ""

    async def replace_lines(
        self,
        file_path: str,
        start_line: int,
        end_line: int,
        content: str,
        file_version: Optional[str] = None
    ) -> bool:
        """Replace lines in file using command"""
        command_service = self.ws_manager.get_command_service(self.client_id)
        if not command_service:
            return False

        command = command_service.create_replace_lines_command(
            file_path, start_line, end_line, content, file_version
        )

        # Send command to client
        success = await self.ws_manager.send_file_command(self.client_id, command)
        if not success:
            return False

        # Wait for acknowledgment
        ack = await command_service.wait_for_acknowledgment(command.command_id)
        return ack and ack.status.value == "success"

    async def insert_lines(
        self,
        file_path: str,
        line_number: int,
        content: str,
        file_version: Optional[str] = None
    ) -> bool:
        """Insert lines in file using command"""
        command_service = self.ws_manager.get_command_service(self.client_id)
        if not command_service:
            return False

        command = command_service.create_insert_lines_command(
            file_path, line_number, content, file_version
        )

        success = await self.ws_manager.send_file_command(self.client_id, command)
        if not success:
            return False

        ack = await command_service.wait_for_acknowledgment(command.command_id)
        return ack and ack.status.value == "success"

    async def delete_lines(
        self,
        file_path: str,
        start_line: int,
        end_line: int,
        file_version: Optional[str] = None
    ) -> bool:
        """Delete lines in file using command"""
        command_service = self.ws_manager.get_command_service(self.client_id)
        if not command_service:
            return False

        command = command_service.create_delete_lines_command(
            file_path, start_line, end_line, file_version
        )

        success = await self.ws_manager.send_file_command(self.client_id, command)
        if not success:
            return False

        ack = await command_service.wait_for_acknowledgment(command.command_id)
        return ack and ack.status.value == "success"

    async def create_file(self, file_path: str, content: str) -> bool:
        """Create file using command"""
        command_service = self.ws_manager.get_command_service(self.client_id)
        if not command_service:
            return False

        command = command_service.create_create_file_command(file_path, content)

        success = await self.ws_manager.send_file_command(self.client_id, command)
        if not success:
            return False

        ack = await command_service.wait_for_acknowledgment(command.command_id)
        return ack and ack.status.value == "success"

    async def delete_file(self, file_path: str) -> bool:
        """Delete file using command"""
        command_service = self.ws_manager.get_command_service(self.client_id)
        if not command_service:
            return False

        command = command_service.create_delete_file_command(file_path)

        success = await self.ws_manager.send_file_command(self.client_id, command)
        if not success:
            return False

        ack = await command_service.wait_for_acknowledgment(command.command_id)
        return ack and ack.status.value == "success"

    async def get_file_version(self, file_path: str) -> Optional[str]:
        """Get file version/checksum from client"""
        return await self.ws_manager.request_file_version(self.client_id, file_path)

    async def file_exists(self, file_path: str) -> bool:
        """Check if file exists by requesting from client"""
        content = await self.read_file(file_path)
        return content is not None and content != ""

    async def list_directory(self, dir_path: str) -> list:
        """List directory contents by requesting from client"""
        try:
            from websocket.utils import request_directory_from_client
            files = await request_directory_from_client(self.ws_manager, self.client_id, dir_path)
            return files

        except Exception as e:
            logger.error(f"❌ Error listing directory {dir_path}: {e}")

        return []

# Backward compatibility alias
VirtualFileSystem = CommandBasedFileSystem
