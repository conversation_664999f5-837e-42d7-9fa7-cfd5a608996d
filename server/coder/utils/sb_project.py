import requests
import zipfile
import io
import os
import json

from rich import print

from pathlib import Path

class SpringBootProject:

    def load_config(self, json_file_path):
        try:
            with open(json_file_path, 'r') as file:
                return json.load(file)
        except FileNotFoundError:
            print(f"Error: Config file '{json_file_path}' not found")
            return None
        except json.JSONDecodeError:
            print(f"Error: Invalid JSON format in '{json_file_path}'")
            return None

    def generate_spring_project(self, config):
        group_id = config.get("group_id", "com.example")
        artifact_id = config.get("artifact_id", "demo")
        name = config.get("name", "demo")
        description = config.get("description", "Demo project for Spring Boot")
        package_name = config.get("package_name", "com.example.demo")
        dependencies = config.get("dependencies", ["web", "data-jpa", "lombok"])
        spring_boot_version = config.get("spring_boot_version", "3.4.5")
        java_version = config.get("java_version", "21")
        
        output_path = Path(os.getenv("MODERN_CODE_FODLER"))
        output_path.mkdir(parents=True, exist_ok=True)
        
        params = {
            "type": "maven-project",
            "language": "java",
            "bootVersion": spring_boot_version,
            "baseDir": artifact_id,
            "groupId": group_id,
            "artifactId": artifact_id,
            "name": name,
            "description": description,
            "packageName": package_name,
            "packaging": "jar",
            "javaVersion": java_version,
            "dependencies": ",".join(dependencies)
        }
        
        response = requests.get(
            "https://start.spring.io/starter.zip",
            params=params,
            stream=True
        )
        
        if response.status_code == 200:
            z = zipfile.ZipFile(io.BytesIO(response.content))

            project_path = output_path / artifact_id
            z.extractall(path=output_path)
            print(f"Spring Boot project '{artifact_id}' generated successfully!")
            print(f"Project location: {project_path.absolute()}")
        else:
            print(f"Error generating project: {response.status_code}")
            print(response.text)

    def create(self, config_file):
        config = self.load_config(config_file)
        
        if config:
            self.generate_spring_project(config)
