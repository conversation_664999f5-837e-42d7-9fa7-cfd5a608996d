import os
import requests

from rich.console import Console

console = Console()

from dotenv import load_dotenv
load_dotenv(override=True)  

class LLM:
    
    def __init__(self):
        self.base_url = os.getenv("SLINGSHOT_URL_UAT")
      
        
        self.bearer_token = os.getenv("LLM_API_KEY")
      

    def call_llm(self, system_prompt):
        try:
            content = {
                "model": "slingshot-gpt-4o-model",
                "prompt": system_prompt
            }
            with console.status("Thinking", spinner="dots"):
                response = requests.post(url=self.base_url, json=content)            
            if response.status_code == 200:
                return response.json()["choices"][0]["text"]
            else:
                return "Slingshot is not reachable"
        except Exception as e:
            print("Error in query", e)
            return "Slingshot has errors"
        

    def call_claude_llm(self, system_prompt):
        try:
            content = {
                "model": "slingshot-claude-model",
                "prompt": system_prompt
            }
            with console.status("Thinking", spinner="dots"):
                response = requests.post(url=self.base_url, json=content)
            if response.status_code == 200:
                return response.json()["choices"][0]["text"]
            else:
                return "Slingshot is not reachable"
        except Exception as e:
            print("Error in query", e)
            return "Slingshot has errors"
        
    def call_litellm(self, system_prompt):
        headers = {
            'Authorization': f'Bearer {self.bearer_token}',
            'Content-Type': 'application/json'
        }
        
       
        content = {
            "max_tokens": 16384,
            "messages": [
                {
                    "role": "user",
                    "content": system_prompt
                }
            ],
            "model": "gpt-4o",
            "n": 1,
            "stream": False,
            "temperature": 0.2,
            "top_p": 1
        }
        try:
            with console.status("Thinking", spinner="dots"):
               
                response = requests.post(self.base_url, json=content, headers=headers)
                
            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
            else:
                return "Slingshot is not reachable"
        except Exception as e:
            print(f"Error in query: {e}")
            return "Slingshot has errors"
        
    def call_litellm_old(self, user_prompt):
       
       
        try:
            url = "http://0.0.0.0:4000/chat/completions"  # Updated to match the curl URL
            payload = {
                "model": "slingshot-model",
                #"model": "gemini-2.0-flash",
                "messages": [
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ]
            }
            headers = {
                "Content-Type": "application/json"
            }

            with console.status("Thinking", spinner="dots"):
                response = requests.post(url=url, json=payload, headers=headers)

            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
            else:
                return f"Slingshot is not reachable. Status code: {response.status_code}"

        except Exception as e:
            print("Error in query:", e)
            return "Slingshot has errors"
    def call_litellm_waste(self, user_prompt):
        try:
            model = os.getenv("LLM_MODEL", "slingshot-model")
            endpoint = os.getenv("SLINGSHOT_URL", "http://localhost:4000/chat/completions")
            api_key = os.getenv("LLM_API_KEY")
            print(f"api_key -- {api_key}")

            if not api_key:
                raise ValueError("Missing LLM_API_KEY in environment")

            url = endpoint
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ]
            }
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }

            with console.status("Thinking", spinner="dots"):
                response = requests.post(url=url, json=payload, headers=headers)

            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
            else:
                return f"Slingshot is not reachable. Status code: {response.status_code} | Message: {response.text}"

        except Exception as e:
            print("Error in query:", e)
            return "Slingshot has errors"