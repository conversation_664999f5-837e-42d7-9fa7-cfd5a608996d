import yaml
import re
from typing import Any, Dict, Optional, List
import logging
from dataclasses import dataclass

class Yaml:

    def extract_yaml_from_text(self, text):
        yaml_block_match = re.search(r"```yaml\n(.*?)\n```", text, re.DOTALL)
        
        if not yaml_block_match:
            raise ValueError("No YAML block found in the text.")

        yaml_content = yaml_block_match.group(1)
        parsed_yaml = yaml.safe_load(yaml_content)
        return parsed_yaml

    def process(self, content):
        try:
            clean_yaml = self.extract_yaml_from_text(content)
            return clean_yaml
        except (yaml.YAMLError, ValueError) as e:
            # If basic extraction fails, try with the comprehensive YAML fixer
            try:
                yaml_block_match = re.search(r"```yaml\n(.*?)\n```", content, re.DOTALL)
                if yaml_block_match:
                    yaml_content = yaml_block_match.group(1)
                    # Use the comprehensive YAML fixer
                    fixer = YAMLFixer(debug=False)
                    result = fixer.fix_yaml(yaml_content)
                    if result.success:
                        return result.data
                    else:
                        # Last resort: try to fix the specific colon issue in reason field
                        return self._fix_reason_colon_issue(yaml_content)
                else:
                    raise ValueError("No YAML block found in the text.")
            except Exception as fallback_error:
                # Final fallback: try to fix the specific colon issue
                yaml_block_match = re.search(r"```yaml\n(.*?)\n```", content, re.DOTALL)
                if yaml_block_match:
                    yaml_content = yaml_block_match.group(1)
                    return self._fix_reason_colon_issue(yaml_content)
                raise ValueError(f"Failed to parse YAML after all attempts: {str(e)}")

    def _fix_reason_colon_issue(self, yaml_content):
        """Fix the specific issue where reason field contains unquoted colons"""
        lines = yaml_content.splitlines()
        fixed_lines = []
        in_reason = False
        reason_content = []

        for line in lines:
            stripped = line.strip()

            # Check if we're starting the reason field
            if stripped.startswith('reason:'):
                in_reason = True
                reason_text = stripped[7:].strip()  # Remove 'reason:' prefix
                if reason_text:
                    # If there's content on the same line, start collecting it
                    reason_content = [reason_text]
                else:
                    reason_content = []
                continue

            # If we're in reason field and encounter another field, end reason
            elif in_reason and ':' in stripped and not stripped.startswith(' '):
                # End of reason field, quote the collected content
                if reason_content:
                    full_reason = ' '.join(reason_content)
                    fixed_lines.append(f'reason: "{full_reason}"')
                else:
                    fixed_lines.append('reason: ""')
                in_reason = False
                reason_content = []
                fixed_lines.append(line)

            # If we're in reason field, collect the content
            elif in_reason:
                if stripped:
                    reason_content.append(stripped)

            # Regular line, not in reason field
            else:
                fixed_lines.append(line)

        # Handle case where reason is the last field
        if in_reason and reason_content:
            full_reason = ' '.join(reason_content)
            fixed_lines.append(f'reason: "{full_reason}"')

        fixed_yaml = '\n'.join(fixed_lines)
        try:
            return yaml.safe_load(fixed_yaml)
        except yaml.YAMLError as e:
            # If still failing, return a basic structure to prevent complete failure
            return {
                'tool': 'unknown',
                'reason': 'YAML parsing failed, using fallback',
                'params': {},
                'result': '',
                'data': ''
            }

@dataclass
class YAMLFixResult:
    """Class to hold the result of YAML fixing attempts"""
    success: bool
    data: Optional[Dict[str, Any]]
    original_error: Optional[str] = None
    fixes_applied: List[str] = None
    fixed_content: Optional[str] = None

class YAMLFixer:
    def __init__(self, debug: bool = False):
        self.debug = debug
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """Setup logging configuration"""
        logger = logging.getLogger('YAMLFixer')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        logger.setLevel(logging.DEBUG if self.debug else logging.INFO)
        return logger

    def fix_yaml(self, content: str) -> YAMLFixResult:
        """Main method to fix YAML content"""
        fixes_applied = []
        original_content = content
        
        # First try parsing as-is
        try:
            data = yaml.safe_load(content)
            return YAMLFixResult(
                success=True,
                data=data,
                fixes_applied=['none_needed'],
                fixed_content=content
            )
        except yaml.YAMLError as e:
            original_error = str(e)
            self.logger.debug(f"Initial parsing failed: {original_error}")

        # Apply fixes one by one until successful
        fixing_methods = [
            self._fix_indentation,
            self._fix_code_blocks,
            self._fix_replacement_blocks,
            self._fix_java_code_blocks,
            self._fix_special_characters,
            self._fix_quotation_marks,
            self._fix_line_endings,
            self._fix_trailing_spaces,
            self._fix_empty_lines,
            self._fix_list_formatting,
            self._fix_common_syntax_errors
        ]

        for fix_method in fixing_methods:
            try:
                content, fix_name = fix_method(content)
                if fix_name:
                    fixes_applied.append(fix_name)
                    
                # Try parsing after each fix
                data = yaml.safe_load(content)
                return YAMLFixResult(
                    success=True,
                    data=data,
                    fixes_applied=fixes_applied,
                    fixed_content=content
                )
            except yaml.YAMLError as e:
                self.logger.debug(f"Fix {fix_method.__name__} applied, but parsing still failed: {str(e)}")
                continue
            except Exception as e:
                self.logger.debug(f"Error during {fix_method.__name__}: {str(e)}")
                continue

        # If all fixes fail, try aggressive fixing
        try:
            content, fixes = self._aggressive_fix(content)
            fixes_applied.extend(fixes)
            data = yaml.safe_load(content)
            return YAMLFixResult(
                success=True,
                data=data,
                fixes_applied=fixes_applied,
                fixed_content=content
            )
        except Exception as e:
            # Last resort: try to fix the specific YAML structure issue
            try:
                content = self._fix_yaml_structure_issues(content)
                fixes_applied.append('yaml_structure_fix')
                data = yaml.safe_load(content)
                return YAMLFixResult(
                    success=True,
                    data=data,
                    fixes_applied=fixes_applied,
                    fixed_content=content
                )
            except Exception as final_e:
                return YAMLFixResult(
                    success=False,
                    data=None,
                    original_error=original_error,
                    fixes_applied=fixes_applied,
                    fixed_content=content
                )

    def _fix_indentation(self, content: str) -> tuple[str, str]:
        """Fix indentation issues"""
        lines = content.splitlines()
        fixed_lines = []
        current_indent = 0
        in_block = False
        
        for line in lines:
            stripped = line.lstrip()
            if not stripped:
                fixed_lines.append('')
                continue
                
            # Detect block indicators
            if ':' in stripped and not stripped.startswith('#'):
                if not in_block:
                    current_indent += 2
                
            # Handle code blocks
            if '|' in stripped:
                in_block = True
                current_indent += 2
            
            # Apply indentation
            if in_block and stripped.startswith(('if', 'for', 'class', 'def')):
                indent = ' ' * (current_indent + 2)
            else:
                indent = ' ' * current_indent
                
            fixed_lines.append(f"{indent}{stripped}")
            
            # Reset block status
            if stripped == '}' or stripped.endswith('}'):
                in_block = False
                current_indent = max(0, current_indent - 2)
                
        return '\n'.join(fixed_lines), 'fixed_indentation'

    def _fix_code_blocks(self, content: str) -> tuple[str, str]:
        """Fix code block formatting"""
        lines = content.splitlines()
        fixed_lines = []
        in_block = False
        block_indent = 0

        for i, line in enumerate(lines):
            if 'replacement:' in line and '|' in line:
                in_block = True
                block_indent = len(line) - len(line.lstrip()) + 2
                fixed_lines.append(line)
                continue
            elif 'replacement:' in line:
                # Handle case where | is on next line
                in_block = True
                block_indent = len(line) - len(line.lstrip()) + 2
                fixed_lines.append(line)
                # Check if next line has |
                if i + 1 < len(lines) and '|' in lines[i + 1]:
                    continue
                else:
                    # Add | if missing
                    fixed_lines.append(' ' * block_indent + '|')
                continue

            if in_block:
                # Check if we're exiting the block (next key at same or lower indentation)
                if line.strip() and not line.startswith(' ' * block_indent) and ':' in line:
                    in_block = False
                    fixed_lines.append(line)
                    continue

                if not line.strip():
                    fixed_lines.append('')
                else:
                    # Ensure proper indentation for code block content
                    stripped = line.lstrip()
                    indent = ' ' * block_indent
                    fixed_lines.append(f"{indent}{stripped}")
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines), 'fixed_code_blocks'

    def _fix_replacement_blocks(self, content: str) -> tuple[str, str]:
        """Fix specific issues with replacement blocks containing closing braces and comments"""
        lines = content.splitlines()
        fixed_lines = []
        in_replacement = False
        replacement_indent = 0

        for i, line in enumerate(lines):
            if 'replacement:' in line:
                in_replacement = True
                replacement_indent = len(line) - len(line.lstrip()) + 2
                fixed_lines.append(line)
                continue
            elif line.strip() == '|' and in_replacement:
                fixed_lines.append(line)
                continue
            elif in_replacement:
                # Check if we're exiting the replacement block (next YAML key at same or lower indentation)
                if (line.strip() and
                    not line.startswith(' ' * replacement_indent) and
                    ':' in line and
                    not line.strip().startswith('}') and
                    not line.strip().startswith('//')):
                    in_replacement = False
                    fixed_lines.append(line)
                    continue

                # Fix indentation for content in replacement block
                if line.strip():
                    stripped = line.lstrip()
                    indent = ' ' * replacement_indent

                    # Handle different types of content
                    if (stripped.startswith('//') or  # Comments
                        stripped.startswith('/*') or  # Block comments
                        stripped.startswith('*') or   # Block comment continuation
                        stripped.startswith('*/') or  # Block comment end
                        stripped in ['}', '};', '})', '};)'] or  # Closing braces
                        stripped.startswith('} //') or  # Closing brace with comment
                        stripped.startswith('public ') or  # Method declarations
                        stripped.startswith('private ') or  # Method declarations
                        stripped.startswith('protected ') or  # Method declarations
                        stripped.startswith('return ') or  # Return statements
                        stripped.endswith(';') or  # Statements
                        stripped.endswith('{') or  # Opening braces
                        stripped.endswith('{')):  # Opening braces
                        fixed_lines.append(f"{indent}{stripped}")
                    else:
                        # Regular content - ensure proper indentation
                        fixed_lines.append(f"{indent}{stripped}")
                else:
                    # Empty line in replacement block
                    fixed_lines.append('')
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines), 'fixed_replacement_blocks'

    def _fix_java_code_blocks(self, content: str) -> tuple[str, str]:
        """Fix Java-specific code block issues in YAML replacement sections"""
        lines = content.splitlines()
        fixed_lines = []
        in_replacement = False
        replacement_indent = 0

        for line in lines:
            if 'replacement:' in line and '|' in line:
                in_replacement = True
                replacement_indent = len(line) - len(line.lstrip()) + 2
                fixed_lines.append(line)
                continue
            elif in_replacement:
                # Check if we're exiting the replacement block
                if (line.strip() and
                    not line.startswith(' ') and
                    ':' in line and
                    not line.strip().startswith('//') and
                    not line.strip().startswith('}') and
                    not line.strip().startswith('public ') and
                    not line.strip().startswith('private ') and
                    not line.strip().startswith('return ')):
                    in_replacement = False
                    fixed_lines.append(line)
                    continue

                # Fix Java code indentation
                if line.strip():
                    stripped = line.lstrip()
                    indent = ' ' * replacement_indent

                    # Special handling for Java constructs
                    if (stripped.startswith('// ') or  # Comments
                        stripped.startswith('/* ') or  # Block comments
                        stripped.startswith('public ') or  # Method declarations
                        stripped.startswith('private ') or  # Method declarations
                        stripped.startswith('protected ') or  # Method declarations
                        stripped.startswith('return ') or  # Return statements
                        stripped == '}' or  # Closing braces
                        stripped.startswith('} //')):  # Closing brace with comment
                        fixed_lines.append(f"{indent}{stripped}")
                    else:
                        # Regular Java code
                        fixed_lines.append(f"{indent}{stripped}")
                else:
                    # Empty line
                    fixed_lines.append('')
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines), 'fixed_java_code_blocks'

    def _fix_special_characters(self, content: str) -> tuple[str, str]:
        """Fix special characters and escape sequences"""
        # Replace tabs with spaces
        content = content.replace('\t', '  ')
        
        # Fix common escape sequences
        content = re.sub(r'\\([^nrt"])', r'\\\\\1', content)
        
        return content, 'fixed_special_chars'

    def _fix_quotation_marks(self, content: str) -> tuple[str, str]:
        """Fix quotation mark issues"""
        lines = content.splitlines()
        fixed_lines = []
        
        for line in lines:
            # Fix unmatched quotes
            quote_count = line.count('"') + line.count("'")
            if quote_count % 2 != 0:
                if '"' in line:
                    line = line.replace('"', "'")
                else:
                    line = f'"{line.strip("\'")}"'
            fixed_lines.append(line)
            
        return '\n'.join(fixed_lines), 'fixed_quotes'

    def _fix_line_endings(self, content: str) -> tuple[str, str]:
        """Normalize line endings"""
        # Convert all line endings to Unix style
        content = content.replace('\r\n', '\n')
        content = content.replace('\r', '\n')
        
        # Ensure file ends with single newline
        content = content.rstrip('\n') + '\n'
        
        return content, 'fixed_line_endings'

    def _fix_trailing_spaces(self, content: str) -> tuple[str, str]:
        """Remove trailing spaces"""
        lines = [line.rstrip() for line in content.splitlines()]
        return '\n'.join(lines), 'fixed_trailing_spaces'

    def _fix_empty_lines(self, content: str) -> tuple[str, str]:
        """Fix empty line issues"""
        lines = content.splitlines()
        fixed_lines = []
        prev_empty = False
        
        for line in lines:
            if not line.strip():
                if not prev_empty:
                    fixed_lines.append('')
                prev_empty = True
            else:
                fixed_lines.append(line)
                prev_empty = False
                
        return '\n'.join(fixed_lines), 'fixed_empty_lines'

    def _fix_list_formatting(self, content: str) -> tuple[str, str]:
        """Fix YAML list formatting"""
        lines = content.splitlines()
        fixed_lines = []
        
        for line in lines:
            stripped = line.lstrip()
            if stripped.startswith('- '):
                indent = len(line) - len(stripped)
                fixed_lines.append(f"{' ' * indent}- {stripped[2:].lstrip()}")
            else:
                fixed_lines.append(line)
                
        return '\n'.join(fixed_lines), 'fixed_list_formatting'

    def _fix_common_syntax_errors(self, content: str) -> tuple[str, str]:
        """Fix common YAML syntax errors"""
        # Fix missing spaces after colons
        content = re.sub(r'([^:\s]):([^\s\n])', r'\1: \2', content)
        
        # Fix multiple colons in the same line
        content = re.sub(r':(\s*):+', ':', content)
        
        return content, 'fixed_syntax_errors'

    def _fix_yaml_structure_issues(self, content: str) -> str:
        """Last resort fix for YAML structure issues"""
        lines = content.splitlines()
        fixed_lines = []
        in_replacement = False
        replacement_indent = 0

        for line in lines:
            if 'replacement:' in line and '|' in line:
                in_replacement = True
                replacement_indent = len(line) - len(line.lstrip()) + 2
                fixed_lines.append(line)
                continue
            elif in_replacement:
                # Check if this looks like a YAML key (exit replacement block)
                if (line.strip() and
                    not line.startswith(' ') and
                    ':' in line and
                    not line.strip().startswith('//') and
                    not line.strip().startswith('}') and
                    not any(line.strip().startswith(keyword) for keyword in
                           ['public ', 'private ', 'protected ', 'return ', 'if ', 'for ', 'while '])):
                    in_replacement = False
                    fixed_lines.append(line)
                    continue

                # Force proper indentation for all content in replacement block
                if line.strip():
                    stripped = line.lstrip()
                    indent = ' ' * replacement_indent
                    fixed_lines.append(f"{indent}{stripped}")
                else:
                    fixed_lines.append('')
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _aggressive_fix(self, content: str) -> tuple[str, List[str]]:
        """Aggressive fixing for severely malformed YAML"""
        fixes_applied = []
        
        # Remove all comments
        content = re.sub(r'#.*$', '', content, flags=re.MULTILINE)
        fixes_applied.append('removed_comments')
        
        # Normalize document separators
        content = re.sub(r'---+', '---', content)
        fixes_applied.append('normalized_separators')
        
        # Fix severe indentation issues
        content = self._fix_severe_indentation(content)
        fixes_applied.append('aggressive_indentation')
        
        return content, fixes_applied

    def _fix_severe_indentation(self, content: str) -> str:
        """Fix severe indentation issues"""
        lines = content.splitlines()
        fixed_lines = []
        indent_level = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                fixed_lines.append('')
                continue
                
            # Adjust indent level based on content
            if stripped.endswith(':'):
                fixed_lines.append(f"{' ' * (indent_level * 2)}{stripped}")
                indent_level += 1
            elif stripped.startswith('- '):
                fixed_lines.append(f"{' ' * (indent_level * 2)}{stripped}")
            else:
                fixed_lines.append(f"{' ' * (indent_level * 2)}{stripped}")
                
            # Decrease indent for closing brackets
            if stripped in ['}', ']']:
                indent_level = max(0, indent_level - 1)
                
        return '\n'.join(fixed_lines)

def process_yaml_content(content: str, debug: bool = False) -> Dict[str, Any]:
    """Process YAML content with comprehensive error handling and fixing"""
    fixer = YAMLFixer(debug=debug)
    result = fixer.fix_yaml(content)
    
    if result.success:
        if debug:
            print("Fixes applied:", result.fixes_applied)
            print("Fixed content:", result.fixed_content)
        return result.data
    else:
        raise ValueError(f"Failed to parse YAML: {result.original_error}")
