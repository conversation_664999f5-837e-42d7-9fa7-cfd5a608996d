import os

from utils.yaml_processor import Yaml
from utils.prompt_template import PromptTemplate

class History:

    def init(self, question, project_name,client_id,ws_manager):
      
        # Set up command-based file system in shared context
        #shared['command_fs'] = self.command_fs
        

        # working_dir is simply the project_name folder
        working_dir= "."

        shared = {
            "coder_prompt": PromptTemplate().coder_prompt(question, project_name),
            "user_query": question,
            "immediate_lsp_feedback": "",
            "project_name": project_name,
            "working_dir": working_dir,
            "total_iterations": 1,
            "code_applied":False,
            "history": [
                {
                    "tool": "",
                    "reason": "",
                    "params": "",
                    "result": "",
                    "data": ""
                }
            ]
        }
        shared['client_id'] = client_id
        shared['ws_manager'] = ws_manager
        return shared

    def add(self, shared, exec_res):
        yaml_data = Yaml().process(exec_res)
        history = {
            "tool": yaml_data.get("tool", ""),
            "reason": yaml_data.get("reason", ""),
            "params": yaml_data.get("params", {}),
            "result": yaml_data.get("result", ""),
            "data": yaml_data.get("data", "")
        }
        shared["history"].append(history)
        return history
    
    def get_latest(self, shared):
        return shared["history"][-1]

    def get_all_histories(self, shared):
        """Get all histories for summary analysis"""
        return shared.get("history", [])
    

    
    def get_latest_specific_tool_data(self,shared,specific_tool_name):
        all_history = shared["history"]
        
        for entry in reversed(all_history):
                if entry.get("tool") == specific_tool_name and "data" in entry:
                    return entry.get("data", {})
        return {}
                   
