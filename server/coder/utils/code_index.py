import os
import tiktoken
import subprocess

from tree_sitter_language_pack import get_parser
class CodeCompress:

    def __init__(self):
        pass

    def compress(self, location: str):
        last_folder = os.path.basename(os.path.normpath(location))
        save_folder = os.getenv("LEGACY_CODE_FOLDER")
        subprocess.run(f'npx repomix --output {save_folder}/{last_folder}.compress {location}', shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        total_tokens = self.analyze_tokens(f"{save_folder}/{last_folder}.compress")
        print(f"📦 source code {last_folder} compressed with total tokens {total_tokens}")

    def analyze_tokens(self, file: str):
        # Use default encoding if environment variable is not set
        encoding_model = os.getenv("ENCODING_TOKEN_MODEL", "cl100k_base")  # Default for GPT-4
        tokenizer = tiktoken.get_encoding(encoding_model)
        with open(file, "r") as file:
            text = file.read()
        tokens = tokenizer.encode(text)
        token_count = len(tokens)
        return self.human_readable(token_count)

    def analyze_tokens_from_content(self, content: str):
        # Use default encoding if environment variable is not set
        encoding_model = os.getenv("ENCODING_TOKEN_MODEL", "cl100k_base")  # Default for GPT-4
        tokenizer = tiktoken.get_encoding(encoding_model)
        tokens = tokenizer.encode(content)
        token_count = len(tokens)
        return self.human_readable(token_count)

    def human_readable(self, number):
        # If the number is in the thousands, millions, etc., we will use K, M, B, etc.
        if number >= 1_000_000_000:
            return f"{number / 1_000_000_000:.1f}B"
        elif number >= 1_000_000:
            return f"{number / 1_000_000:.1f}M"
        elif number >= 1_000:
            return f"{number / 1_000:.1f}K"
        else:
            return str(number)

class CallGraphBuilder:
    def __init__(self):
        self.parser = get_parser("java")
        self.call_graph = {}  # Store method calls
        self.class_methods = {}  # Store class->methods mapping
        self.class_files = {}  # Store class->file mapping
        
    def parse_file(self, filepath):
        with open(filepath, "rb") as f:
            code = f.read()
        return self.parser.parse(code), code

    def build_call_graph(self, base_dir):
        """Build call graph for all Java files in directory"""
        # First pass: collect all classes and their methods
        for root, _, files in os.walk(base_dir):
            for file in files:
                if file.endswith(".java"):
                    full_path = os.path.join(root, file)
                    relative_path = os.path.relpath(full_path, base_dir)
                    tree, code = self.parse_file(full_path)
                    self.collect_class_methods(tree.root_node, code, relative_path)
        
        # Second pass: analyze method calls
        for root, _, files in os.walk(base_dir):
            for file in files:
                if file.endswith(".java"):
                    full_path = os.path.join(root, file)
                    tree, code = self.parse_file(full_path)
                    self.analyze_method_calls(tree.root_node, code)
                    
        self.save_call_graph(base_dir)

    def get_method_signature(self, method_node, code_bytes):
        """Extract method signature including return type and parameters"""
        method_name = method_node.child_by_field_name("name")
        return_type_node = method_node.child_by_field_name("type")
        parameters = method_node.child_by_field_name("parameters")
        
        name = code_bytes[method_name.start_byte:method_name.end_byte].decode() if method_name else "unknown"
        
        # Handle return type
        ret_type = "void"  # default
        if return_type_node:
            ret_type = code_bytes[return_type_node.start_byte:return_type_node.end_byte].decode()
        else:
            # Try to find return type in the direct children
            for child in method_node.children:
                if child.type in ["primitive_type", "type_identifier"]:
                    ret_type = code_bytes[child.start_byte:child.end_byte].decode()
                    break
        
        params = []
        if parameters:
            for param in parameters.children:
                if param.type == "formal_parameter":
                    param_type = param.child_by_field_name("type")
                    param_name = param.child_by_field_name("name")
                    if param_type and param_name:
                        param_str = f"{code_bytes[param_type.start_byte:param_type.end_byte].decode()} {code_bytes[param_name.start_byte:param_name.end_byte].decode()}"
                        params.append(param_str)
                    
        return {
            "name": name,
            "return_type": ret_type,
            "parameters": params
        }

    def collect_class_methods(self, node, code_bytes, file_path, current_class=None):
        """First pass: collect all classes and their methods"""
        if node.type == "class_declaration":
            class_name = node.child_by_field_name("name")
            if class_name:
                current_class = code_bytes[class_name.start_byte:class_name.end_byte].decode()
                self.class_methods[current_class] = {}
                self.class_files[current_class] = file_path

        if node.type == "method_declaration" and current_class:
            method_sig = self.get_method_signature(node, code_bytes)
            self.class_methods[current_class][method_sig["name"]] = method_sig

        for child in node.children:
            self.collect_class_methods(child, code_bytes, file_path, current_class)

    def analyze_method_calls(self, node, code_bytes, current_class=None, current_method=None):
        """Second pass: analyze method calls between classes"""
        if node.type == "class_declaration":
            class_name = node.child_by_field_name("name")
            if class_name:
                current_class = code_bytes[class_name.start_byte:class_name.end_byte].decode()

        if node.type == "method_declaration" and current_class:
            method_sig = self.get_method_signature(node, code_bytes)
            current_method = method_sig["name"]
            if current_class not in self.call_graph:
                self.call_graph[current_class] = {}
            if current_method not in self.call_graph[current_class]:
                self.call_graph[current_class][current_method] = {
                    "signature": method_sig,
                    "calls": set()
                }

        if node.type == "method_invocation" and current_class and current_method:
            method_name = node.child_by_field_name("name")
            if method_name:
                called_method = code_bytes[method_name.start_byte:method_name.end_byte].decode()
                target_class = self.find_target_class(called_method)
                if target_class:
                    self.call_graph[current_class][current_method]["calls"].add(
                        (target_class, called_method)
                    )

        for child in node.children:
            self.analyze_method_calls(child, code_bytes, current_class, current_method)

    def find_target_class(self, method_name):
        """Find which class contains the called method"""
        for class_name, methods in self.class_methods.items():
            if method_name in methods:
                return class_name
        return None

    def generate_call_graph_summary(self):
        """Generate a formatted call graph summary"""
        summary = []
        for class_name, methods in self.call_graph.items():
            file_path = self.class_files.get(class_name, "unknown_file")
            summary.append(f"{file_path}")
            summary.append(f"class {class_name}:")
            for method_name, method_info in methods.items():
                signature = method_info["signature"]
                params_str = ", ".join(signature["parameters"])
                summary.append(f"│  {signature['return_type']} {method_name}({params_str}):")
                
                for target_class, target_method in method_info["calls"]:
                    if target_class in self.class_methods and target_method in self.class_methods[target_class]:
                        target_signature = self.class_methods[target_class][target_method]
                        target_params_str = ", ".join(target_signature["parameters"])
                        summary.append(f"│    └─> {target_class}.{target_method}({target_params_str}) -> {target_signature['return_type']}")
            summary.append("")
        return summary

    def save_call_graph(self, base_dir):
        """Save call graph to file"""
        summary = self.generate_call_graph_summary()
        save_folder = os.getenv("LEGACY_CODE_FOLDER")
        last_folder = os.path.basename(os.path.normpath(base_dir))
        os.makedirs(save_folder, exist_ok=True)
        with open(f"{save_folder}/{last_folder}.callgraph", "w", encoding="utf-8") as f:
            f.write("\n".join(summary))
        total_tokens = CodeCompress().analyze_tokens(f"{save_folder}/{last_folder}.callgraph")
        print(f"📊 Call graph generated for {last_folder} with total tokens {total_tokens}")

    def analyze_flows(self, base_dir):
        """Analyze and save all code flows in graph format"""
        analyzer = CodeFlowAnalyzer(self.call_graph, self.class_methods, self.class_files)
        return analyzer.save_graph_flows(base_dir)

class CodeFlowAnalyzer:
    def __init__(self, call_graph, class_methods, class_files):
        self.call_graph = call_graph
        self.class_methods = class_methods
        self.class_files = class_files
        self.flows = []
        self.visited = set()
        # Graph characters
        self.VERTICAL = "│"
        self.HORIZONTAL = "──"
        self.BRANCH = "├"
        self.END = "└"
        self.ARROW = "→"

    def collect_flows(self):
        """Collect all possible flows through the codebase"""
        self.flows = []
        self.visited = set()

        for class_name, methods in self.call_graph.items():
            for method_name in methods:
                current_path = []
                self.visited = set()
                self.trace_flow(class_name, method_name, current_path)

        return self.flows

    def trace_flow(self, class_name, method_name, current_path):
        """Recursively trace method calls to build flows"""
        method_id = f"{class_name}.{method_name}"
        
        if method_id in self.visited:
            if current_path:
                self.flows.append(current_path[:])
            return
        
        self.visited.add(method_id)
        
        method_info = self.call_graph[class_name][method_name]
        signature = method_info["signature"]
        current_path.append({
            "class": class_name,
            "method": method_name,
            "signature": signature,
            "file": self.class_files.get(class_name, "unknown")
        })

        if not method_info["calls"]:
            self.flows.append(current_path[:])
        else:
            for target_class, target_method in method_info["calls"]:
                if target_class in self.call_graph and target_method in self.call_graph[target_class]:
                    self.trace_flow(target_class, target_method, current_path)

        current_path.pop()
        self.visited.remove(method_id)

    def format_method_signature(self, step):
        """Format a single method signature in the flow"""
        sig = step["signature"]
        params_str = ", ".join(sig["parameters"])
        return f"{sig['return_type']}:{step['class']}.{step['method']}({params_str})"

    def generate_graph_representation(self):
        """Generate a text-based graph representation of all flows"""
        flows = self.collect_flows()  # Changed from find_all_flows to collect_flows
        
        # Create a tree structure
        tree = {}
        for flow in flows:
            current = tree
            for step in flow:
                step_str = self.format_method_signature(step)
                if step_str not in current:
                    current[step_str] = {}
                current = current[step_str]

        # Generate the graph lines
        graph_lines = []
        self._generate_graph_lines(tree, "", "", graph_lines, True)
        return graph_lines

    def _generate_graph_lines(self, tree, prefix, current_node, lines, is_last=False):
        """Recursively generate graph lines"""
        if current_node:  # Skip for root
            lines.append(f"{prefix}{current_node}")

        items = list(tree.items())
        if not items:
            return

        for i, (node, subtree) in enumerate(items):
            is_last_item = i == len(items) - 1
            
            if current_node:  # Not root
                new_prefix = prefix.replace(self.BRANCH, self.VERTICAL).replace(self.END, " ")
                new_prefix = new_prefix + "    "
            else:  # Root
                new_prefix = ""

            if is_last_item:
                current_prefix = f"{new_prefix}{self.END}{self.HORIZONTAL}{self.ARROW} "
            else:
                current_prefix = f"{new_prefix}{self.BRANCH}{self.HORIZONTAL}{self.ARROW} "

            self._generate_graph_lines(subtree, current_prefix, node, lines, is_last_item)

    def save_graph_flows(self, base_dir):
        """Save flows in graph format"""
        graph_lines = self.generate_graph_representation()
        
        save_folder = os.getenv("LEGACY_CODE_FOLDER")
        last_folder = os.path.basename(os.path.normpath(base_dir))
        
        if save_folder:
            os.makedirs(save_folder, exist_ok=True)
            flow_file = os.path.join(save_folder, f"{last_folder}.flowgraph")
            
            header = [
                "=== Code Flow Graph ===",
                "Legend:",
                "├──→ Branch point",
                "└──→ End point",
                "│    Continuation",
                "",
                "=== Flows ===",
                ""
            ]
            
            with open(flow_file, "w", encoding="utf-8") as f:
                f.write("\n".join(header))
                f.write("\n".join(graph_lines))
            
            try:
                total_tokens = CodeCompress().analyze_tokens(flow_file)
                print(f"📊 Flow graph generated for {last_folder} with total tokens {total_tokens}")
            except:
                print(f"📊 Flow graph generated for {last_folder}")
            
            return flow_file
        return None

class CodeIndex:

    def __init__(self):
        pass

    def index(self, location: str):
        CodeCompress().compress(location)
        call_graph_builder = CallGraphBuilder()
        call_graph_builder.build_call_graph(location)
        call_graph_builder.analyze_flows(location)
