import hashlib
import os
import json
import time
import datetime
import logging
from typing import List, Dict, Set, Optional, Tuple
from dotenv import load_dotenv
from colorama import Fore, Style, init
# from utils.kg import Kg  # No longer needed here - moved to kg.py

# Import the MerkleStore
from .merkle_store import MerkleStore

# Initialize colorama
init()

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("MerkleIndexer")

class MerkleIndexer:
    def __init__(self, project_name: str):
        """
        Initialize the Merkle DAG indexer for a project.
        
        Args:
            project_name: Name of the project to index
        """
        # Get working folder from environment variable
        self.working_folder = os.getenv("WORKING_FOLDER", "./generated")
        logger.info(f"{Fore.BLUE}Using working folder: {self.working_folder}{Style.RESET_ALL}")
        
        self.project_name = project_name
        logger.info(f"{Fore.BLUE}Initializing Merkle indexer for project: {project_name}{Style.RESET_ALL}")
        
        # Set the base directory for the project
        self.base_dir = os.path.join(self.working_folder, project_name)
        logger.info(f"{Fore.BLUE}Project base directory: {self.base_dir}{Style.RESET_ALL}")
        
        # Verify the base directory exists
        if not os.path.exists(self.base_dir):
            logger.warning(f"{Fore.YELLOW}Warning: Base directory {self.base_dir} does not exist{Style.RESET_ALL}")
        
      
            
        # Initialize MerkleStore for LanceDB operations
        self.store = MerkleStore(project_name)
        

        
       
        
   
    
    def get_previous_dag(self):
        """Get the previous DAG data if available."""
        logger.info(f"{Fore.BLUE}Retrieving previous DAG information from LanceDB{Style.RESET_ALL}")
        
        # Get previous DAG from LanceDB
        previous_dag_info = self.store.get_previous_dag()
        
        # Check if previous_dag_info is None
        if previous_dag_info is None:
            logger.info(f"{Fore.YELLOW}No previous DAG found in LanceDB{Style.RESET_ALL}")
            return None
        
        logger.info(f"{Fore.GREEN}Previous DAG loaded successfully with {(previous_dag_info['root_hash'])} {Style.RESET_ALL}")
        return previous_dag_info
    
    
    
    def build_initial_dag(self):
        """Build the initial Merkle DAG for the entire codebase."""
        logger.info(f"{Fore.BLUE}Building initial Merkle DAG for {self.base_dir}...{Style.RESET_ALL}")
        
        if not os.path.exists(self.base_dir):
            logger.error(f"{Fore.RED}Error: Base directory {self.base_dir} does not exist{Style.RESET_ALL}")
            return None, None
        
        start_time = time.time()
        
        # Build the DAG
        root_hash, dag_data = self._build_dag_recursive(self.base_dir, self.base_dir)
        
        if root_hash is None:
            logger.error(f"{Fore.RED}Error: Failed to build DAG. No files found or error occurred.{Style.RESET_ALL}")
            return None, None
        
        # Save to LanceDB
        self.store.save_dag( root_hash, dag_data)
        
        end_time = time.time()
        logger.info(f"{Fore.GREEN}Initial Merkle DAG built in {end_time - start_time:.2f} seconds{Style.RESET_ALL}")
        logger.info(f"{Fore.GREEN}Initial Merkle Root: {root_hash}{Style.RESET_ALL}")
     
        
        return root_hash, None  # No changes for initial DAG
    
    def update_dag(self):
        """
        Update the Merkle DAG and identify changed files.
        Returns the new root hash and a dictionary of changed files by type.
        """
        logger.info(f"{Fore.BLUE}Updating Merkle DAG for {self.project_name}...{Style.RESET_ALL}")
        
        if not os.path.exists(self.base_dir):
            logger.error(f"{Fore.RED}Error: Base directory {self.base_dir} does not exist{Style.RESET_ALL}")
            return None, None
        
        # Get the previous DAG for comparison
        previous_dag = self.get_previous_dag()
        
        if not previous_dag:
            logger.info(f"{Fore.YELLOW}No previous DAG found. This is effectively an initial build.{Style.RESET_ALL}")
            return self.build_initial_dag()
        
        start_time = time.time()
        
       
        latest_root_hash,latest_dag_data = self._build_dag_recursive(self.base_dir, self.base_dir)
        
        print(f"Latest root hash: {latest_root_hash}")
        
        if latest_root_hash is None:
            logger.error(f"{Fore.RED}Error: Failed to build updated DAG. No files found or error occurred.{Style.RESET_ALL}")
            return None, None
        
        # Find changed files by comparing DAGs
        logger.info(f"{Fore.BLUE}Identifying changes between DAGs...{Style.RESET_ALL}")
        changes = self._identify_changes(previous_dag["dag_data"],latest_dag_data)
        
        # Save to LanceDB
        self.store.save_dag(latest_root_hash, latest_dag_data)
        
        end_time = time.time()
        logger.info(f"{Fore.GREEN}DAG updated in {end_time - start_time:.2f} seconds{Style.RESET_ALL}")
        logger.info(f"{Fore.GREEN}New Merkle Root: {latest_root_hash}{Style.RESET_ALL}")
     
        
        # Log changes
        print(f"{Fore.GREEN}Changes detected:{Style.RESET_ALL}")
        print(f"{Fore.GREEN}- Added: {len(changes['added'])} files{Style.RESET_ALL}")
        print(f"{Fore.GREEN}- Modified: {len(changes['modified'])} files{Style.RESET_ALL}")
        print(f"{Fore.GREEN}- Deleted: {len(changes['deleted'])} files{Style.RESET_ALL}")
        
        return latest_root_hash, changes
    
    def _build_dag_recursive(self, path: str, base_dir: str) -> Tuple[Optional[str], Dict[str, dict]]:
        """Recursively build the DAG for a path and return the merkle_dag and root hash."""
        merkle_dag = {}

        def recurse(current_path: str) -> Optional[str]:
            if not os.path.exists(current_path):
                logger.error(f"{Fore.RED}Path does not exist: {current_path}{Style.RESET_ALL}")
                return None

            relative_path = os.path.relpath(current_path, base_dir)

            if os.path.isfile(current_path):
                try:
                    file_hash = self.hash_file(current_path)
                    merkle_dag[file_hash] = {
                        "type": "file",
                        "path": relative_path,
                        "hash": file_hash,
                    }
                    return file_hash
                except Exception as e:
                    logger.error(f"{Fore.RED}Error hashing file {current_path}: {str(e)}{Style.RESET_ALL}")
                    return None

            elif os.path.isdir(current_path):
                try:
                    child_hashes = []
                    for name in sorted(os.listdir(current_path)):
                        if name.startswith('.'):
                            continue
                        child_path = os.path.join(current_path, name)
                        child_hash = recurse(child_path)
                        if child_hash:
                            child_hashes.append(child_hash)

                    if not child_hashes:
                        dir_hash = self.hash_directory([])
                        merkle_dag[dir_hash] = {
                            "type": "dir",
                            "path": relative_path,
                            "hash": dir_hash,
                            "children": []
                        }
                        return dir_hash

                    dir_hash = self.hash_directory(child_hashes)
                    merkle_dag[dir_hash] = {
                        "type": "dir",
                        "path": relative_path,
                        "hash": dir_hash,
                        "children": child_hashes
                    }
                    return dir_hash
                except Exception as e:
                    logger.error(f"{Fore.RED}Error processing directory {current_path}: {str(e)}{Style.RESET_ALL}")
                    return None

            return None

        root_hash = recurse(path)
        return root_hash, merkle_dag
    
    def hash_file(self, filepath: str) -> str:
        """Generate a hash for a file's contents."""
        with open(filepath, 'rb') as f:
            file_hash = hashlib.sha256(f.read()).hexdigest()
            logger.debug(f"Hashed file: {filepath} -> {file_hash[:8]}...")
            return file_hash
            
    def hash_directory(self, child_hashes: List[str]) -> str:
        """Generate a hash for a directory based on its children's hashes."""
        combined = ''.join(sorted(child_hashes)).encode()
        dir_hash = hashlib.sha256(combined).hexdigest()
        return dir_hash
    
    def _identify_changes(self, old_dag: Dict, latest_dag: Dict) -> Dict[str, List[str]]:
        """
        Compare old and new DAGs to identify changed files and directories.
        Returns a dictionary with lists of added, modified, deleted files and directories.
        """
        logger.info(f"{Fore.BLUE}Comparing DAGs to identify changes...{Style.RESET_ALL}")
        
        changes = {
            "added": [],
            "modified": [],
            "deleted": [],
            "added_dirs": [],
            "deleted_dirs": [],
            "renamed_dirs": []  # Will store tuples of (old_path, new_path)
        }
        
        # Create lookup dictionaries for faster access
        old_files = {}
        new_files = {}
        old_dirs = {}
        new_dirs = {}
        
        # Build lookup for old DAG
        for hash_key, node in old_dag.items():
            if node.get("type") == "file":
                old_files[node.get("path")] = hash_key
            elif node.get("type") == "dir":
                old_dirs[node.get("path")] = {
                    "hash": hash_key,
                    "children": node.get("children", [])
                }
        
        # Build lookup for new DAG
        for hash_key, node in latest_dag.items():
            if node.get("type") == "file":
                new_files[node.get("path")] = hash_key
            elif node.get("type") == "dir":
                new_dirs[node.get("path")] = {
                    "hash": hash_key,
                    "children": node.get("children", [])
                }
        
        # Find new and modified files
        for file_path, hash_key in new_files.items():
            if file_path not in old_files:
                # New file
                changes["added"].append(os.path.join(self.base_dir, file_path))
                logger.debug(f"Added file: {file_path}")
            elif old_files[file_path] != hash_key:
                # Modified file (hash changed)
                changes["modified"].append(os.path.join(self.base_dir, file_path))
                logger.debug(f"Modified file: {file_path}")
        
        # Find deleted files
        for file_path in old_files:
            if file_path not in new_files:
                changes["deleted"].append(os.path.join(self.base_dir, file_path))
                logger.debug(f"Deleted file: {file_path}")
        
        # Find new directories
        for dir_path in new_dirs:
            if dir_path not in old_dirs:
                changes["added_dirs"].append(os.path.join(self.base_dir, dir_path))
                logger.debug(f"Added directory: {dir_path}")
        
        # Find deleted directories
        for dir_path in old_dirs:
            if dir_path not in new_dirs:
                changes["deleted_dirs"].append(os.path.join(self.base_dir, dir_path))
                logger.debug(f"Deleted directory: {dir_path}")
        
        # Try to detect renamed directories by comparing content hashes
        # This is a heuristic and may not catch all renames
        deleted_dir_hashes = {old_dirs[path]["hash"]: path for path in old_dirs if path not in new_dirs}
        added_dir_hashes = {new_dirs[path]["hash"]: path for path in new_dirs if path not in old_dirs}
        
        # If a directory has the same hash in both deleted and added, it was likely renamed
        for hash_key, old_path in deleted_dir_hashes.items():
            if hash_key in added_dir_hashes:
                new_path = added_dir_hashes[hash_key]
                changes["renamed_dirs"].append((
                    os.path.join(self.base_dir, old_path),
                    os.path.join(self.base_dir, new_path)
                ))
                logger.debug(f"Renamed directory: {old_path} -> {new_path}")
                
                # Remove from deleted and added lists since we've identified it as a rename
                if os.path.join(self.base_dir, old_path) in changes["deleted_dirs"]:
                    changes["deleted_dirs"].remove(os.path.join(self.base_dir, old_path))
                if os.path.join(self.base_dir, new_path) in changes["added_dirs"]:
                    changes["added_dirs"].remove(os.path.join(self.base_dir, new_path))
        
        return changes
    
    def is_initial_indexing(self):
        """Check if this is the initial indexing (no previous DAG)."""
        current_dag = self.store.get_current_dag()
        is_initial = current_dag is None
        logger.info(f"{Fore.BLUE}Is initial indexing: {is_initial}{Style.RESET_ALL}")
        return is_initial
        
    
    
    def update_index(self, force_full: bool = False):
        """
        Update the Merkle DAG and index changed files.
        
        Args:
            force_full: Force a full rebuild even if a previous DAG exists
        """
        # Get working folder and project path
        working_folder = os.getenv("WORKING_FOLDER", "./generated")
        project_path = os.path.join(working_folder, self.project_name)
        lancedb_prefix = os.getenv("WORKING_PROJECT_LANCEDB_PREFIX", "working_")
        lancedb_table_name = lancedb_prefix + self.project_name
        
        # Force full rebuild if requested
        if force_full:
            logger.info(f"{Fore.YELLOW}Forced full rebuild requested{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}:hourglass_not_done: Forced full rebuild...{Style.RESET_ALL}")
            
            root_hash, _ = self.build_initial_dag()
            if root_hash:
                try:
                    # Import here to avoid circular imports
                    from utils.kg import Kg
                    Kg().index_codebase(self.project_name, project_path)
                    print(f"{Fore.GREEN}:hourglass_done: Full rebuild completed. Root hash: {root_hash[:8]}...{Style.RESET_ALL}")
                    return
                except Exception as e:
                    logger.error(f"{Fore.RED}Error during full indexing: {str(e)}{Style.RESET_ALL}")
                    print(f"{Fore.RED}:x: Error during full indexing: {str(e)}{Style.RESET_ALL}")
                    return
            else:
                print(f"{Fore.RED}:x: Failed to build initial DAG{Style.RESET_ALL}")
                return
        
        # Update DAG and get changes - this handles initial indexing internally
        logger.info(f"{Fore.YELLOW}Updating DAG and checking for changes{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}:hourglass_not_done: Updating DAG and checking for changes...{Style.RESET_ALL}")
        
        new_root_hash, changes = self.update_dag()
        
        # If no changes object returned, indexer already handled initial indexing
        if not changes:
            print(f"{Fore.GREEN}:white_check_mark: No changes detected or initial indexing completed.{Style.RESET_ALL}")
            return
        
        # Process changes
        added = changes.get("added", [])
        modified = changes.get("modified", [])
        deleted = changes.get("deleted", [])
        
      
        # Import here to avoid circular imports
        from utils.kg import Kg
        kg = Kg()
        
        # Update index for changed files
        try:
            # Index added and modified files
            for file_path in added + modified:
                kg.index_file(lancedb_table_name, file_path)
            
            # Remove deleted files
            for file_path in deleted:
                kg.remove_file(lancedb_table_name, file_path)
            
            print(f"{Fore.GREEN}:hourglass_done: Index updated. Root hash: {new_root_hash[:8]}...{Style.RESET_ALL}")
        except Exception as e:
            logger.error(f"{Fore.RED}Error updating index: {str(e)}{Style.RESET_ALL}")
            print(f"{Fore.RED}:x: Error updating index: {str(e)}{Style.RESET_ALL}")

    # MOVED: index_file_from_client_content method has been moved to kg.py
    # for better organization and proper embedding generation

    # MOVED: _remove_file_chunks_static method has been moved to kg.py
    # Use kg._remove_file_chunks() instead
