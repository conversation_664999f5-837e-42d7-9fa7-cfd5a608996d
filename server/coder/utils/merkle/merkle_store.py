import os
import time
import json
import datetime
import logging
import numpy as np
import lancedb
import pyarrow as pa
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
import pandas as pd


# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("MerkleStore")

class MerkleStore:
    """
    A class to handle storage and retrieval of Merkle DAG data using LanceDB.
    Uses timestamp-based queries instead of is_current flag for cleaner logic.
    """
    
    def __init__(self, project_name: str):
        """
        Initialize the MerkleStore for a project.
        
        Args:
            project_name: Name of the project
        """
        self.project_name = project_name
        
        # Get LanceDB location from environment variable
        lancedb_location = os.getenv("LANCEDB_LOCATION", "./db")
        self.db_path = lancedb_location
        self.table_name = "merkledags"
        
        #logger.info(f"Initializing MerkleStore with LanceDB at: {self.db_path}")
        
        # Initialize LanceDB connection and table
        self._init_lancedb_table()
        
    def _init_lancedb_table(self):
        """Initialize LanceDB table for storing Merkle DAG history"""
        #logger.info(f"Initializing LanceDB table for Merkle DAGs: {self.table_name}")
        
        # Ensure db directory exists
        os.makedirs(self.db_path, exist_ok=True)
        
        # Connect to LanceDB
        db = lancedb.connect(self.db_path)
        
        # Check if table exists
        if self.table_name in db.table_names():
            #logger.info(f"LanceDB table {self.table_name} already exists")
            return
        
        # Create schema for the table - removed is_current field
        records = [{
            "id": "initial",
            "project_name": self.project_name,
            "timestamp": time.time(),
          
            "root_hash": "initial",
            "dag_data": "{}"
        }]
        
        # Build arrow table
        ids = [r["id"] for r in records]
        project_names = [r["project_name"] for r in records]
        timestamps = [r["timestamp"] for r in records]
       
        root_hashes = [r["root_hash"] for r in records]
        dag_datas = [r["dag_data"] for r in records]
        
        table = pa.table({
            "id": pa.array(ids, pa.string()),
            "project_name": pa.array(project_names, pa.string()),
            "timestamp": pa.array(timestamps, pa.float64()),
          
            "root_hash": pa.array(root_hashes, pa.string()),
            "dag_data": pa.array(dag_datas, pa.string())
        })
        
        # Create the table
        db.create_table(self.table_name, data=table)
        logger.info(f"LanceDB table {self.table_name} created successfully")
    
    def save_dag(self, root_hash: str, dag_data: dict) -> str:
        """
        Save Merkle DAG information to LanceDB unless the root_hash matches the latest stored root_hash.

        Args:
            root_hash: Root hash of the DAG
            dag_data: The DAG data dictionary

        Returns:
            The ID of the saved DAG entry, or None if skipped due to duplicate root_hash.
        """
        logger.info(f"Saving Merkle DAG information to LanceDB for project: {self.project_name}")

        # Connect to LanceDB
        db = lancedb.connect(self.db_path)

        # Open the table
        table = db.open_table(self.table_name)

        # Retrieve the latest DAG info for the project
        latest_records = self.get_latest_dag_info(table, self.project_name, limit=1)

        if not latest_records.empty:
            latest_root_hash = latest_records.iloc[0]['root_hash']
            if latest_root_hash == root_hash:
                logger.info(f"Skipping save: root_hash '{root_hash}' is the same as the latest stored root_hash.")
                return None  # Skip saving as it's duplicate

        # Proceed with saving since root_hash is new
        timestamp = time.time()
        timestamp_str = datetime.datetime.fromtimestamp(timestamp).strftime("%Y%m%d_%H%M%S_%f")[:-3]  # milliseconds precision
        dag_id = f"{self.project_name}_dag_{timestamp_str}_{root_hash[:8]}"

        if isinstance(dag_data, dict):
            dag_data_str = json.dumps(dag_data)
        else:
            logger.warning("dag_data is not a dictionary.")
            dag_data_str = str(dag_data)

        table.add([{
            "id": dag_id,
            "project_name": self.project_name,
            "timestamp": timestamp,
            "root_hash": root_hash,
            "dag_data": dag_data_str
        }])

        logger.info(f"Merkle DAG information saved with root_hash: {root_hash}")

        # Clean up old DAGs
        self.cleanup_old_dags(max_history_per_project=20, max_age_days=60)

        return dag_id
    
    
    def get_latest_dag_info(self, table, project_name: str, limit: int = 2) -> Any:
        """
        Return the latest N records for a given project, sorted by timestamp descending.

        Args:
            table: The LanceDB table object.
            project_name: Name of the project to filter by.
            limit: Number of most recent records to return.

        Returns:
            A pandas DataFrame of the top N records sorted by timestamp descending.
        """
        try:
            # Step 1: Get all rows for the project
            results = table.search().where(f"project_name = '{project_name}'").to_pandas()

            # Step 2: Sort by timestamp descending
            if not results.empty and 'timestamp' in results.columns:
                results = results.sort_values("timestamp", ascending=False).head(limit)

            return results

        except Exception as e:
            logger.error(f"Error retrieving sorted DAGs for project '{project_name}': {e}")
            return pd.DataFrame()  # Empty result on failure
    
   
    
    def get_current_dag(self) -> Optional[Dict[str, Any]]:
        """
        Get the most recent (current) Merkle DAG information from LanceDB
        
        Returns:
            Dictionary with current DAG information or None if not found
        """
       
        
        # Connect to LanceDB
        db = lancedb.connect(self.db_path)
        
        # Open the table
        table = db.open_table(self.table_name)
        
       
        results = self.get_latest_dag_info(table, self.project_name, limit=1)
        
        if len(results) == 0:
            logger.info(f"No Merkle DAG information found for project: {self.project_name}")
            return None
        
        current_dag = results.iloc[0]
        
        # Parse the DAG data from JSON string
        try:
            dag_data = json.loads(current_dag["dag_data"])
        except Exception as e:
            logger.error(f"Error parsing current DAG data: {str(e)}")
            dag_data = {}
        
        return {
            "id": current_dag["id"],
            "project_name": current_dag["project_name"],
           
            "root_hash": current_dag["root_hash"],
            "timestamp": current_dag["timestamp"],
            "dag_data": dag_data
        }
    
    def get_previous_dag(self) -> Optional[Dict[str, Any]]:
        """
        Get the second most recent Merkle DAG data (previous version).
        
        Returns:
            Dictionary with previous DAG information or None if not found
        """
        logger.info(f"Retrieving previous Merkle DAG information for project: {self.project_name}")
        
        # Connect to LanceDB
        db = lancedb.connect(self.db_path)
        
        # Open the table
        table = db.open_table(self.table_name)
        
        # Get the two most recent DAGs for this project
       
        results = self.get_latest_dag_info(table, self.project_name, limit=2)
        
        if len(results) < 2:
            logger.info(f"No previous Merkle DAG found for project: {self.project_name} (only {len(results)} DAG(s) exist)")
            return None
        
        # The second row is the previous DAG
        previous_dag = results.iloc[0]
       
        print(f" Previous RootHash  {previous_dag['root_hash']}")
       
        
        # Parse the DAG data from JSON string
        try:
            dag_data = json.loads(previous_dag["dag_data"])
        except Exception as e:
            logger.error(f"Error parsing previous DAG data: {str(e)}")
            return None
        
        return {
            "id": previous_dag["id"],
            "project_name": previous_dag["project_name"],
          
            "root_hash": previous_dag["root_hash"],
            "timestamp": previous_dag["timestamp"],
            "dag_data": dag_data
        }
    
    def get_dag_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get the history of Merkle DAGs for this project.
        
        Args:
            limit: Maximum number of DAGs to return (most recent first)
            
        Returns:
            List of DAG information dictionaries ordered by timestamp (newest first)
        """
        logger.info(f"Retrieving DAG history for project: {self.project_name} (limit: {limit})")
        
        # Connect to LanceDB
        db = lancedb.connect(self.db_path)
        
        # Open the table
        table = db.open_table(self.table_name)
        
        # Get DAG history sorted by timestamp (newest first)
       
        results = self.get_latest_dag_info(table, self.project_name, limit=limit)
        
        if len(results) == 0:
            logger.info(f"No DAG history found for project: {self.project_name}")
            return []
        
        history = []
        for _, row in results.iterrows():
            try:
                dag_data = json.loads(row["dag_data"])
            except Exception as e:
                logger.error(f"Error parsing DAG data for {row['id']}: {str(e)}")
                continue
                
            history.append({
                "id": row["id"],
                "project_name": row["project_name"],
             
                "root_hash": row["root_hash"],
                "timestamp": row["timestamp"],
                "created_at": datetime.datetime.fromtimestamp(row["timestamp"]).strftime("%Y-%m-%d %H:%M:%S"),
                "dag_data": dag_data
            })
        
        logger.info(f"Retrieved {len(history)} DAG entries from history")
        return history
    
    def get_dag_at_timestamp(self, target_timestamp: float) -> Optional[Dict[str, Any]]:
        """
        Get the DAG that was current at or before a specific timestamp.
        
        Args:
            target_timestamp: Unix timestamp to search for
            
        Returns:
            Dictionary with DAG information or None if not found
        """
        logger.info(f"Retrieving DAG at timestamp {target_timestamp} for project: {self.project_name}")
        
        # Connect to LanceDB
        db = lancedb.connect(self.db_path)
        
        # Open the table
        table = db.open_table(self.table_name)
        
        try:
            # Get all records for this project and filter by timestamp in Python
            all_results = table.search().where(f"project_name = '{self.project_name}'").to_pandas()
            
            if len(all_results) == 0:
                logger.info(f"No DAGs found for project: {self.project_name}")
                return None
            
            # Filter by timestamp and get the most recent one at or before target
            filtered_results = all_results[all_results['timestamp'] <= target_timestamp]
            
            if len(filtered_results) == 0:
                logger.info(f"No DAG found at or before timestamp {target_timestamp} for project: {self.project_name}")
                return None
            
            # Sort by timestamp descending and get the first (most recent)
            sorted_results = filtered_results.sort_values('timestamp', ascending=False)
            dag_row = sorted_results.iloc[0]
            
        except Exception as e:
            logger.error(f"Error querying DAG at timestamp: {e}")
            return None
        
        # Parse the DAG data from JSON string
        try:
            dag_data = json.loads(dag_row["dag_data"])
        except Exception as e:
            logger.error(f"Error parsing DAG data: {str(e)}")
            return None
        
        return {
            "id": dag_row["id"],
            "project_name": dag_row["project_name"],
          
            "root_hash": dag_row["root_hash"],
            "timestamp": dag_row["timestamp"],
            "dag_data": dag_data
        }
    
    def cleanup_old_dags(self, max_history_per_project: int = 5, max_age_days: int = 7):
        """
        Remove old DAG entries using multiple strategies:
        1. Keep only the specified number of most recent entries per project
        2. Remove entries older than max_age_days
        3. Always keep at least 2 entries (current + previous for delta analysis)

        Args:
            max_history_per_project: Maximum number of historical DAGs to keep per project
            max_age_days: Maximum age in days for DAG entries
        """
        logger.info(f"Cleaning up old Merkle DAG entries for project: {self.project_name}")
        logger.info(f"Cleanup policy: max_history={max_history_per_project}, max_age_days={max_age_days}")

        # Connect to LanceDB
        db = lancedb.connect(self.db_path)

        # Open the table
        table = db.open_table(self.table_name)

        # Get all DAGs for this project, sorted by timestamp (newest first)
        try:
            all_results = table.search().where(f"project_name = '{self.project_name}'").to_pandas()
            if len(all_results) == 0:
                logger.info("No DAGs found for cleanup")
                return

            results = all_results.sort_values('timestamp', ascending=False)
        except Exception as e:
            logger.error(f"Error retrieving DAGs for cleanup: {e}")
            return

        if len(results) <= 2:
            logger.info(f"Only {len(results)} DAGs exist. Keeping all for delta analysis.")
            return

        current_time = time.time()
        cutoff_time = current_time - (max_age_days * 24 * 60 * 60)  # Convert days to seconds

        dags_to_delete = []
        kept_count = 0

        for idx, row in results.iterrows():
            # Always keep the first 2 (most recent) for delta analysis
            if kept_count < 2:
                kept_count += 1
                logger.debug(f"Keeping DAG {row['id']} (recent entry #{kept_count})")
                continue

            # Apply cleanup rules for remaining entries
            should_delete = False
            reason = ""

            # Rule 1: Exceed max history count
            if kept_count >= max_history_per_project:
                should_delete = True
                reason = f"exceeds max_history ({max_history_per_project})"

            # Rule 2: Too old (age-based cleanup)
            elif row['timestamp'] < cutoff_time:
                should_delete = True
                age_days = (current_time - row['timestamp']) / (24 * 60 * 60)
                reason = f"too old ({age_days:.1f} days > {max_age_days} days)"

            if should_delete:
                dags_to_delete.append((row['id'], reason))
                logger.debug(f"Marking DAG {row['id']} for deletion: {reason}")
            else:
                kept_count += 1
                logger.debug(f"Keeping DAG {row['id']} (within limits)")

        # Perform deletions
        if dags_to_delete:
            logger.info(f"Deleting {len(dags_to_delete)} old DAG entries...")
            for dag_id, reason in dags_to_delete:
                try:
                    table.delete(f"id = '{dag_id}'")  # ✅ Correct usage
                    logger.info(f"Deleted DAG {dag_id}: {reason}")
                except Exception as e:
                    logger.error(f"Failed to delete DAG {dag_id}: {str(e)}")

            logger.info(f"Cleanup completed. Kept {kept_count} DAGs, deleted {len(dags_to_delete)} DAGs")
        else:
            logger.info(f"No DAGs need cleanup. Keeping all {kept_count} DAGs")

        # Log storage stats
        try:
            remaining_results = table.search().where(f"project_name = '{self.project_name}'").to_pandas()
            logger.info(f"Project {self.project_name} now has {len(remaining_results)} DAG entries in storage")
        except Exception as e:
            logger.warning(f"Could not retrieve storage stats: {e}")

    
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics for this project's DAGs.
        
        Returns:
            Dictionary with storage statistics
        """
        # Connect to LanceDB
        db = lancedb.connect(self.db_path)
        
        # Open the table
        table = db.open_table(self.table_name)
        
        try:
            # Get all DAGs for this project
            results = table.search().where(f"project_name = '{self.project_name}'").to_pandas()
        except Exception as e:
            logger.error(f"Error retrieving storage stats: {e}")
            return {
                "project_name": self.project_name,
                "total_dags": 0,
                "oldest_dag": None,
                "newest_dag": None,
                "storage_span_days": 0,
                "error": str(e)
            }
        
        if len(results) == 0:
            return {
                "project_name": self.project_name,
                "total_dags": 0,
                "oldest_dag": None,
                "newest_dag": None,
                "storage_span_days": 0
            }
        
        oldest_timestamp = results['timestamp'].min()
        newest_timestamp = results['timestamp'].max()
        span_days = (newest_timestamp - oldest_timestamp) / (24 * 60 * 60)
        
        return {
            "project_name": self.project_name,
            "total_dags": len(results),
            "oldest_dag": datetime.datetime.fromtimestamp(oldest_timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "newest_dag": datetime.datetime.fromtimestamp(newest_timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "storage_span_days": round(span_days, 2)
        }
    
    def force_cleanup(self, keep_only_latest: bool = False):
        """
        Force aggressive cleanup of DAGs.
        
        Args:
            keep_only_latest: If True, keep only the most recent DAG (not recommended for delta analysis)
        """
        logger.warning(f"Force cleanup requested for project: {self.project_name}")
        
        if keep_only_latest:
            logger.warning("Keeping only latest DAG - delta analysis will not be possible!")
            self.cleanup_old_dags(max_history_per_project=1, max_age_days=0)
        else:
            logger.info("Aggressive cleanup - keeping only 2 most recent DAGs")
            self.cleanup_old_dags(max_history_per_project=2, max_age_days=1)