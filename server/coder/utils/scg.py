import os
import uuid

class Scg:

    def build_scg(self, location: str):
        folder_name = str(uuid.uuid4())
        os.system(f'./lib/scg-cli/bin/scg-cli generate {location}')
        os.system(f'./lib/scg-cli/bin/scg-cli summary -o html {location}')
        os.system(f'mkdir ./reports/scg/{folder_name}')
        os.system(f'mv summary.* ./reports/scg/{folder_name}')  
        return folder_name
