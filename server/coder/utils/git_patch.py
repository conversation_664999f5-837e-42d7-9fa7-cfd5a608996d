import difflib
import datetime
import hashlib
import os

from rich import print

class GitPatch:

    def generate_git_patch_header(self, file_path, original_lines, modified_lines):
        old_hash = hashlib.sha1("".join(original_lines).encode()).hexdigest()[:7] if original_lines else "0000000"
        new_hash = hashlib.sha1("".join(modified_lines).encode()).hexdigest()[:7] if modified_lines else "0000000"

        return [
            f"diff --git .{file_path} .{file_path}",
            f"index {old_hash}..{new_hash} 100644",
            f"--- {'.' + file_path if original_lines else '/dev/null'}",
            f"+++ {'.' + file_path if modified_lines else '/dev/null'}"
        ]

    def generate_patch(self, data):
        operations = data["params"]["operations"]
        diff_output = []

        for op in operations:
            file_path = op["file"]
            action = op["action"]
            replacement_lines = op.get("replacement", "").splitlines(keepends=False)

            original_lines = []
            modified_lines = []

            if action == "edit":
                if not os.path.exists(file_path):
                    print(f"File {file_path} not found for editing.")
                    continue

                with open(file_path, 'r') as f:
                    original_lines = f.readlines()

                start_line = op["start_line"] - 1
                end_line = op["end_line"]

                modified_lines = (
                    original_lines[:start_line] +
                    replacement_lines +
                    original_lines[end_line:]

                )

            elif action == "create":
                original_lines = []
                modified_lines = replacement_lines

            elif action == "delete":
                if not os.path.exists(file_path):
                    print(f"File {file_path} not found for deletion.")
                    continue

                with open(file_path, 'r') as f:
                    original_lines = f.readlines()

                modified_lines = []

            else:
                print(f"Unknown action '{action}' for file {file_path}")
                continue

            header = self.generate_git_patch_header(file_path.replace(os.getenv("WORKING_FOLDER"), ""), original_lines, modified_lines)
            diff_body = list(difflib.unified_diff(
                original_lines,
                modified_lines,
                fromfile=f".{file_path}",
                tofile=f".{file_path}",
                lineterm=''
            ))[2:]  # Skip --- +++ from difflib

            diff_output.extend(header + diff_body + [""])

        # Save patch file
        timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
        patch_location = os.getenv("MODERN_CODE_FODLER")
        patch_file = f"{patch_location}/{timestamp}-changes.patch"
        os.makedirs(os.path.dirname(patch_file), exist_ok=True)

        with open(patch_file, "w") as f:
            f.writelines(line if line.endswith('\n') else line + '\n' for line in diff_output)

        print(f"✅ Patch file created at {patch_file}")
