import os
import re
import json

class PromptTemplate:

    def coder_prompt(self, question, project_name):
        working_dir = "."

       

        with open("./prompts/execute/coder_prompt.txt", 'r') as file:
            prompt = file.read()

        prompt = prompt.replace("###USER_QUERY###", question)
        prompt = prompt.replace("###WORKING_FOLDER###", working_dir)

        return prompt

    def extract_tag_content(self, text, tag):
        pattern = fr"<{tag}>(.*?)</{tag}>"
        
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            return ""

    def analysis_prompt(self, content, user_query):

        with open("./prompts/plan/analysis_prompt.txt", 'r') as file:
            prompt = file.read()

        codebase_context = self.extract_tag_content(content, "all-codebases")
        documentation = self.extract_tag_content(content, "documentations")
        history_info = self.extract_tag_content(content, "histories")
        prompt = prompt.replace("###CURRENT_CODEBASE###", codebase_context)
        prompt = prompt.replace("###DOCUMENTATION###", documentation)
        prompt = prompt.replace("###HISTORY###", history_info)
        prompt = prompt.replace("###USER_QUERY###", user_query)
        return prompt

    def code_edit_prompt(self, content, user_query, project_working_folder):

       

        with open("./prompts/execute/code_edit_prompt.txt", 'r') as file:
            prompt = file.read()

        codebase_context = self.extract_tag_content(content, "all-codebases")
        documentation = self.extract_tag_content(content, "documentations")
        history_info = self.extract_tag_content(content, "histories")

       
        working_dir = "."

        prompt = prompt.replace("###CURRENT_CODEBASE###", codebase_context)
        prompt = prompt.replace("###DOCUMENTATION###", documentation)
        prompt = prompt.replace("###HISTORY###", history_info)
        prompt = prompt.replace("###USER_QUERY###", user_query)
        prompt = prompt.replace("###WORKING_FOLDER###", working_dir)
        return prompt
    
    
    def summary_prompt(self,  user_query, coder_prompt):

      with open("./prompts/execute/summary_prompt.txt", 'r') as file:
            prompt = file.read()
            
      

      history_info = self.extract_tag_content(coder_prompt, "histories")
      prompt = prompt.replace("###HISTORY###", history_info)
      prompt = prompt.replace("###USER_QUERY###", user_query)
      return prompt
  
    def request_analyzer_prompt(self,  user_query, project_name):

      with open("./prompts/plan/request_analyzer_prompt.txt", 'r') as file:
            prompt = file.read()

     
      prompt = prompt.replace("###USER_QUERY###", user_query)
      prompt = prompt.replace("###PROJECT_NAME###", project_name)
      return prompt
  
    def impact_analysis_prompt(self,  project_assessment, project_name, tasks):

      with open("./prompts/plan/impact_analysis_prompt.txt", 'r') as file:
            prompt = file.read()

     
      prompt = prompt.replace("###PROJECT_ASSESSMENT###", json.dumps(project_assessment, indent=2) if isinstance(project_assessment, dict) else str(project_assessment))
      prompt = prompt.replace("###PROJECT_NAME###", project_name)
       # Join the list into a string for the prompt
      tasks_str = "\n".join([json.dumps(task, indent=2) if isinstance(task, dict) else str(task) for task in tasks]) if isinstance(tasks, list) else str(tasks)
      prompt = prompt.replace("###TASK_LIST###", tasks_str)
      return prompt
  
    def execution_plan_prompt(self,  user_query):

      with open("./prompts/plan/execution_plan_prompt.txt", 'r') as file:
            prompt = file.read()

     
      prompt = prompt.replace("###USER_QUERY###", user_query)
      
      return prompt

    

    def action_plan_agent_prompt(self, user_query, context, subtasks):
        with open("./prompts/plan/action_plan_agent_prompt.txt", 'r') as file:
            prompt = file.read()
        prompt = prompt.replace("###USER_QUERY###", user_query)
        prompt = prompt.replace("###CONTEXT###", context)
        prompt = prompt.replace("###SUBTASKS###", subtasks)
        return prompt

    def reviewer_agent_prompt(self, user_query, context, subtasks, action_plan):
        with open("./prompts/plan/reviewer_agent_prompt.txt", 'r') as file:
            prompt = file.read()
        prompt = prompt.replace("###USER_QUERY###", user_query)
        prompt = prompt.replace("###CONTEXT###", context)
        prompt = prompt.replace("###SUBTASKS###", subtasks)
        prompt = prompt.replace("###ACTION_PLAN###", action_plan)
        return prompt


    def include_history(self, content, history):
        """
        Smartly append new history entry to existing <histories> section.
        Preserves all existing history entries and adds the new one at the top (most recent first).
        """
       

        # Create the new history entry XML
        new_history_entry = f"""<history>
  <tool>{history.get("tool", "")}</tool>
  <reason>{history.get("reason", "")}</reason>
  <params>{history.get("params", "")}</params>
  <result>{history.get("result", "")}</result>
  <data>{history.get("data", "")}</data>
</history>"""

        lines = content.splitlines()
        new_lines = []

        for line in lines:
            new_lines.append(line)

            # Look for the opening <histories> tag to insert new history immediately after it
            if "<histories>" in line:
                # Add the new history entry immediately after opening tag (most recent first)
                new_lines.append(new_history_entry)
               

        # Debug: Extract and print the complete <histories> section
        result_content = '\n'.join(new_lines) + '\n'


        return result_content

    def include_all_histories(self, content, all_histories):
        """Include all histories for summary analysis"""
        lines = content.splitlines()
        new_lines = []

        for line in lines:
            new_lines.append(line)
            if "<histories>" in line:
                # Add all histories for comprehensive analysis
                for history in all_histories:
                    new_lines.append(f"""
<history>
  <tool>{history.get("tool", "")}</tool>
  <reason>{history.get("reason", "")}</reason>
  <params>{history.get("params", "")}</params>
  <result>{history.get("result", "")}</result>
  <data>{history.get("data", "")}</data>
</history>""")

        return '\n'.join(new_lines) + '\n'

    def inject_memory_into_prompt(self, base_prompt: str, memory_context: dict) -> str:
        """Inject Cognee memory context into ###MEMORIES### section"""
        if not memory_context or "###MEMORIES###" not in base_prompt:
            return base_prompt
            
        memory_content = self._build_memory_content(memory_context)
        if not memory_content:
            return base_prompt
            
        # Replace ###MEMORIES### marker with actual memory content
        return base_prompt.replace("###MEMORIES###", memory_content)
    
    def _build_memory_content(self, memory_context: dict) -> str:
        """Build memory content for ###MEMORIES### section using same format as history"""
        if not memory_context or not memory_context.get("relevant_memories"):
            return "<memories>\n<!-- No relevant memories found -->\n</memories>"
            
        content = "<memories>\n"
        
        # Process Cognee GRAPH_COMPLETION results in XML format like history
        for memory in memory_context["relevant_memories"]:
            memory_text = memory.get("content", "")
            timestamp = memory.get("timestamp", "")
            relevance = memory.get("relevance", 1.0)
            
            if memory_text:
                # Truncate for compact display
                truncated_content = memory_text[:200] + "..." if len(memory_text) > 200 else memory_text
                content += f"<memory>\n"
                content += f"  <content>{truncated_content}</content>\n"
                content += f"  <timestamp>{timestamp}</timestamp>\n"
                content += f"  <relevance>{relevance}</relevance>\n"
                content += f"</memory>\n"
        
        # Add search context
        if memory_context.get("search_query"):
            content += f"<!-- Found {memory_context.get('memory_count', 0)} relevant memories for query: '{memory_context['search_query']}' -->\n"
        
        content += "</memories>"
        return content

    def code_review_prompt(self, user_query, code_changes, semantic_context,file_content):
        with open("./prompts/execute/code_review_prompt.txt", 'r') as file:
            prompt = file.read()
        
        prompt = prompt.replace("###USER_QUERY###", user_query)
        prompt = prompt.replace("###CODE_CHANGES###", code_changes)
        prompt = prompt.replace("###SEMANTIC_CONTEXT###", semantic_context)
        prompt = prompt.replace("###FILE_CONTENT###", file_content)
        
        return prompt
    
