"""
Utility for logging user commands/queries to history file
"""
import os
import asyncio
from datetime import datetime
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class CommandLogger:
    def __init__(self):
        # Get the directory where this script is located and build path from there
        current_dir = Path(__file__).parent.parent  # Go up to coder/ directory
        self.history_dir = current_dir / "history"
        self.history_file = self.history_dir / "commands.txt"
        self._lock = asyncio.Lock()
    
    async def log_user_query(self, client_id: str, project_name: str, question: str):
        """
        Log user query to history/commands.txt file
        
        Args:
            client_id: Client identifier
            project_name: Name of the project
            question: User's question/query
        """
        async with self._lock:
            try:
                # Ensure directory exists
                self.history_dir.mkdir(parents=True, exist_ok=True)
                
                # Create timestamp
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                # Format log entry
                log_entry = f"[{timestamp}] Client: {client_id} | Project: {project_name}\n"
                log_entry += f"Query: {question}\n"
                log_entry += "-" * 80 + "\n\n"
                
                # Append to file
                with open(self.history_file, "a", encoding="utf-8") as f:
                    f.write(log_entry)
                
                logger.info(f"📝 Logged user query to {self.history_file}")
                
            except Exception as e:
                logger.error(f"❌ Failed to log user query: {e}")
                # Don't raise exception - logging failure shouldn't break the main flow
    
    async def get_recent_queries(self, limit: int = 10) -> list:
        """
        Get recent queries from history file
        
        Args:
            limit: Number of recent queries to return
            
        Returns:
            List of recent query entries
        """
        try:
            if not self.history_file.exists():
                return []
            
            with open(self.history_file, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Split by separator and get recent entries
            entries = content.split("-" * 80 + "\n\n")
            entries = [entry.strip() for entry in entries if entry.strip()]
            
            # Return most recent entries
            return entries[-limit:] if entries else []
            
        except Exception as e:
            logger.error(f"❌ Failed to read query history: {e}")
            return []

# Global instance
command_logger = CommandLogger()
