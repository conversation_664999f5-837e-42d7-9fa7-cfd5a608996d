import os
import re
import logging
import lancedb
import numpy as np
import pyarrow as pa
import shutil
import traceback

#from rerankers import Reranker
from sentence_transformers import SentenceTransformer
#from utils.gitutils import GitUtils
#from models.knowledge_graph import KnowledgeGraphs
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.text_splitter import Language

from utils.llm import LLM
#from models.chat import Chat

# Configure minimal logging for performance
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

class Kg:
    def __init__(self):
        self.model_name = os.getenv("CODE_EMBEDDING_MODEL", "microsoft/codebert-base")
        self.model_device = os.getenv("CODE_EMBEDDING_MODEL_DEVICE", "cpu")
        self.expected_dim = int(os.getenv("CODE_EMBEDDING_MODEL_DIMENSION", "768"))
        self.chunk_size = int(os.getenv("CODE_EMBEDDING_CHUNK_SIZE", "2000"))
        self.chunk_overlap = int(os.getenv("CODE_EMBEDDING_CHUNK_OVERLAP", "200"))
        self.chunk_initial_max = int(os.getenv("CODE_EMBEDDING_CHUNK_MAX_INITIAL", "200"))
        
        # Cache for model and database connections
        self._model_cache = None
        self._db_cache = {}
        
        #print(f"🚀 Initializing Kg with model: {self.model_name}, device: {self.model_device}")
        #print(f"⚙️  Chunk size: {self.chunk_size}, overlap: {self.chunk_overlap}")

    def _standardize_file_path(self, file_path):
        """Standardize file path to absolute normalized path"""
        return os.path.normpath(os.path.abspath(file_path))

    def _get_db_path(self, project_name):
        """Get standardized database path"""
        lancedb_location = os.getenv("LANCEDB_LOCATION", "./db")
        return f"{lancedb_location}/{project_name}"

    def _get_model(self):
        """Get cached model instance"""
        if self._model_cache is None:
            #print("🤖 Loading embedding model...")
            self._model_cache = SentenceTransformer(
                model_name_or_path=self.model_name, 
                device=self.model_device
            )
            #print("✅ Model loaded successfully")
        return self._model_cache

    def _get_database(self, project_name):
        """Get cached database connection"""
        if project_name not in self._db_cache:
            db_path = self._get_db_path(project_name)
            self._db_cache[project_name] = lancedb.connect(db_path)
        return self._db_cache[project_name]

    def _get_table(self, project_name, table_name="codebase"):
        """Get table from database with existence check"""
        db = self._get_database(project_name)
        if table_name not in db.table_names():
            return None
        return db.open_table(table_name)

    def index_file_from_client_content(self, project_id: str, file_data: dict) -> bool:
        """
        Index a file using content provided by client with proper embedding generation.

        Args:
            project_id: The project identifier
            file_data: Dictionary containing file information and chunks from client
                      Expected format: {'filePath': str, 'chunks': List[str]}

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"🔍 KG: Starting index_file_from_client_content")
            #print(f"  file_data keys: {list(file_data.keys())}")

            file_path = file_data.get("filePath")
            chunks = file_data.get("chunks", [])

            #print(f"🔍 KG: Extracted data:")
            print(f"  file_path: {file_path}")
            #print(f"  chunks type: {type(chunks)}")
            #print(f"  chunks count: {len(chunks)}")

            if not file_path:
                logger.warning(f"No file path provided in file_data")
                return False

            if not chunks:
                logger.warning(f"No chunks provided for file: {file_path}")
                return True  # Not an error, just empty file

            # Standardize file path
            standardized_path = self._standardize_file_path(file_path)
            print(f"📁 Standardized path: {standardized_path}")

            # Get model for embedding generation
            model = self._get_model()
            #print(f"🤖 Model loaded: {self.model_name}")

            # Remove existing chunks for this file first
            self._remove_file_chunks(project_id, standardized_path)

            # Process chunks and generate embeddings
            records = []
            #print(f"🔄 Processing {len(chunks)} chunks...")

            # Get full file content to calculate line numbers
            full_content = '\n'.join(chunks) if chunks else ""

            # Calculate line ranges for all chunks using robust method
            chunk_line_ranges = self._calculate_chunk_line_ranges(full_content, chunks)

            for i, (chunk_text, line_range) in enumerate(zip(chunks, chunk_line_ranges)):
                try:
                    # Generate embedding for this chunk
                    print(f"  📝 Processing chunk {i+1}/{len(chunks)} ({len(chunk_text)} chars)")
                    embedding = model.encode([chunk_text], show_progress_bar=False)[0]

                    # Detect language from file extension
                    language = self._detect_language_from_path(file_path)

                    record = {
                        "file": standardized_path,
                        "chunk": chunk_text,
                        "content": chunk_text,  # For compatibility
                        "chunk_id": f"{standardized_path}_{i}",
                        "language": language,
                        "line_start": line_range['start'],
                        "line_end": line_range['end'],
                        "embedding": embedding
                    }
                    records.append(record)

                   

                except Exception as e:
                    logger.error(f"Failed to process chunk {i} for file {file_path}: {e}")
                    continue

            if not records:
                logger.warning(f"No valid records created for file: {file_path}")
                return True

            print(f"✅ Generated {len(records)} embeddings")

            # Build Arrow table and store in LanceDB
            arrow_table = self.build_arrow_table(records)
            db = self._get_database(project_id)

            if "codebase" not in db.table_names():
                db.create_table("codebase", data=arrow_table)
                print(f"🗄️  Created new table for project: {project_id}")
            else:
                table = db.open_table("codebase")
                table.add(arrow_table)
                print(f"📥 Added {len(records)} chunks to existing table")

            print(f"✅ Successfully indexed file: {file_path} ({len(records)} chunks)")
            return True

        except Exception as e:
            logger.error(f"Failed to index file from client content: {e}")
            print(f"❌ Error details: {traceback.format_exc()}")
            return False

    def _remove_file_chunks(self, project_id: str, file_path: str):
        """
        Remove existing chunks for a file from the database

        Args:
            project_id: The project identifier
            file_path: The file path to remove chunks for
        """
        try:
            db = self._get_database(project_id)
            if "codebase" in db.table_names():
                table = db.open_table("codebase")
                # Escape single quotes in file path for SQL safety
                escaped_path = file_path.replace("'", "''")
                # Delete existing entries for this file
                table.delete(f"file = '{escaped_path}'")
                print(f"🗑️  Removed existing chunks for file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to remove existing chunks for {file_path}: {e}")

    @staticmethod
    def remove_file_chunks_static(project_id: str, file_path: str):
        """
        Static method to remove file chunks (for backward compatibility)

        Args:
            project_id: The project identifier
            file_path: The file path to remove chunks for
        """
        kg = Kg()
        kg._remove_file_chunks(project_id, file_path)

    def _process_file_content(self, file_path):
        """Read and validate file content"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                code = f.read()
            
            if not code.strip():
                logger.debug(f"File {file_path} is empty")
                return None
            
            return code
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            return None

    def _create_records_from_file(self, file_path, code):
        """Create embedding records from file content"""
        # Standardize the file path before storing
        standardized_path = self._standardize_file_path(file_path)

        # Chunk the code
        chunks = self.chunk_code_with_langchain(code, file_path)
        if not chunks:
            logger.debug(f"No valid chunks found in file: {file_path}")
            return []

        # Calculate line ranges for all chunks
        chunk_line_ranges = self._calculate_chunk_line_ranges(code, chunks)

        # Embed the chunks
        model = self._get_model()
        embeddings = self.embed_chunks(chunks, model)

        # Create records
        records = []
        for i, (chunk, emb, line_range) in enumerate(zip(chunks, embeddings, chunk_line_ranges)):
            if not chunk.strip():
                continue

            embedding_list = emb.tolist() if isinstance(emb, np.ndarray) else list(emb)
            if len(embedding_list) != self.expected_dim:
                logger.warning(f"Skipping embedding with unexpected dimension in file {file_path}: "
                            f"{len(embedding_list)} (expected {self.expected_dim})")
                continue

            records.append({
                "file": standardized_path,  # Store standardized path
                "chunk": chunk,
                "chunk_id": f"{standardized_path}_{i}",
                "content": chunk,  # For compatibility
                "language": self._detect_language_from_path(file_path),
                "line_start": line_range['start'],
                "line_end": line_range['end'],
                "embedding": embedding_list
            })

        return records

    def _create_records_from_content(self, file_path, content, language=None):
        """Create embedding records from provided content instead of reading file"""
        # Standardize the file path before storing
        standardized_path = self._standardize_file_path(file_path)

        # Chunk the code
        chunks = self.chunk_code_with_langchain(content, file_path)
        if not chunks:
            logger.debug(f"No valid chunks found in content for: {file_path}")
            return []

        # Calculate line ranges for all chunks
        chunk_line_ranges = self._calculate_chunk_line_ranges(content, chunks)

        # Embed the chunks
        model = self._get_model()
        embeddings = model.encode(chunks)

        # Create records
        records = []
        for i, (chunk, embedding, line_range) in enumerate(zip(chunks, embeddings, chunk_line_ranges)):
            record = {
                "file": standardized_path,
                "chunk_id": f"{standardized_path}_{i}",
                "content": chunk,
                "language": language or self._detect_language_from_path(file_path),
                "line_start": line_range['start'],
                "line_end": line_range['end'],
                "embedding": embedding
            }
            records.append(record)

        return records

    def _detect_language_from_path(self, file_path):
        """Detect programming language from file extension"""
        ext = os.path.splitext(file_path)[1].lower()
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.scala': 'scala',
            '.html': 'html',
            '.css': 'css',
            '.sql': 'sql',
            '.md': 'markdown',
            '.json': 'json',
            '.xml': 'xml',
            '.yml': 'yaml',
            '.yaml': 'yaml'
        }
        return language_map.get(ext, 'text')

    def read_code_files(self, root_dir, exclude_folders=None, exclude_extensions=None):
        exclude_folders = [re.compile(p) for p in (exclude_folders or [])]
        exclude_extensions = [re.compile(p) for p in (exclude_extensions or [])]

        print(f"📁 Reading code files from: {root_dir}")

        code_files = []
        for dirpath, dirnames, filenames in os.walk(root_dir):
            dirnames[:] = [
                d for d in dirnames
                if not any(p.fullmatch(d) for p in exclude_folders)
            ]

            for filename in filenames:
                if any(p.search(filename) for p in exclude_extensions):
                    continue

                filepath = os.path.join(dirpath, filename)
                code_files.append(filepath)

        #print(f"🔍 Found {len(code_files)} code files to process")
        return code_files

    def get_splitter_for_extension(self, extension):
        extension = extension.lower()
        try:
            language = Language(extension.lstrip('.'))
            return RecursiveCharacterTextSplitter.from_language(
                language=language,
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap
            )
        except ValueError:
            return RecursiveCharacterTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap
            )

    def chunk_code_with_langchain(self, code, filename):
        ext = os.path.splitext(filename)[1]
        splitter = self.get_splitter_for_extension(ext)
        chunks = splitter.split_text(code)
        return chunks

    def _calculate_chunk_line_ranges(self, full_content, chunks):
        """
        Calculate accurate line ranges for chunks by tracking positions in the original content.
        This method handles overlapping chunks and duplicate content correctly.
        """
        chunk_line_ranges = []
        content_lines = full_content.split('\n')

        for chunk in chunks:
            chunk_lines = chunk.split('\n')

            # Find the best match for this chunk in the content
            best_start_line = 1
            best_match_score = 0

            # Search for the chunk starting from different positions
            for start_line_idx in range(len(content_lines)):
                if start_line_idx + len(chunk_lines) > len(content_lines):
                    break

                # Calculate match score for this position
                match_score = 0
                for i, chunk_line in enumerate(chunk_lines):
                    if start_line_idx + i < len(content_lines):
                        if content_lines[start_line_idx + i].strip() == chunk_line.strip():
                            match_score += 1

                # Update best match if this is better
                if match_score > best_match_score:
                    best_match_score = match_score
                    best_start_line = start_line_idx + 1  # Convert to 1-based indexing

            # Calculate end line
            end_line = best_start_line + len(chunk_lines) - 1

            chunk_line_ranges.append({
                'start': best_start_line,
                'end': max(best_start_line, end_line)
            })

        return chunk_line_ranges

    def embed_chunks(self, chunks, model):
        return model.encode(chunks, show_progress_bar=False)

    def build_arrow_table(self, records):
        #print(f"🏗️  Building Arrow table from {len(records)} records...")
        files = [r["file"] for r in records]

        # Handle both old format (chunk) and new format (content)
        chunks = [r.get("chunk", r.get("content", "")) for r in records]

        # Extract line numbers if available
        line_starts = [r.get("line_start", 0) for r in records]
        line_ends = [r.get("line_end", 0) for r in records]
        languages = [r.get("language", "text") for r in records]
        chunk_ids = [r.get("chunk_id", f"{r['file']}_{i}") for i, r in enumerate(records)]

        embeddings = np.array([r["embedding"] for r in records], dtype=np.float32)
        embedding_flat = embeddings.flatten().tolist()
        embedding_array = pa.FixedSizeListArray.from_arrays(
            pa.array(embedding_flat, type=pa.float32()),
            list_size=self.expected_dim
        )

        # Build enhanced table with line number support
        table_data = {
            "file": pa.array(files, pa.string()),
            "chunk": pa.array(chunks, pa.string()),
            "embedding": embedding_array
        }

        # Add line number columns if any record has them
        if any(line_starts) or any(line_ends):
            table_data.update({
                "line_start": pa.array(line_starts, pa.int32()),
                "line_end": pa.array(line_ends, pa.int32()),
                "language": pa.array(languages, pa.string()),
                "chunk_id": pa.array(chunk_ids, pa.string())
            })
            print(f"✅ Enhanced Arrow table with line numbers: {len(records)} records")
        else:
            print(f"✅ Standard Arrow table built: {len(records)} records")

        table = pa.table(table_data)
        return table

    def index_codebase(self, project_name, code_folder):
        try:
            print(f"🎯 Starting index process for: {project_name}")
            print(f"📂 Processing repository at: {code_folder}")
        except Exception as e:
            print(f"❌ Failed to access repository: {e}")
            raise
        
        env_exclude_folders = os.getenv("FOLDERS_TO_SKIP", "")
        exclude_folders = [p.strip() for p in env_exclude_folders.split(",") if p.strip()]
        
        env_exclude_extensions = os.getenv("EXTENSIONS_TO_SKIP", "")
        exclude_extensions = [p.strip() for p in env_exclude_extensions.split(",") if p.strip()]

        try:
            code_files = self.read_code_files(code_folder, exclude_folders, exclude_extensions)
            if not code_files:
                print("⚠️  No valid code files found after filtering")
                return

            records = []
            total_files = len(code_files)
            
            for i, file_path in enumerate(code_files, 1):
                if i % 20 == 0:  # Progress update every 20 files
                    print(f"⏳ Processing file {i}/{total_files}: {os.path.basename(file_path)}")
                
                code = self._process_file_content(file_path)
                if code is None:
                    continue
                
                file_records = self._create_records_from_file(file_path, code)
                records.extend(file_records)

            if not records:
                print("⚠️  No valid records to persist after processing")
                return

            # Build and persist table
            arrow_table = self.build_arrow_table(records)
            db = self._get_database(project_name)
            
            if "codebase" in db.table_names():
                print("🗑️  Dropping existing 'codebase' table")
                db.drop_table("codebase")

            db.create_table("codebase", data=arrow_table)
            print(f"💾 Persisted {len(records)} code chunks to LanceDB")
            print("🎉 Indexing completed successfully!")

        except Exception as e:
            print(f"💥 Error occurred while indexing: {e}")
            raise

    def search_codebase(self, project, query, context_size=20):
        print(f"🔍 Searching for: {query}")

        try:
            model = self._get_model()
            table = self._get_table(project)

            if table is None:
                print(f"❌ No 'codebase' table found for project: {project}")
                return []

            # Embed the query
            query_emb = model.encode([query])
            query_vec = query_emb[0] if isinstance(query_emb, np.ndarray) else list(query_emb)

            # Perform vector search
            results = table.search(query_vec, vector_column_name="embedding").limit(context_size).to_arrow()
            chunks = [results["chunk"][idx].as_py() for idx in range(len(results["chunk"]))]

            print(f"✅ Found {len(chunks)} relevant chunks")
            return chunks

        except Exception as e:
            print(f"💥 Error during search: {e}")
            traceback.print_exc()
            raise

    def search_codebase_with_lines(self, project, query, context_size=20):
        """Enhanced search that returns chunks with line number information"""
        print(f"🔍 Enhanced search for: {query}")

        try:
            model = self._get_model()
            table = self._get_table(project)

            if table is None:
                print(f"❌ No 'codebase' table found for project: {project}")
                return []

            # Embed the query
            query_emb = model.encode([query])
            query_vec = query_emb[0] if isinstance(query_emb, np.ndarray) else list(query_emb)

            # Perform vector search
            results = table.search(query_vec, vector_column_name="embedding").limit(context_size).to_arrow()
            
            #print(f"  \n KG ------ results schema: {results}")

            enhanced_chunks = []
            for idx in range(len(results["chunk"])):
                chunk_data = {
                    "content": results["chunk"][idx].as_py(),
                    "file": results["file"][idx].as_py(),
                }
                
                #print(f"  \n KG ------ chunk_data: {chunk_data}")

                # Add line numbers if available
                if "line_start" in results.schema.names:
                    chunk_data.update({
                        "line_start": results["line_start"][idx].as_py(),
                        "line_end": results["line_end"][idx].as_py(),
                       
                    })

                enhanced_chunks.append(chunk_data)

            #print(f"✅ Found {len(enhanced_chunks)} relevant chunks with line info -- {enhanced_chunks}")
            return enhanced_chunks

        except Exception as e:
            print(f"💥 Error during enhanced search: {e}")
            traceback.print_exc()
            raise

    def ask(self, project, question):
        # Use enhanced search with line numbers for better context
        enhanced_chunks = self.search_codebase_with_lines(project, question, 20)

        # Format chunks with line information for better context
        if enhanced_chunks and any(chunk.get("line_start") for chunk in enhanced_chunks):
            formatted_chunks = []
            for chunk in enhanced_chunks:
                if chunk.get("line_start") and chunk.get("line_end"):
                    formatted_chunk = f"File: {chunk['file']} (Lines {chunk['line_start']}-{chunk['line_end']}, {chunk.get('language', 'text')}):\n{chunk['content']}"
                else:
                    formatted_chunk = f"File: {chunk['file']}:\n{chunk['content']}"
                formatted_chunks.append(formatted_chunk)
            chunks = formatted_chunks
        else:
            # Fallback to basic search if enhanced search fails
            chunks = self.search_codebase(project, question, 20)

        combined = f" context={str(chunks)}, User Query ={question}"
        llm_response = LLM().call_litellm(combined)
        #print(f"🤖✨ [bold green]RAG Agent[/bold green] -- llm_response {llm_response}✨")
        return llm_response

    def remove_file(self, project_name, file_path):
        """
        Remove a file from the knowledge graph by deleting only its chunks from LanceDB.
        """
        try:
            standardized_path = self._standardize_file_path(file_path)
            print(f"🗑️  Removing file from index: {os.path.basename(file_path)}")
            
            table = self._get_table(project_name)
            if table is None:
                print(f"⚠️  No 'codebase' table found for project: {project_name}")
                return True
            
            # Check if file exists in the table first
            try:
                escaped_path = standardized_path.replace("'", "''")
                existing_chunks = table.search().where(f"file = '{escaped_path}'").to_list()
                
                if not existing_chunks:
                    print(f"ℹ️  File not found in index: {os.path.basename(file_path)}")
                    return True 
            except Exception as e:
                logger.warning(f"Error checking if file exists in table: {str(e)}")
            
            # Delete chunks for this specific file
            try:
                deleted_count = table.delete(f"file = '{escaped_path}'")
                print(f"✅ Successfully removed {deleted_count} chunks for: {os.path.basename(file_path)}")
                return True
            except Exception as e:
                print(f"❌ Error removing file {os.path.basename(file_path)}: {str(e)}")
                return False
            
        except Exception as e:
            print(f"💥 Error removing file {os.path.basename(file_path)}: {str(e)}")
            return False

    def index_file(self, project_name, file_path):
        """
        Index a single file into the knowledge graph.
        """
        try:
            # Check if file exists
            if not os.path.isfile(file_path):
                print(f"❌ File not found: {os.path.basename(file_path)}")
                return False

            print(f"📄 Indexing file: {os.path.basename(file_path)}")

            # Remove existing chunks for this file first
            if not self.remove_file(project_name, file_path):
                print(f"⚠️  Failed to remove existing chunks, continuing anyway...")

            # Process file content
            code = self._process_file_content(file_path)
            if code is None:
                print(f"ℹ️  File is empty or unreadable: {os.path.basename(file_path)}")
                return True

            # Create records
            records = self._create_records_from_file(file_path, code)
            if not records:
                print(f"ℹ️  No valid records for file: {os.path.basename(file_path)}")
                return True

            # Build Arrow table and add to database
            arrow_table = self.build_arrow_table(records)
            db = self._get_database(project_name)

            if "codebase" not in db.table_names():
                db.create_table("codebase", data=arrow_table)
                print(f"🆕 Created new table with {len(records)} chunks")
            else:
                table = db.open_table("codebase")
                table.add(arrow_table)
                print(f"➕ Added {len(records)} chunks to existing table")

            print(f"✅ Successfully indexed: {os.path.basename(file_path)}")
            return True

        except Exception as e:
            print(f"💥 Error indexing {os.path.basename(file_path)}: {str(e)}")
            return False

    def index_file_from_content(self, project_name, file_path, content, language=None):
        """
        Index a file using provided content instead of reading from disk.
        This method is used by the API to index files with content sent by the client.
        """
        try:
            print(f"📄 Indexing file from content: {os.path.basename(file_path)}")

            # Remove existing chunks for this file first
            if not self.remove_file(project_name, file_path):
                print(f"⚠️  Failed to remove existing chunks, continuing anyway...")

            # Validate content
            if not content or not content.strip():
                print(f"ℹ️  File content is empty: {os.path.basename(file_path)}")
                return True

            # Create records from provided content
            records = self._create_records_from_content(file_path, content, language)
            if not records:
                print(f"ℹ️  No valid records for file: {os.path.basename(file_path)}")
                return True

            # Build Arrow table and add to database
            arrow_table = self.build_arrow_table(records)
            db = self._get_database(project_name)

            if "codebase" not in db.table_names():
                db.create_table("codebase", data=arrow_table)
                print(f"🆕 Created new table with {len(records)} chunks")
            else:
                table = db.open_table("codebase")
                table.add(arrow_table)
                print(f"➕ Added {len(records)} chunks to existing table")

            print(f"✅ Successfully indexed from content: {os.path.basename(file_path)}")
            return True

        except Exception as e:
            print(f"💥 Error indexing from content {os.path.basename(file_path)}: {str(e)}")
            return False

    def check_file_in_index(self, project_name, file_path):
        """
        Check if a file is already indexed in the knowledge graph.
        """
        try:
            standardized_path = self._standardize_file_path(file_path)
            
            table = self._get_table(project_name)
            if table is None:
                return False, 0
            
            escaped_path = standardized_path.replace("'", "''")
            existing_chunks = table.search().where(f"file = '{escaped_path}'").to_list()
            
            exists = len(existing_chunks) > 0
            if exists:
                print(f"✅ File found in index: {os.path.basename(file_path)} ({len(existing_chunks)} chunks)")
            else:
                print(f"❌ File not in index: {os.path.basename(file_path)}")
                
            return exists, len(existing_chunks)
            
        except Exception as e:
            print(f"💥 Error checking file: {str(e)}")
            return False, 0

    def cleanup_cache(self):
        """Clean up cached resources"""
        self._model_cache = None
        self._db_cache.clear()
        print("🧹 Cleaned up cached resources")