import os
import time
import datetime
import schedule
from threading import Thread
from colorama import Fore, Style
import logging

# Import the MerkleIndexer
from merkle.merkle_indexer import MerkleIndexer


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def schedule_all_dag_rebuilds():
    """
    Start a scheduler to periodically rebuild the Merkle DAG for all projects.
    Automatically discovers all project folders within WORKING_FOLDER.
    The interval is read from the MERKLE_REBUILD_INTERVAL environment variable.
    """
    # Get interval from environment variable (default to 30 minutes)
    interval_minutes = int(os.getenv("MERKLE_REBUILD_INTERVAL", "30"))
    working_folder = os.getenv("WORKING_FOLDER", "./generated")
    
    logger.info(f"{Fore.CYAN}=== Starting Merkle DAG Scheduler for All Projects ==={Style.RESET_ALL}")
    logger.info(f"{Fore.GREEN}Scheduling DAG rebuilds every {interval_minutes} minutes{Style.RESET_ALL}")
    logger.info(f"{Fore.GREEN}Working folder: {working_folder}{Style.RESET_ALL}")
    
    # Function to discover and rebuild all projects
    def rebuild_all_projects():
        logger.info(f"{Fore.YELLOW}[{datetime.datetime.now()}] Discovering projects and running scheduled rebuilds...{Style.RESET_ALL}")
        
        # Get all immediate subdirectories in the working folder
        try:
            project_dirs = [d for d in os.listdir(working_folder) 
                           if os.path.isdir(os.path.join(working_folder, d))]
            
            if not project_dirs:
                logger.info(f"{Fore.YELLOW}No projects found in {working_folder}{Style.RESET_ALL}")
                return
                
            logger.info(f"{Fore.GREEN}Found {len(project_dirs)} projects: {', '.join(project_dirs)}{Style.RESET_ALL}")
            
            # Rebuild each project
            for project_name in project_dirs:
                logger.info(f"{Fore.CYAN}Rebuilding project: {project_name}{Style.RESET_ALL}")
                try:
                    indexer = MerkleIndexer(project_name)
                    indexer.update_index(force_full=False)
                    logger.info(f"{Fore.GREEN}Rebuild completed for {project_name}{Style.RESET_ALL}")
                except Exception as e:
                    logger.error(f"{Fore.RED}Error rebuilding {project_name}: {str(e)}{Style.RESET_ALL}")
                    
            logger.info(f"{Fore.GREEN}[{datetime.datetime.now()}] All scheduled rebuilds completed{Style.RESET_ALL}")
        except Exception as e:
            logger.error(f"{Fore.RED}Error discovering projects: {str(e)}{Style.RESET_ALL}")
    
    # Run once immediately
    rebuild_all_projects()
    
    # Schedule the job
    schedule.every(interval_minutes).minutes.do(rebuild_all_projects)
    
    # Run the scheduler in a background thread
    def run_scheduler():
        while True:
            schedule.run_pending()
            time.sleep(1)
    
    # Start the scheduler thread
    scheduler_thread = Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    
    logger.info(f"{Fore.GREEN}Scheduler started in background. Press Ctrl+C to stop.{Style.RESET_ALL}")
    
    # Keep the main thread alive to allow the scheduler to run
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        logger.info(f"{Fore.YELLOW}Scheduler stopped.{Style.RESET_ALL}")

if __name__ == "__main__":
    # This allows the script to be run directly
    schedule_all_dag_rebuilds()