import gzip
import base64
import json
import logging
from fastapi import HTTPException

logger = logging.getLogger(__name__)

def decompress_payload(compressed_data: str) -> dict:
    """Decompress and parse the payload from client"""
    try:
        # Decode base64
        compressed_bytes = base64.b64decode(compressed_data)
        # Decompress gzip
        decompressed_bytes = gzip.decompress(compressed_bytes)
        # Parse JSON
        payload = json.loads(decompressed_bytes.decode('utf-8'))
        return payload
    except Exception as e:
        logger.error(f"❌ Failed to decompress payload: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid compressed payload: {e}") 