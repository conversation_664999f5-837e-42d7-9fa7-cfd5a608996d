from typing import Dict, List, Optional, Any
from pydantic import BaseModel

class ChunkData(BaseModel):
    content: str
    chunk_hash: str
    line_range: Dict[str, int]

class FileData(BaseModel):
    obfuscated_path: str
    file_hash: str
    language: str
    chunks: List[ChunkData]
    type: str

class MerkleTree(BaseModel):
    root: str
    leaves: List[str]
    tree: Dict[str, str]
    fileMap: Dict[str, Any]
    timestamp: int

class IndexBatchRequest(BaseModel):
    compressed: str

class DifferentialSyncRequest(BaseModel):
    compressed: str

class IndexBatchResponse(BaseModel):
    status: str
    message: str
    batch_index: int
    processed_files: int

class LatestMerkleTreeResponse(BaseModel):
    status: str
    merkle_tree: Optional[MerkleTree] = None
    message: str

class DifferentialSyncResponse(BaseModel):
    status: str
    message: str
    processed_changes: Dict[str, int]

class ProjectSummaryRequest(BaseModel):
    project_name: str
    question: Optional[str] = "Generate a high-level summary of this code in 3 sentences."

class ProjectSummaryResponse(BaseModel):
    status: str
    summary: str
    project_name: str

class ActiveFileInfo(BaseModel):
    fileName: str
    filePath: str
    fullPath: str

class CoderRequest(BaseModel):
    project_name: str
    question: str
    active_file: Optional[ActiveFileInfo] = None

class FileOperation(BaseModel):
    operation: str  # "create", "update", "delete"
    file_path: str
    content: Optional[str] = None
    line_number: Optional[int] = None

class CoderResponse(BaseModel):
    status: str
    message: str
    project_name: str
    file_operations: List[FileOperation]
    summary: str 