1. Index the entire codebase

   - Compress the entire codebase, will be used to search, readfile, grep, list dir
   - Chunk the code base content, read file by file
   - Embed the chunk using code-embedding model
   - Persist into a vector db for RAG

2. Codebase Agent Mode (complex tasks, lots of token usage)

   - Features:
     Ask questions about your code,
     Get advice on how to refactor code,
     Add new features to selected lines of code,
     Add new feature spanning multiple files,
     Document new features,
     Automatically perform tasks on your behalf (git actions)
   - Use the chat template for codebase agent mode
   - Add all these tool capabilites to the agent mode
     - codebase_search - This semantic search tool finds relevant code snippets. Use the user's exact query and specify directories if needed for accuracy.
     - read_file - This tool reads specific lines from a file with summaries of the rest. Always ensure full context by reading more if needed.
     - run_terminal_cmd - Propose commands to run on the user's system. Commands need user approval, must handle state, and avoid interactive breaks.
     - list_dir - Use this tool to quickly list directory contents and understand file structure before using deeper tools like semantic search or reading.
     - grep_search - Use this fast regex search to find exact matches with regex. Ideal for locating specific strings or patterns in code.
     - edit_file - Propose clear, minimal edits to a file with context. Use // ... existing code ... to show unchanged parts between edits.
     - file_search - Quickly find files using fuzzy path matching. Best when you know part of a filename but not its exact location.
     - delete_file - Deletes a specified file. Fails safely if the file is missing, access is denied, or deletion isn't permitted.
     - reapply - Uses a smarter model to apply the last edit if the previous attempt was incorrect or didn't follow instructions.
     - web_search - Search the web for real-time, up-to-date information. Ideal for current events, tech updates, or verifying recent facts.
     - diff_history - Retrieve recent file change history, including modified files, timestamps, and line additions/removals. Use to understand recent codebase modifications.

3. Codebase Indexing and LanceDB Integration

   Before using the semantic search capabilities, codebases must be indexed into LanceDB. This is a two-step process that needs to be performed manually for both legacy and modern/working codebases.

   ### Initial Indexing Requirements

   - **Legacy Codebases**: Older codebases or reference implementations must be indexed first to provide historical context and reference patterns
   - **Working/Modern Codebases**: Your current working codebase must be indexed separately to enable accurate semantic search and code understanding
     (Governed by WORKING_FOLDER env variable)

   ### Indexing Process

   1. **Step 1: Index Legacy Codebase**

      ```bash
      python coder.py kg legacy_code /path/to/legacy/codebase
      ```

      This creates a baseline of reference implementations and patterns in your LanceDB.

   2. **Step 2: Index Working Codebase**
      ```bash
      python coder.py kg working_code /path/to/working/codebase
      ```
      This adds your current working code to the knowledge graph, enabling the system to understand your specific implementation.

   ### LanceDB Table Structure

   The system creates and maintains the following tables in LanceDB:

   - `codebase`: Contains code chunks with embeddings for semantic search
   - `merkle_dag`: Stores the Merkle DAG history for tracking changes

   ### Embedding and Chunking

   - Code is automatically chunked into semantically meaningful segments
   - Each chunk is embedded using a specialized code-embedding model
   - Embeddings capture semantic meaning, allowing for concept-based search
   - File metadata is preserved to maintain context and relationships

   ### Updating the Index

   After the initial indexing, you can keep the index up-to-date using:

   - Scheduled updates (see section 4)
   - Manual rebuilds (see section 5)
   - Incremental updates when files change

   ### Example: Indexing a Sample Codebase

   ```bash
   # Index a sample codebase
   python coder.py kg working_sam_code /Users/<USER>/Documents/ai/code/slingshot-product/ref/v3/coder/generated/sam_code
   ```

   ### Troubleshooting

   - If indexing fails, check file permissions and path validity
   - For large codebases, consider indexing in smaller batches
   - If embeddings seem incorrect, ensure you're using the latest embedding model

4. Querying Indexed Codebases with Natural Language

   Once your codebases are indexed, you can ask natural language questions about your code using the semantic search capabilities. This allows you to quickly find relevant code snippets, understand implementation patterns, and explore your codebase without having to manually search through files.

   ### Basic Usage

   ```bash
   python coder.py ask-gr <lancedb_table_name> "Your question about the code"
   ```

   For example:

   ```bash
   python coder.py ask-gr working_sam_code "How is authentication implemented?"
   ```

   ### Query Types

   The system supports various types of queries:

   - **Conceptual Questions**: "How is error handling implemented?"
   - **Implementation Details**: "Show me examples of database connections"
   - **Architecture Questions**: "What is the overall structure of the API?"
   - **Pattern Discovery**: "Find all instances of the observer pattern"
   - **Function Location**: "Where is the user validation logic?"

   ### Advanced Query Options

   ```bash
   python coder.py ask-gr <lancedb_table_name>
   ```

   ### Using Query Results

   You can use these results to:

   - Understand existing implementations
   - Find reference examples for new features
   - Identify patterns and anti-patterns
   - Locate code that needs refactoring

5. Scheduled Merkle Tree DAG Updates

   The system includes an automated scheduler for keeping the Merkle Tree DAG up-to-date, which is essential for tracking code changes and maintaining accurate semantic search capabilities.

   ### How It Works

   - The scheduler automatically discovers all project folders within the `WORKING_FOLDER` environment variable
   - For each project, it rebuilds the Merkle DAG at regular intervals defined by `MERKLE_REBUILD_INTERVAL` (in minutes)
   - The scheduler runs as a background process, continuously monitoring and updating the codebase representation
   - Each update captures incremental changes, avoiding full rebuilds unless necessary

   ### Configuration

   - Set `MERKLE_REBUILD_INTERVAL` in your `.env` file (default: 30 minutes)
   - Set `WORKING_FOLDER` in your `.env` file to specify where your projects are located

   ### Running the Scheduler

   ```bash
   python utils/schedule_dag_diff.py
   ```

   ### Benefits

   - Ensures semantic search always uses up-to-date code representations
   - Maintains historical versions for tracking changes over time
   - Optimizes resource usage by only processing changed files
   - Provides automatic cleanup of old DAG versions to prevent storage bloat

6. Manual Merkle Tree DAG Rebuilding

   For cases where you need immediate updates or want more control over the DAG rebuilding process, you can manually trigger rebuilds.

   ### Basic Usage

   ```bash
   python coder.py rebuild-dag <project_name>
   ```

   ### Options

   - `project_name`: The name of the project folder to rebuild (required)
   - `--force-full`: Force a complete rebuild of the entire codebase, even if no files have changed

   ### When to Use Manual Rebuilds

   - After significant code changes that need immediate indexing
   - When troubleshooting search or indexing issues
   - Before important coding sessions to ensure the most up-to-date context
   - When the automatic scheduler is not running

   ### How It Works

   - Computes hashes for all files in the project
   - Builds a Merkle Tree to efficiently track changes
   - Compares with previous version to identify modified files
   - Only re-indexes changed files to save processing time
   - Updates the vector database with new embeddings
   - Preserves the DAG history for future reference

   ### Performance Considerations

   - Full rebuilds can be resource-intensive for large codebases
   - Incremental updates are much faster and are the default behavior
   - The system automatically optimizes for minimal processing while maintaining accuracy

## Starting the Server

1. **Setup your environment variables:**

   - Copy `.env.example` to `.env` and fill in any required values.
   - Make sure all necessary environment variables are set for your environment.

2. **Install dependencies:**

   ```bash
   pip install -r requirements.txt
   ```

3. **Start the server:**

   ```bash
   python start_api.py
   ```

   This will launch the FastAPI server using the configuration in your `.env` file.

4. **Architecture Details:**
   - For a detailed explanation of the WebSocket architecture and server internals, see the documentation in the `arch/` folder (`arch/websockets.md`).
