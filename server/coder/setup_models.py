import os

from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv, find_dotenv
from rich import print

find_dotenv()
load_dotenv()

model_name = os.getenv("CODE_EMBEDDING_MODEL")
model_location = os.getenv("CODE_EMBEDDING_MODEL_LOCATION")

def has_folder_contents():
    if os.path.isabs(model_location):
        return False

    return os.path.isdir(model_location) and any(os.path.isfile(os.path.join(model_location, f)) for f in os.listdir(model_location))

if not has_folder_contents():
    print(f":hourglass_not_done: checking to download {model_name} ...")
    model = SentenceTransformer(model_name)
    model.save(model_location)
    print(f"✅ {model_name} downloaded successfully")
else:
    print(f"⚠️  {model_name} has already been downloaded")
