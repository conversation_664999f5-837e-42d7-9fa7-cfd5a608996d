import traceback
import typer
import os
import time
import json
import datetime
from colorama import Fore, Style, init

from pocketflow import Node, Flow

from rich import print
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from typing_extensions import Annotated

from dotenv import find_dotenv, load_dotenv

from utils.code_index import CodeIndex, CodeCompress
from utils.history import History
from utils.rag import <PERSON>g<PERSON><PERSON><PERSON>, Rag<PERSON><PERSON>y
from utils.scg import Scg
from utils.kg import Kg
from utils.sb_project import SpringBootProject
from utils.graph_rag import GraphRagProcessor
from utils.merkle.merkle_indexer import MerkleIndexer

from tools.summary_tool import SummaryTool
from tools.code_init_tool import CodeInitTool
from tools.docs_tool import DocsTool
from tools.dir_tool import DirectoryTool
from tools.file_read_tool import FileReadTool

from tools.analysis_tool import AnalysisTool
from tools.semantic_search_tool import SemanticSearchTool

from agents.code_review_agent import CodeReviewerAgent
from tools.code_expert_tool import CodeExpertTool
from tools.code_applier_tool import CodeApplierTool


from agents.coder_agent import CoderAgent

find_dotenv()
load_dotenv(override=True)

app = typer.Typer()
console = Console()

def display_welcome_screen():
    # Create welcome text with styling
    welcome_text = Text()
    welcome_text.append("Welcome to ", style="cyan")
    welcome_text.append("Slingshot Agent Coding", style="bright_cyan bold")
    welcome_text.append("!", style="cyan")
    
    welcome_panel = Panel(
        welcome_text,
        subtitle="Starting up...",
        border_style="cyan"
    )
    
    console.print(welcome_panel)
    
    with console.status("Loading", spinner="dots"):
        time.sleep(1)

@app.callback()
def main():
    display_welcome_screen()

def init_flow():
    coder_agent = CoderAgent()
    code_init_tool = CodeInitTool()
    docs_tool = DocsTool()
    dir_tool = DirectoryTool()
    file_read_tool = FileReadTool()
    code_expert_tool = CodeExpertTool()
    code_reviewer_tool = CodeReviewerAgent()
    code_applier_tool = CodeApplierTool()
    analysis_tool = AnalysisTool()
    semantic_search_tool = SemanticSearchTool()
    summary_tool = SummaryTool()

    # Set up the flow connections
    coder_agent - "code_init_tool" >> code_init_tool
    code_init_tool >> coder_agent

    coder_agent - "docs_tool" >> docs_tool
    docs_tool >> coder_agent

    coder_agent - "dir_tool" >> dir_tool
    dir_tool >> coder_agent

    coder_agent - "file_read_tool" >> file_read_tool
    file_read_tool >> coder_agent

    coder_agent - "analysis_tool" >> analysis_tool
    analysis_tool >> coder_agent
    
    coder_agent - "semantic_search_tool" >> semantic_search_tool
    semantic_search_tool >> coder_agent

    # Code expert flow (generates code)
    coder_agent - "code_expert_tool" >> code_expert_tool
    
   
    code_expert_tool - "code_reviewer_tool" >> code_reviewer_tool

    coder_agent - "code_reviewer_tool" >> code_reviewer_tool
    
    
    code_reviewer_tool - "code_applier_tool" >> code_applier_tool
 
    code_reviewer_tool  - "coder_agent" >> coder_agent
    # Complete flow
    code_applier_tool - "complete" >> summary_tool
    coder_agent - "complete" >> summary_tool

    return code_init_tool

@app.command(help="Generate code from prompts")
def agent(project_name: str):

    question = typer.prompt("🤖✨ What do you want to do?")
    with open("./history/commands.txt", 'a') as f:
        f.writelines(question+'\n')

    shared = History().init(question,project_name)

    code_init_tool = init_flow()

    flow = Flow(start=code_init_tool)
    flow.run(shared)
    
@app.command(help='Generate complete Knowledge Graph of the codebase')
def kg(name: str, location: str):
    print(f":hourglass_not_done: generating knowledge graph for the code at location {location} ...")
    Kg().index_codebase(name, location)
    print(f":hourglass_done: check knowledge graph available")
    
@app.command(help='Ask specific questions on the graph rag for codebase')
def ask_gr(project_name: str):
    question = typer.prompt("What's your question?")
    print(f":hourglass_not_done: processing the question now  for '{project_name}' project...")
    #result = GraphRagProcessor().ask_question(question)
    result = Kg().ask(project_name,question)
    #print(f":hourglass_done: The response is - {result}")
    typer.echo(f":hourglass_done: The response is - {result}")
    

@app.command(help="Index or reindex the complete codebase")
def index():

    print("⚠️  This will now index the codebase, in-case new contents are added, it will re-index it.")
    location = typer.prompt("🤖✨ Enter the complete location of the codebase you want to index?")
    CodeIndex().index(location)



@app.command(help='Delete Knowledge Graph of the codebase')
def del_kg():
    print(f":hourglass_not_done: deleting knowledge graph from graph rag ...")
    response = Kg().delete_code_graph()
    print(f":hourglass_done: knowledge graph deleted")

@app.command(help='Create spring boot project based on config file provided')
def sb(config_file: Annotated[str, typer.Argument()] = "spring_boot_config.json"):
    print(f":hourglass_not_done: generating spring boot project ...")
    SpringBootProject().create(config_file)
    print(f":hourglass_done: done")

@app.command(help='Rebuild Merkle DAG and update index')
def rebuild_dag(project_name: str, force_full: bool = False):
    """
    Rebuild the Merkle DAG and update the index for changed files.
    """
    print(f"{Fore.CYAN}=== Merkle DAG Builder ==={Style.RESET_ALL}")
    
    # Initialize the indexer and run the update
    indexer = MerkleIndexer(project_name)
    indexer.update_index(force_full=force_full)



   

if __name__ == "__main__":
    try:
        app()
    except Exception as e:
       
        
       
        print(f":boom: Coder Agent has encountered an issue at this time, try your operation again. {traceback.print_exc}")
       
        traceback.print_exc()
       
