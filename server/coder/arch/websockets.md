# WebSocket Architecture in This Project

## Overview

This document explains how WebSocket communication is implemented and managed on the server side of this project. It covers the main components, state management, and the flow of messages between client and server.

---

## Key Components

- **api.py**: Defines the FastAPI WebSocket endpoint. Accepts connections and dispatches messages to handlers.
- **websocket/manager.py (WebSocketManager)**: Tracks all active WebSocket connections and manages sending/receiving messages.
- **websocket/context.py (WebSocketContext)**: Stores the current `client_id` for each request/handler using Python's `contextvars` (per-request, not global).
- **websocket/handlers.py**: Contains functions that process incoming WebSocket messages.
- **ws_coder.py (WebSocketCoderService)**: Orchestrates the multi-agent flow for a client's request.
- **websocket/base_node.py (WebSocketBaseNode)**: Base class for all tools/agents, ensures they use the correct context.
- **ws_dir_tool.py / ws_file_read_tool.py**: Tools that request directory listings or file contents from the client.

---

## State Management

| State              | Where Managed    | Scope               | Purpose                                |
| ------------------ | ---------------- | ------------------- | -------------------------------------- |
| Active connections | WebSocketManager | All clients         | Track which clients are connected      |
| Per-client context | WebSocketContext | Per handler/request | Ensure all code uses correct client_id |

- **No command-based file system or command/acknowledgment state is used for file/directory reads.**
- **No unnecessary state is kept for file operations.**

---

## Example Flow

1. **Client** connects to the server via WebSocket.
2. **api.py** accepts the connection, registers the client with a unique `client_id` using the `WebSocketManager`.
3. For each message from the client:
   - The endpoint sets the current `client_id` in the `WebSocketContext` for this handler.
   - The message is dispatched to the appropriate handler in `websocket/handlers.py`.
4. The handler uses the context to know which client it’s working with and may call into services like `WebSocketCoderService`.
5. If the server needs to read a file or list a directory, it uses `WebSocketDirectoryTool` or `WebSocketFileReadTool`, which simply request the file or directory listing from the client.
6. All tools/agents inherit from `WebSocketBaseNode`, which ensures they always use the correct context to communicate with the right client.

---

## Sequence Diagram (Textual)

```
Client (Browser/VSCode)
    |
    v
[WebSocket Connection]
    |
    v
api.py (WebSocket endpoint)
    |
    v
Set context (client_id) for this handler
    |
    v
websocket/handlers.py (message handler)
    |
    v
ws_coder.py (WebSocketCoderService) or other service
    |
    v
[Tools/Agents] <--- inherit from --- websocket/base_node.py
    |
    v
If file/dir needed:
    |
    v
WebSocketDirectoryTool / WebSocketFileReadTool
    |
    v
Request file/dir from client (simple request/response)
```

---

## Message Sending: `send_message` vs `broadcast_console_output`

### `send_message`

- **Purpose:** Sends a generic message (any type) to a specific client.
- **Usage:**
  ```python
  await ws_manager.send_message(client_id, {"type": "some_type", "data": {...}})
  ```
- **Flexibility:** You specify the message type and payload. Used for all kinds of messages (status, errors, file responses, etc.).

### `broadcast_console_output`

- **Purpose:** Sends a message specifically intended for the client’s console/log output (e.g., status updates, logs, progress).
- **Usage:**
  ```python
  await ws_manager.broadcast_console_output(client_id, "Some log message", "info")
  ```
- **What it does:** Internally, it calls `send_message` with a fixed message type (`"console_output"`) and a specific data structure:
  ```python
  {
      "type": "console_output",
      "data": {
          "message": "Some log message",
          "level": "info",
          "timestamp": ...
      }
  }
  ```
- **Intended for:** Streaming logs, status updates, or anything you want to show in a “console” or log panel on the client side.

### Best Practice

- Use `broadcast_console_output` for anything you want to appear in the client’s log/console panel.
- Use `send_message` for all other message types (file responses, status, errors, etc.).
- `broadcast_console_output` is just a convenience wrapper for a common pattern.

| Method                     | Use for...              | Example Use Case        |
| -------------------------- | ----------------------- | ----------------------- |
| `send_message`             | Any message type        | File ops, status, error |
| `broadcast_console_output` | Console/log output only | Progress, logs, info    |

---

## Notes

- All state is only for tracking active connections and per-request context.
- No command-based file system is used for file/directory reads.
- The architecture is simple, robust, and easy to maintain.
