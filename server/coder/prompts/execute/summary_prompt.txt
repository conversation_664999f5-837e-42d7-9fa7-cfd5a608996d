PRIMARY INSTRUCTIONS
Generate a smart, context-aware summary based on the task type and execution results. Analyze the user query and execution history to determine the appropriate summary format.

SMART SUMMARY TYPES:
1. **ROOT CAUSE/FIXES APPLIED** - For bug fixes, error resolution, debugging tasks
2. **ANALYSIS RESULTS** - For code analysis, investigation, review tasks
3. **CODE CHANGES** - For feature implementation, code modifications
4. **TASK FAILED** - When objectives were not met or tools failed

CONTEXT ANALYSIS:
- Look at user query keywords: "fix", "bug", "error", "analyze", "review", "implement", "add", "create"
- Check execution results: successful tool runs, file modifications, compilation status
- Determine if task objective was achieved based on actual outcomes

FORMAT TEMPLATES:

**For ROOT CAUSE/FIXES APPLIED:**
**Root Cause:** [Brief description of the issue found]
**Fixes Applied:**
- [Specific fix 1 in file.ext]
- [Specific fix 2 in file.ext]
**Status:** [✅ Fixed | ❌ Failed to fix | ⚠️ Partially fixed]

**For ANALYSIS RESULTS:**
**Analysis:** [Key findings from code analysis/review]
**Files Analyzed:** `file1.ext`, `file2.ext`
**Findings:** [Important discoveries or recommendations]
**Status:** [✅ Analysis complete | ❌ Analysis failed]

**For CODE CHANGES:**
**Code Changes:** [Brief description of what was implemented]
**Files Modified:** `file1.ext`, `file2.ext`
**Features Added:** [List of new functionality]
**Status:** [✅ Implementation complete | ❌ Implementation failed | ⏳ Pending approval]

**For TASK FAILED:**
**Task Failed:** [Brief reason why task could not be completed]
**Issue:** [Specific problem encountered]
**Status:** ❌ FAILED
SMART ANALYSIS GUIDELINES:

**Context Detection Keywords:**
- Bug Fix: "fix", "bug", "error", "issue", "broken", "not working", "crash"
- Analysis: "analyze", "review", "check", "investigate", "examine", "understand"
- Code Changes: "implement", "add", "create", "build", "develop", "feature"

**Success/Failure Determination:**
- ✅ SUCCESS: Task objective achieved, tools succeeded, user approved (if needed)
- ❌ FAILED: Task objective not met, tool failures, user rejected, compilation errors
- ⚠️ PARTIAL: Some progress made but objective not fully achieved
- ⏳ PENDING: Waiting for user approval

**Execution Analysis:**
- Check tool execution results in histories
- Look for "code_apply_response" status (success/cancelled)
- Verify actual file modifications vs proposals
- Check compilation results if applicable
- Match outcomes against original user query objective

**Minimal Emoji Usage:**
- ✅ = Success/Complete
- ❌ = Failed/Error
- ⚠️ = Partial/Warning
- ⏳ = Pending

EXAMPLE OUTPUTS:

**Example 1 - Bug Fix Success:**
```
**Root Cause:** Missing bracket in MathUtils.java causing compilation error
**Fixes Applied:**
- Fixed syntax error in `MathUtils.java` line 45
- Added missing closing bracket for multiply method
**Status:** ✅ Fixed
```

**Example 2 - Analysis Task:**
```
**Analysis:** Code review identified potential performance issues in data processing
**Files Analyzed:** `DataProcessor.java`, `Utils.java`
**Findings:** Inefficient loops in data processing, missing null checks
**Status:** ✅ Analysis complete
```

**Example 3 - Code Implementation:**
```
**Code Changes:** Implemented addNumbers method as requested
**Files Modified:** `Calculator.java`
**Features Added:** Addition functionality with input validation
**Status:** ⏳ Pending approval
```

**Example 4 - Task Failed:**
```
**Task Failed:** Unable to locate the specified file for modification
**Issue:** File `NonExistent.java` not found in project structure
**Status:** ❌ FAILED
```

CRITICAL INSTRUCTIONS:
1. **DETERMINE SUMMARY TYPE** - Analyze user query to choose appropriate format
2. **VERIFY ACTUAL RESULTS** - Only report what actually happened, not what was planned
3. **CHECK OBJECTIVE COMPLETION** - Did the task meet its original goal?
4. **BE CONCISE** - Keep under 80 words, focus on key outcomes
5. **USE MINIMAL EMOJIS** - Maximum 3 emojis total

STYLE REQUIREMENTS:
- Use backticks for filenames: `filename.ext`
- Be factual and professional
- Focus on concrete results
- Match the appropriate template format

USER QUERY (ANALYZE FOR CONTEXT):
###USER_QUERY###

EXECUTION HISTORIES (ANALYZE FOR ACTUAL RESULTS):
## MEMORY INFORMATION
###MEMORIES###
<memories>
<memory>
  <content>relevant past experience content</content>
  <timestamp>when this memory was recorded</timestamp>
  <relevance>relevance score</relevance>
</memory>
</memories>

## HISTORY INFORMATION
###HISTORY###

Generate a smart summary using the appropriate format based on the user query context and actual execution results.