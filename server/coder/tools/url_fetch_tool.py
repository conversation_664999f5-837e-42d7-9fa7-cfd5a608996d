import logging
import aiohttp
from utils.history import History
from tools.ws_base_node import WebSocketBaseNode

logger = logging.getLogger(__name__)

class UrlFetchTool(WebSocketBaseNode):
    async def _prep_impl(self, shared):
        history = History().get_latest(shared)
        url = history["params"].get("url")
        return {"url": url}

    async def _exec_impl(self, prep_res):
        url = prep_res.get("url")
        if not url:
            return {"error": "No URL provided"}
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    content = await response.text()
                    return {"url": url, "content": content, "status": response.status}
        except Exception as e:
            logger.error(f"❌ URL FETCH TOOL - Error fetching {url}: {e}")
            return {"error": str(e), "url": url}

    async def _post_impl(self, shared, prep_res, exec_res):
        from websocket.lifecycle_utils import send_tool_status

        history = History().get_latest(shared)
        history["result"] = f"Fetched URL: {prep_res.get('url')}"
        history["data"] = exec_res

        # Send success/error status based on result
        if "error" in exec_res:
            await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "error", f"Failed to fetch URL: {exec_res.get('error')}")
        else:
            await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "success", f"URL fetched successfully")

        # Set flag to prevent base class from sending generic success status
        shared['_tool_status_sent'] = True

        return "default"