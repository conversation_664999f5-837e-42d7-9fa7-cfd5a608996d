
import logging
from utils.history import History
from .ws_base_node import WebSocketBaseNode
from websocket.utils import request_directory_from_client
from websocket.lifecycle_utils import send_tool_status

logger = logging.getLogger(__name__)

class DirTool(WebSocketBaseNode):
    async def _prep_impl(self, shared):
        """Get directory data via WebSocket instead of filesystem"""
        history = History().get_latest(shared)
        folder_name = history["params"]["target_path"]
        if not self.client_id or not self.ws_manager:
            print(f"❌ DIR TOOL - Missing WebSocket connection: client_id={self.client_id}, ws_manager={self.ws_manager}")
            return {"error": "Missing WebSocket connection", "path": folder_name}
        files = await request_directory_from_client(self.ws_manager, self.client_id, ".")
        return {
                "path": folder_name,
                "files": files
            }

       
    
    async def build_directory_tree_from_websocket(self, files_data, indent=""):
        """Build directory tree from WebSocket recursive data - matching original dir_tool.py logic"""
        if "error" in files_data:
            return f"[Error]: {files_data['error']}\n"

        files = files_data["files"]

        # Handle empty directory case
        if not files:
            tree_str = f"{indent}(Empty directory)\n"
            print(f"\n tree_str -- {tree_str}")
            return tree_str

        tree_str = ""

        # Build hierarchical structure
        processed_paths = set()

        for file_info in sorted(files, key=lambda x: x.get("path", "")):
            name = file_info.get('name', '')
            file_type = file_info.get('type', 'file')
            full_path = file_info.get('path', name)

            if full_path in processed_paths:
                continue

            # Calculate depth and indentation
            depth = full_path.count('/')
            current_indent = indent + "    " * depth

            if file_type == "directory":
                tree_str += f"{current_indent}- {name}/\n"
            else:
                tree_str += f"{current_indent}- {name}\n"

            processed_paths.add(full_path)

       
        return tree_str
    
    async def _exec_impl(self, prep_res):
        """Same as your original exec but using WebSocket data"""
       
        tree_str = await self.build_directory_tree_from_websocket(prep_res) 
        return tree_str
    
    
    async def _post_impl(self, shared, prep_res, exec_res):
        """Post processing with status indicator"""
       
        history = History().get_latest(shared)
        history["result"] = f"All the files in the {history['params']['target_path']} has be listed."
        history["data"] = exec_res

        # Send success/error status
        if exec_res is not None and not exec_res.startswith("[Error]"):
            # Empty directory is valid, not an error
            if not exec_res.strip():
                await send_tool_status(self.ws_manager, self.client_id, "dir_tool", "success", "Empty directory analyzed")
            else:
                await send_tool_status(self.ws_manager, self.client_id, "dir_tool", "success", "Project structure analyzed")
        else:
            print(f"📂 DIR TOOL - Sending ERROR status")
            await send_tool_status(self.ws_manager, self.client_id, "dir_tool", "error", "Failed to analyze project structure")
        return "default"
