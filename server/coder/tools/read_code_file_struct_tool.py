import logging
from utils.history import History
from .ws_base_node import WebSocketBaseNode
from websocket.utils import parse_file_paths, request_files_from_client

logger = logging.getLogger(__name__)

class ReadCodeFileStructTool(WebSocketBaseNode):
    async def _prep_impl(self, shared):
        history = History().get_latest(shared)
        files_to_read = history["params"]["target_path"]
        logger.info(f"🏗️ ReadCodeFileStructTool: Processing code structure request for {files_to_read}")
        file_paths = parse_file_paths(files_to_read)
        files_content = await request_files_from_client(self.ws_manager, self.client_id, file_paths)
        # Only keep structure_map for each file
        structure_only = {}
        for file_path, file_data in files_content.items():
            if isinstance(file_data, dict):
                structure_only[file_path] = file_data.get("structure_map", [])
            else:
                structure_only[file_path] = []
        return structure_only

    def _format_history_data(self, exec_res):
        """
        Format the execution result to clearly show only the code structure (structure_map).
        """
        if not isinstance(exec_res, dict):
            return f"CODE STRUCTURE:{exec_res}"
        formatted_output = []
        for file_path, structure_map in exec_res.items():
            formatted_output.append(f"\n📋 STRUCTURE MAP for {file_path}:")
            if structure_map:
                for i, element in enumerate(structure_map, 1):
                    line_num = element.get('line', 'N/A')
                    element_desc = element.get('element', 'N/A')
                    formatted_output.append(f"  {i:2d}. Line {line_num}: {element_desc}")
            else:
                formatted_output.append("  (No structure elements found)")
        return "\n".join(formatted_output)

    async def _exec_impl(self, structure_only):
        return structure_only

    async def _post_impl(self, shared, prep_res, exec_res):
        from websocket.lifecycle_utils import send_tool_status
        history = History().get_latest(shared)
        has_errors = False
        error_files = []
        success_files = []
        if isinstance(exec_res, dict):
            for file_path, structure_map in exec_res.items():
                if isinstance(structure_map, str) and structure_map.startswith("Error:"):
                    has_errors = True
                    error_files.append(file_path)
                    logger.error(f"❌ ReadCodeFileStructTool: Failed to get structure for {file_path}: {structure_map}")
                else:
                    success_files.append(file_path)
                    logger.info(f"✅ : Got structure for {file_path} ({len(structure_map)} elements)")
        if has_errors:
            history["result"] = f"Failed to get code structure: {', '.join(error_files)}"
            history["data"] = exec_res
            for file_path in error_files:
                await send_tool_status(
                    self.ws_manager,
                    self.client_id,
                    self.__class__.__name__,
                    "error",
                    f"Failed to get code structure: {file_path.split('/')[-1]}",
                    file_path=file_path,
                    file_name=file_path.split('/')[-1]
                )
            shared['_tool_status_sent'] = True
            return "default"
        else:
            history["result"] = f"Got code structure for: {history['params']['target_path']}"
            formatted_data = self._format_history_data(exec_res)
            history["data"] = formatted_data
            for file_path in success_files:
                await send_tool_status(
                    self.ws_manager,
                    self.client_id,
                    self.__class__.__name__,
                    "success",
                    f"Got code structure for {file_path.split('/')[-1]}",
                    file_path=file_path,
                    file_name=file_path.split('/')[-1]
                )
            shared['_tool_status_sent'] = True
            logger.info(f"✅ ReadCodeFileStructTool: Successfully got code structure for {len(success_files)} files: {', '.join(success_files)}")
            return "default" 