import os
import re
from .ws_base_node import WebSocketBaseNode
from rich import print
from utils.history import History
from utils.prompt_template import PromptTemplate
from utils.yaml_processor import Yaml, YAMLFixer
from agents.execute.code_edit_agent import CodeEditAgent

class CodeExpertTool(WebSocketBaseNode):
    async def _prep_impl(self, shared):
        current_prompt = shared["coder_prompt"]
        user_query = shared["user_query"]
        print(f"🤖💥 Coder expert  query -- { shared["user_query"]}")
        project_working_folder = shared["working_dir"]
        
        history = History().get_latest(shared)
        
         
        content = PromptTemplate().code_edit_prompt(current_prompt, user_query, project_working_folder)
        return content

    async def _exec_impl(self, prep_res):
        response = CodeEditAgent().generate(prep_res)
        try:
            code_yaml = Yaml().process(response)
        except Exception as e:
            print(":bug: Invalid yaml received in LLM response, trying to fix it.")
            yaml_block_match = re.search(r"```yaml\n(.*?)\n```", response, re.DOTALL)
            if not yaml_block_match:
                raise ValueError("No YAML block found in the text.")
            yaml_content = yaml_block_match.group(1)
            fixer = YAMLFixer()
            fix_result = fixer.fix_yaml(yaml_content)
            if fix_result.success:
                code_yaml = fix_result.data
                print(f"✅ YAML fixed successfully. Fixes applied: {fix_result.fixes_applied}")
            else:
                print(f"❌ Failed to fix YAML: {fix_result.original_error}")
                raise ValueError(f"Failed to parse YAML even after fixing: {fix_result.original_error}")
        return {"code_yaml": code_yaml, "response": response}

    async def _post_impl(self, shared, prep_res, exec_res):
        from websocket.lifecycle_utils import send_tool_status

        history = History().get_latest(shared)
        shared["last_code_applied_flag"] = False
        history["result"] = "The response from code expert has been generated successfully."
        history["data"] = exec_res["code_yaml"]

        # Send success status to client
        await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "success", "Code analysis complete")

        # Set flag to prevent base class from sending generic success status
        shared['_tool_status_sent'] = True

        return "code_applier_tool"
       
        
       
       
        
      
