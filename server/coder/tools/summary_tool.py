import re

from .ws_base_node import WebSocketBaseNode
from utils.prompt_template import PromptTemplate
from utils.history import History
from agents.execute.summary_agent import SummaryAgent
from websocket.manager import websocket_manager
from websocket.utils import send_ws_message

class SummaryTool(WebSocketBaseNode):
    def __init__(self):
        super().__init__()
        self.max_retries = 1

    def analyze_task_context(self, user_query, history_content):
        """Analyze user query and execution history to determine task context and success"""
        query_lower = user_query.lower()
        history_lower = history_content.lower()

        # Determine task type based on user query keywords AND execution history
        task_type = "code_changes"  # default

        # First check user query keywords
        if any(keyword in query_lower for keyword in ["fix", "bug", "error", "issue", "broken", "not working", "crash"]):
            task_type = "bug_fix"
        elif any(keyword in query_lower for keyword in ["analyze", "review", "check", "investigate", "examine", "understand"]):
            task_type = "analysis"
        elif any(keyword in query_lower for keyword in ["implement", "add", "create", "build", "develop", "feature"]):
            task_type = "code_changes"

        # Override based on execution history - if only search/read tools were used, it's analysis
        if ("semantic_search_tool" in history_lower or "file_read_tool" in history_lower) and "code_apply_request" not in history_lower:
            task_type = "analysis"

        # Determine success status based on execution history
        success_status = "failed"  # default to failed unless proven successful

        # Check for successful tool execution and user approval
        if "code_apply_response" in history_lower:
            if '"status": "success"' in history_lower or '"status":"success"' in history_lower:
                success_status = "success"
            elif '"status": "cancelled"' in history_lower or '"status":"cancelled"' in history_lower:
                success_status = "rejected"
            else:
                success_status = "pending"
        elif "applied_count" in history_lower:
            # Check if any files were actually applied
            try:
                applied_section = history_lower.split("applied_count")[1].split()[0]
                if applied_section != "0" and applied_section != '"0"':
                    success_status = "success"
            except:
                pass
        elif "showCodeApproval" in history_content and "code_apply_response" not in history_content:
            success_status = "pending"
        elif task_type == "analysis":
            # For analysis tasks, check if analysis was completed successfully
            if ("analysis" in history_lower or "findings" in history_lower or
                "semantic_search_tool" in history_lower or "file_read_tool" in history_lower or
                "complete" in history_lower or "verified" in history_lower or
                "reviewed" in history_lower or "examined" in history_lower):
                success_status = "success"
        elif "tool execution failed" in history_lower or "compilation error" in history_lower:
            success_status = "failed"
        elif task_type == "analysis" and ("semantic_search_tool" in history_lower or "file_read_tool" in history_lower):
            # If it's an analysis task and tools were executed without failure, consider it successful
            success_status = "success"

        return task_type, success_status

    async def _prep_impl(self, shared):
        user_query = shared["user_query"]
        coder_prompt = shared["coder_prompt"]

        # Extract history content for context analysis
        history_content = self.extract_tag_content(coder_prompt, "histories")

        # Analyze task context and success
        task_type, success_status = self.analyze_task_context(user_query, history_content)

        # Log the analysis for debugging
        print(f"🧠 Smart Summary Analysis: Task Type = {task_type}, Success Status = {success_status}")
       

        # Store context for use in post_impl
        shared["task_context"] = {
            "task_type": task_type,
            "success_status": success_status
        }

        summary_prompt = PromptTemplate().summary_prompt(user_query, coder_prompt)
        return summary_prompt

    def extract_tag_content(self, text, tag):
        """Extract content between XML-like tags"""
        pattern = fr"<{tag}>(.*?)</{tag}>"
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            return ""

    async def _exec_impl(self, prep_res):
        try:
            response = SummaryAgent().generate(prep_res)
            return response
        except Exception as e:
            print(f"❌ Error in SummaryTool exec_impl: {e}")
            return f"Error generating summary: {str(e)}"

    async def _post_impl(self, shared, prep_res, exec_res):
        client_id = shared.get("client_id")
        clean_summary = self.clean_markdown_to_text(exec_res)

        # Get task context for smarter status determination
        task_context = shared.get("task_context", {})
        task_type = task_context.get("task_type", "code_changes")
        success_status = task_context.get("success_status", "failed")

        print(f"📤 Summary Tool Post: Task Type = {task_type}, Success Status = {success_status}")
      

        # Determine overall status for the response
        response_status = "success" if success_status in ["success", "pending"] else "error"

        # Always send summary for analysis tasks, or if task succeeded/pending
        should_send_summary = (success_status != "failed" or "analysis" in task_type.lower())

        print(f"📤 Should send summary: {should_send_summary}")

        if should_send_summary:
            print(f"📤 Sending summary to client {client_id}")
            await send_ws_message(websocket_manager, client_id, "coder_response", {
                "status": response_status,
                "message": clean_summary,
                "summary": clean_summary
            })
        else:
            print(f"❌ Not sending summary - task failed and not analysis type")

        # Send tool status update
        from websocket.lifecycle_utils import send_tool_status
        if response_status == "success":
            await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "success", "Summary generated")
        else:
            await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "error", "Summary generation failed")

        # Set flag to prevent base class from sending generic success status
        shared['_tool_status_sent'] = True

        history = History().get_latest(shared)
        history["result"] = f"Smart summary generated - {task_type} ({success_status})"
        history["data"] = clean_summary
        return "default"

    def clean_markdown_to_text(self, markdown_text):
        try:
            import re
            text = re.sub(r'```[a-zA-Z]*', '', markdown_text)
            text = re.sub(r'```', '', text)
            return text.strip()
        except Exception:
            return markdown_text

       


