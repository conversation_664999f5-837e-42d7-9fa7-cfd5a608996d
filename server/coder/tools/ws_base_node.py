import asyncio
import json
import os
from pocketflow import As<PERSON><PERSON><PERSON>
import traceback
from websocket.context import get_ws_context
from websocket.lifecycle_utils import send_tool_status
from websocket.utils import send_ws_message, parse_file_paths
from services.cognee_service import CogneeService

class WebSocketBaseNode(AsyncNode):
    """Base class for WebSocket-based tools and agents with common utilities and lifecycle hooks"""
    def __init__(self, client_id: str = None, ws_manager=None):
        super().__init__()
        if client_id is None or ws_manager is None:
            ctx = get_ws_context()
            self.client_id = ctx.client_id
            self.ws_manager = ctx.ws_manager
        else:
            self.client_id = client_id
            self.ws_manager = ws_manager
        self.max_retries = 1
        
        # Initialize Cognee service for memory support
        self.cognee_service = CogneeService()

    # parse_file_paths removed; use the utility import instead

    async def prep_async(self, shared):
        await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "running", "Starting...")
        try:
            result = await self._prep_impl(shared)
            return result
        except Exception as e:
            await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "error", str(e))
            raise

    async def exec_async(self, prep_res):
        try:
            result = await self._exec_impl(prep_res)
            return result
        except Exception as e:
            await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "error", str(e))
            raise

    async def post_async(self, shared, prep_res, exec_res):
        try:
            result = await self._post_impl(shared, prep_res, exec_res)

            # Only send success status if tool hasn't already sent its own status
            if not shared.get('_tool_status_sent', False):
                await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "success", "Post-processing complete")

            return result

        except Exception as e:
            await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "error", str(e))
            raise

    async def _prep_impl(self, shared):
        raise NotImplementedError
    async def _exec_impl(self, prep_res):
        raise NotImplementedError
    async def _post_impl(self, shared, prep_res, exec_res):
        raise NotImplementedError
    
    # Cognee Memory Helper Methods
    async def _get_memory_context(self, shared) -> dict:
        """Get relevant memory context for agents (tools can override to return {})"""
        if not hasattr(self, 'cognee_service') or not self.cognee_service.enabled:
            return {}
            
        try:
            client_id = shared.get("client_id", self.client_id)
            query = shared.get("user_query", "")
            
            memory_context = await self.cognee_service.get_compact_memory(
                client_id=client_id,
                query=query,
                context=shared
            )
            print(f"\n\n *********** COGNEE RETRIEVAL :: {memory_context}")
            return memory_context or {}
        except Exception as e:
            
            return {}
    
    async def _store_memory_interaction(self, shared, exec_res):
        """Store interaction in memory (agents can call this in post_async)"""
        if not hasattr(self, 'cognee_service') or not self.cognee_service.enabled:
            return
            
        try:
            client_id = shared.get("client_id", self.client_id)
            interaction_data = {
                "query": shared.get("user_query", ""),
                "response": exec_res,
                "agent_type": self.__class__.__name__,
                "iteration": shared.get("total_iterations", 1),
                "project": shared.get("project_name", "")
            }
            
            await self.cognee_service.store_interaction_memory(client_id, interaction_data)
        except Exception as e:
            
            pass
    

