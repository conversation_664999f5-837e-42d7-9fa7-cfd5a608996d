
import os
import asyncio

from utils.history import History
from .ws_base_node import WebSocketBaseNode
from websocket.utils import send_ws_message, parse_file_paths

class CodeApplierTool(WebSocketBaseNode):
    
    async def prep_async(self, shared):
        print(f"✅ 🛠️ Code Applier in action... ")
        
        # Get client_id and ws_manager from shared context
        self.client_id = shared.get('client_id')
        self.ws_manager = shared.get('ws_manager')
        # Get code changes from code_expert_tool
        return  History().get_latest_specific_tool_data(shared, "code_expert_tool")
      

    async def exec_async(self, code_changes):
        print(f"WebSocket CODE APPLIER TOOL")
        
        if not code_changes:
            print("⚠️ No code changes to apply.")
            return {"status": "no_changes"}

        print(f"🔍 DEBUG: Received code_changes structure: {type(code_changes)}")
        print(f"🔍 DEBUG: Code changes keys: {list(code_changes.keys()) if isinstance(code_changes, dict) else 'Not a dict'}")
        print(f"🔍 DEBUG: Code changes content: {code_changes}")

        if not self.client_id or not self.ws_manager:
            print("❌ Missing WebSocket connection")
            return {"status": "error", "message": "Missing WebSocket connection"}

        # Process and send code operations to client
        # The code_changes is the parsed YAML data directly from code_expert_tool
        operations = code_changes.get("operations", [])

        # If operations is empty, check if they're nested under params
        if not operations and isinstance(code_changes, dict) and "params" in code_changes:
            params = code_changes.get("params", {})
            operations = params.get("operations", [])
            print(f"🔍 DEBUG: Found operations under params: {len(operations)} operations")

        # If operations is still empty, check if code_changes itself is the operations list
        elif not operations and isinstance(code_changes, list):
            operations = code_changes
            print(f"🔍 DEBUG: Using code_changes as operations list: {len(operations)} operations")
        elif not operations and isinstance(code_changes, dict) and "files" in code_changes:
            # Check if it's in the format with "files" key
            operations = code_changes.get("files", [])
            print(f"🔍 DEBUG: Using files from code_changes: {len(operations)} operations")

        if not operations:
            print("⚠️ No operations found in code changes.")
            print(f"🔍 DEBUG: Full code_changes structure for debugging:")
            print(f"🔍 DEBUG: {code_changes}")
            return {"status": "no_operations"}

        # Clean file paths in operations to remove project name prefix
        cleaned_operations = []
        for operation in operations:
            cleaned_operation = operation.copy()

            # Handle file operations
            if "file" in cleaned_operation:
                original_file = cleaned_operation["file"]
                # Use the base class method to clean file paths
                cleaned_files = parse_file_paths(original_file)
                cleaned_operation["file"] = cleaned_files[0] if cleaned_files else original_file
                print(f"🧹 Cleaned operation file path: {original_file} -> {cleaned_operation['file']}")

            

            cleaned_operations.append(cleaned_operation)

        await send_ws_message(self.ws_manager, self.client_id, "code_apply_request", {
            "operations": cleaned_operations
        })
        return "success"

           

    

    async def post_async(self, shared, prep_res, exec_res):
        history = History().get_latest(shared)

        # Check if we're already waiting for feedback (prevent concurrent waits)
        if hasattr(self, '_waiting_for_feedback') and self._waiting_for_feedback:
            print(f"⚠️ Already waiting for feedback - skipping duplicate wait")
            history["result"] = f"Code applied - already waiting for feedback"
            history["data"] = f"Code applied - duplicate wait prevented"
            return "complete"

        # Get timeout from environment
        user_response_timeout = int(os.getenv("RECODE_USER_RESPONSE_TIMEOUT", "60"))
        print(f"⏰ Code Applier: Waiting {user_response_timeout} seconds for immediate LSP feedback from client...")

        # Set flag to prevent concurrent waits
        self._waiting_for_feedback = True

        try:
            # Use asyncio.wait_for to enforce a hard timeout
            immediate_feedback = await asyncio.wait_for(
                self.wait_for_immediate_feedback_via_shared_state(shared, user_response_timeout),
                timeout=user_response_timeout + 10  # Add 10 seconds buffer to prevent hanging
            )
        except asyncio.TimeoutError:
            print(f"⏰ HARD TIMEOUT: Force-stopping wait after {user_response_timeout + 10} seconds")
            immediate_feedback = None
        finally:
            # Always clear the flag
            self._waiting_for_feedback = False
        
       
        shared["code_applied"] =True
        if immediate_feedback:
            print(f"⚡ Received immediate LSP feedback from client!")
            print(f"📊 Compilation issues from client LSP: {immediate_feedback}")

            # Store cleaned feedback string in shared for coder_agent
            shared["immediate_lsp_feedback"] = immediate_feedback
            history["result"] = f"Code applied and there are compilation issues received from client LSP. Need to fix them."
            history["data"] = f"Code applied - LSP analysis: {immediate_feedback}"
            print(f"✅ 🛠️ Code Applier: Returning to coder_agent for fixes")
            return "coder_agent"
        else:
            print(f"⏰ No immediate feedback received within {user_response_timeout} seconds - client will use normal queued feedback")
            history["result"] = f"Successfully sent changes to client (no immediate feedback)"
            history["data"] = f"Code applied - waiting for normal queued feedback flow"
            print(f"✅ 🛠️ Code Applier: Completing flow - no immediate feedback")
            return "complete"

    async def wait_for_immediate_feedback_via_shared_state(self, shared, timeout_seconds: float) -> dict | None:
        """Wait for immediate feedback with optimized polling."""
        feedback_key = f"immediate_feedback_{self.client_id}"
        deadline = asyncio.get_event_loop().time() + timeout_seconds
        check_interval = 0.1  # Check every 100ms for responsiveness
        log_interval = 30.0  # Log every 30 seconds
        last_log_time = asyncio.get_event_loop().time()
        start_time = asyncio.get_event_loop().time()

        print(f"🔍 Awaiting immediate LSP feedback (timeout: {timeout_seconds}s, check every {check_interval}s)")

        try:
            while asyncio.get_event_loop().time() < deadline:
                # Check client connection
                if self.client_id not in self.ws_manager.active_connections:
                    print(f"❌ Client {self.client_id} disconnected while waiting for feedback")
                    return None

                # Check for feedback using modern Python features
                feedback_store = getattr(self.ws_manager, 'immediate_feedback_store', None)
                if feedback_store and (feedback := feedback_store.get(feedback_key)):
                    del feedback_store[feedback_key]
                    elapsed = asyncio.get_event_loop().time() - start_time
                    print(f"⚡ Immediate LSP feedback received after {elapsed:.1f}s")
                    return feedback

                # Log progress periodically
                current_time = asyncio.get_event_loop().time()
                if current_time - last_log_time >= log_interval:
                    remaining = deadline - current_time
                    print(f"⏰ Still waiting for immediate feedback... ({remaining:.0f}s remaining)")
                    last_log_time = current_time

                await asyncio.sleep(check_interval)

            print(f"⏰ TIMEOUT: No immediate LSP feedback received after {timeout_seconds:.1f}s")
            print(f"⏰ Proceeding with normal flow - client will use queued feedback if needed")
            return None

        except asyncio.CancelledError:
            print(f"❌ Immediate feedback wait cancelled for client {self.client_id}")
            raise
        except Exception as e:
            print(f"❌ Error while waiting for immediate feedback: {e}")
            return None


    
    
        
       
