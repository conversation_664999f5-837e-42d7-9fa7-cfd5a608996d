import logging
from utils.history import History
from .ws_base_node import WebSocketBaseNode
from websocket.utils import parse_file_paths, request_files_from_client, request_file_range_from_client

logger = logging.getLogger(__name__)

class FileReadTool(WebSocketBaseNode):


    async def _prep_impl(self, shared):
        history = History().get_latest(shared)
        files_to_read = history["params"]["target_path"]

        # Get line range parameters from params (as specified in coder_prompt.txt)
        line_from = history["params"].get("line_from")
        line_to = history["params"].get("line_to")

        logger.info(f"📖 FileReadTool: Processing file read request for {files_to_read}")
        logger.info(f"📏 FileReadTool: Line range params - from: {line_from}, to: {line_to}")

        file_paths = parse_file_paths(files_to_read)

        # Determine if we should use line ranges or full file content
        use_line_ranges = self._should_use_line_ranges(line_from, line_to)

        if use_line_ranges:
            # Validate and sanitize line ranges
            validated_line_from, validated_line_to = self._validate_line_ranges(line_from, line_to)
            logger.info(f"📏 FileReadTool: Using validated line range {validated_line_from}-{validated_line_to} for files: {file_paths}")

            files_content = await request_file_range_from_client(
                self.ws_manager, self.client_id, file_paths, validated_line_from, validated_line_to
            )
        else:
            logger.info(f"📄 FileReadTool: Reading full content for files: {file_paths}")
            files_content = await request_files_from_client(self.ws_manager, self.client_id, file_paths)

        # Only keep code content (no structure_map)
        code_only = {}
        for file_path, file_data in files_content.items():
            if isinstance(file_data, dict):
                code_only[file_path] = file_data.get("content", "")
            elif isinstance(file_data, str):
                code_only[file_path] = file_data
            else:
                code_only[file_path] = ""
        return code_only

    def _should_use_line_ranges(self, line_from, line_to):
        """
        Determine if line ranges should be used based on the parameters.
        Returns False if ranges are undefined, 0, -1, or invalid.
        """
        # Check for undefined, None, 0, or -1 values
        if line_from is None or line_to is None:
            logger.info("📄 FileReadTool: Line ranges not specified, using full file")
            return False

        if line_from == 0 or line_from == -1 or line_to == 0 or line_to == -1:
            logger.info(f"📄 FileReadTool: Special line range values ({line_from}, {line_to}), using full file")
            return False

        # Check for valid positive integers
        try:
            line_from_int = int(line_from)
            line_to_int = int(line_to)

            if line_from_int <= 0 or line_to_int <= 0:
                logger.info(f"📄 FileReadTool: Invalid line range values ({line_from_int}, {line_to_int}), using full file")
                return False

            if line_from_int > line_to_int:
                logger.info(f"📄 FileReadTool: Invalid range order ({line_from_int} > {line_to_int}), using full file")
                return False

            return True

        except (ValueError, TypeError):
            logger.info(f"📄 FileReadTool: Non-numeric line range values ({line_from}, {line_to}), using full file")
            return False

    def _validate_line_ranges(self, line_from, line_to):
        """
        Validate and sanitize line range values.
        Ensures they are positive integers and in correct order.
        Expands range by ±20 lines for better context.
        """
        try:
            original_from = int(line_from)
            original_to = int(line_to)

            # Expand range by ±20 lines for better context
            expanded_from = max(1, original_from - 20)  # Don't go below line 1
            expanded_to = original_to + 20

            logger.info(f"📏 FileReadTool: Expanded line range from {original_from}-{original_to} to {expanded_from}-{expanded_to} (+40 lines context)")
            return expanded_from, expanded_to

        except (ValueError, TypeError):
            # This shouldn't happen if _should_use_line_ranges passed, but be safe
            logger.warning(f"⚠️ FileReadTool: Failed to validate line ranges, using 1-100 as fallback")
            return 1, 100

    def _format_history_data(self, exec_res):
        """
        Format the execution result to show only code content (no structure_map).
        """
        if not isinstance(exec_res, dict):
            return f"CODE CONTENT:\n{exec_res}"
        formatted_output = []
        for file_path, content in exec_res.items():
            formatted_output.append(f"\n📄 FILE: {file_path}")
            formatted_output.append(content)
        return "\n".join(formatted_output)

    async def _exec_impl(self, code_only):
        return code_only

    async def _post_impl(self, shared, prep_res, exec_res):
        from websocket.lifecycle_utils import send_tool_status

        history = History().get_latest(shared)

        # Get line range info for logging
        line_from = history["params"].get("line_from")
        line_to = history["params"].get("line_to")
        range_info = f" (lines {line_from}-{line_to})" if line_from and line_to else ""

        # Check if any files had errors
        has_errors = False
        error_files = []
        success_files = []

        if isinstance(exec_res, dict):
            for file_path, content in exec_res.items():
                if isinstance(content, str) and content.startswith("Error:"):
                    has_errors = True
                    error_files.append(file_path)
                    logger.error(f"❌ FileReadTool: Failed to read {file_path}{range_info}: {content}")
                else:
                    success_files.append(file_path)
                    content_length = len(content) if isinstance(content, str) else 0
                    logger.info(f"✅ FileReadTool: Successfully read {file_path}{range_info} ({content_length} chars)")

        if has_errors:
            history["result"] = f"Failed to read files{range_info}: {', '.join(error_files)}"
            history["data"] = exec_res
            for file_path in error_files:
                await send_tool_status(
                    self.ws_manager,
                    self.client_id,
                    self.__class__.__name__,
                    "error",
                    f"Failed to read file: {file_path.split('/')[-1]}{range_info}",
                    file_path=file_path,
                    file_name=file_path.split('/')[-1]
                )
            logger.error(f"❌ FileReadTool: Failed to read {len(error_files)} files{range_info}: {', '.join(error_files)}")

            # Directly send error status to client
            shared['_tool_status_sent'] = True

            return "default"  # Next node name for pocketflow
        else:
            history["result"] = f"Read files{range_info}: {history['params']['target_path']}"

            # Format history data to show only code content
            formatted_data = self._format_history_data(exec_res)
            history["data"] = formatted_data

            for file_path in success_files:
                await send_tool_status(
                    self.ws_manager,
                    self.client_id,
                    self.__class__.__name__,
                    "success",
                    f"Reading code from {file_path.split('/')[-1]}{range_info}",
                    file_path=file_path,
                    file_name=file_path.split('/')[-1]
                )
            # Send success status to client with line range information
            logger.info(f"✅ FileReadTool: Successfully read {len(success_files)} files{range_info}: {', '.join(success_files)}")

            # Set flag to prevent base class from sending generic success status
            shared['_tool_status_sent'] = True

            return "default"  # Next node name for pocketflow

     