from tools.ws_base_node import WebSocketBaseNode
from utils.history import History
from utils.kg import Kg

class SemanticSearchTool(WebSocketBaseNode):
    async def _prep_impl(self, shared):
        question = shared["user_query"]
        project_name = shared["project_name"]
        return {"question": question, "project_name": project_name}

    async def _exec_impl(self, shared_map):
        project_name = shared_map["project_name"]
        question = shared_map["question"]
        results = {}
        try:
            enhanced_results = Kg().search_codebase_with_lines(f"{project_name}", question)
            if enhanced_results and any(chunk.get("line_start") for chunk in enhanced_results):
                formatted_results = []
                for chunk in enhanced_results:
                    if chunk.get("line_start") and chunk.get("line_end"):
                        formatted_chunk = f"File: {chunk['file']}\nLines {chunk['line_start']}-{chunk['line_end']} ({chunk.get('language', 'text')}):\n{chunk['content']}\n"
                    else:
                        formatted_chunk = f"File: {chunk['file']}:\n{chunk['content']}\n"
                    formatted_results.append(formatted_chunk)
                results["current_working_code"] = formatted_results
                results["enhanced_search"] = True
                results["line_info_available"] = True
            else:
                modern_results = Kg().search_codebase(f"{project_name}", question)
                results["current_working_code"] = modern_results
                results["enhanced_search"] = False
                results["line_info_available"] = False
        except Exception as e:
            results["current_working_code"] = "No WORKING code found for this project.."
            results["enhanced_search"] = False
            results["line_info_available"] = False
        return results

    async def _post_impl(self, shared, prep_res, exec_res):
        from websocket.lifecycle_utils import send_tool_status

        history = History().get_latest(shared)
        history["result"] = "Semantic search completed."
        history["data"] = exec_res

        # Send success status to client
        await send_tool_status(self.ws_manager, self.client_id, self.__class__.__name__, "success", "Codebase search complete")

        # Set flag to prevent base class from sending generic success status
        shared['_tool_status_sent'] = True

        return "default"
       
