from tools.ws_base_node import WebSocketBaseNode
from utils.history import History
from utils.kg import Kg

class ActionPlanSummaryTool(WebSocketBaseNode):
    async def _prep_impl(self, shared):
        history = History().get_latest(shared)
        list_of_actions = history["params"]["enhanced_tasks"]
        return list_of_actions

    async def _exec_impl(self, prep_res):
        
        return prep_res

    async def _post_impl(self, shared, prep_res, exec_res):
        history = History().get_latest(shared)
        history["result"] = "list of actions planned"
        history["data"] = exec_res
        # Extract only the descriptions from each task dict
        if isinstance(exec_res, list):
            descriptions = [task.get("description", "") for task in exec_res if isinstance(task, dict)]
        else:
            descriptions = []
        shared["actions"] = descriptions
        #print(f"Action Plan Summary Tool -- descriptions: {descriptions}")
        return "default"
       
