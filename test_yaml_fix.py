#!/usr/bin/env python3

import sys
import os
sys.path.append('server/coder')

from utils.yaml_processor import Yaml

def test_yaml_fix():
    # Test the problematic YAML that was causing the error
    problematic_yaml = """```yaml
tool: action_plan_summary_tool
reason: The task is simple and clear: add a method to the greeting file that calculates the product of two numbers. The previous history indicates that the Greeting.java file exists and similar tasks have been executed without additional context. Therefore, the task can be executed as-is without any changes.
params:
  enhanced_tasks:
    - task: 1
      description: "Add a method to the greeting file that calculates the product of two numbers."
```"""

    print("Testing YAML processor with problematic content...")
    print("Original YAML:")
    print(problematic_yaml)
    print("\n" + "="*50 + "\n")
    
    try:
        yaml_processor = Yaml()
        result = yaml_processor.process(problematic_yaml)
        print("✅ SUCCESS! YAML parsed successfully:")
        print(f"Tool: {result.get('tool')}")
        print(f"Reason: {result.get('reason')}")
        print(f"Params: {result.get('params')}")
        return True
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_yaml_fix()
    sys.exit(0 if success else 1)
