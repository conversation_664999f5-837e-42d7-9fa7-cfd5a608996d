# Cognee Memory Integration Documentation

## Overview

This document describes the integration of the Cognee memory framework into the AI coding assistant system, providing agents with intelligent long-term memory capabilities.

## Architecture

### Core Components

1. **CogneeService** (`server/coder/services/cognee_service.py`)
   - Official Cognee SDK integration
   - NetworkX graph analytics
   - Client-specific dataset management
   - GRAPH_COMPLETION search for relationship-aware retrieval

2. **WebSocketBaseNode** (`server/coder/tools/ws_base_node.py`)
   - Base class with memory helper methods
   - `_get_memory_context()` - Retrieve relevant memories
   - `_store_memory_interaction()` - Store agent interactions

3. **PromptTemplate** (`server/coder/utils/prompt_template.py`)
   - Memory injection into `###MEMORIES###` sections
   - XML format matching existing history structure

## Memory Flow

### Storage Process
```
Agent Interaction → CogneeService.store_interaction_memory()
                 ↓
Format for Cognee → _format_memory_content()
                 ↓
Add to Dataset → cognee.add([content], dataset_name)
                 ↓
Build Knowledge Graph → cognee.cognify([dataset_name])
                 ↓
Update NetworkX Graph → _update_local_graph()
```

### Retrieval Process
```
Agent Needs Context → CogneeService.get_compact_memory()
                   ↓
Search with Cognee → cognee.search(SearchType.GRAPH_COMPLETION)
                   ↓
Format Results → _build_memory_content()
                   ↓
Inject into Prompt → PromptTemplate.inject_memory_into_prompt()
```

## Configuration

### Environment Variables
```env
# Enable/disable Cognee memory
COGNEE_ENABLED=false

# Memory limits
COGNEE_MAX_MEMORY_ITEMS=3
COGNEE_MAX_MEMORY_CHARS=500

# LiteLLM endpoint (no API key required)
SLINGSHOT_URL=http://localhost:8000

# Storage location (reuses existing LanceDB)
LANCEDB_LOCATION=./db
```

### Dependencies
```bash
pip install cognee networkx
```

## Memory Organization

### Dataset Structure
- **Client Isolation**: Each client gets their own dataset: `agent_memory_{client_id}`
- **Automatic Classification**: Cognee's knowledge graph handles content types
- **Relationship Mapping**: Graph structure connects related concepts

### Memory Content Format
```xml
<memories>
<memory>
  <content>Agent interaction content from Cognee GRAPH_COMPLETION</content>
  <timestamp>2025-01-24T10:04:56</timestamp>
  <relevance>0.95</relevance>
</memory>
</memories>
```

## Agent Integration

### Execute Agents
- **CoderAgent**: Memory-enhanced code generation
- **CodeEditAgent**: Context-aware code modifications  
- **SummaryAgent**: Intelligent summarization with history

### Plan Agents
- **ExecutionPlanAgent**: Planning with past experience
- **ActionableRequestAnalyzer**: Request analysis with context

### Integration Pattern
```python
# In agent _prep_impl method
memory_context = await self._get_memory_context(shared)
if memory_context:
    prompt = PromptTemplate().inject_memory_into_prompt(prompt, memory_context)

# In agent _post_impl method
await self._store_memory_interaction(shared, exec_res)
```

## Prompt Structure

### Before Memory Integration
```text
## User Query
###USER_QUERY###

## History Information  
###HISTORY###
```

### After Memory Integration
```text
## Memory Information
###MEMORIES###
<memories>...</memories>

## User Query
###USER_QUERY###

## History Information
###HISTORY###
```

## Search Capabilities

### SearchType.GRAPH_COMPLETION
- **Relationship-aware**: Uses full knowledge graph structure
- **Contextual reasoning**: Provides intelligent, connected answers
- **Agent-optimized**: Perfect for tracking interactions and patterns

### NetworkX Analytics
- **Real-time insights**: Client connections, agent usage, project activity
- **Graph metrics**: Centrality, density, relationship analysis
- **Pattern detection**: Most used agents, active projects

## Benefits

1. **Intelligent Memory**: Agents remember context and relationships
2. **Client Isolation**: Separate memory datasets per client
3. **Contextual Search**: Relationship-aware memory retrieval
4. **Minimal Changes**: Existing agent code largely unchanged
5. **Graceful Degradation**: Works with or without Cognee enabled

## Usage Examples

### Enable Memory
```env
COGNEE_ENABLED=true
SLINGSHOT_URL=http://localhost:8000
```

### Check Memory Stats
```python
from services.cognee_service import cognee_service
stats = cognee_service.get_memory_stats(client_id="user123")
```

### Get Graph Insights
```python
insights = cognee_service.get_graph_insights(client_id="user123")
```

## Troubleshooting

### Common Issues
1. **Cognee not installed**: `pip install cognee networkx`
2. **No SLINGSHOT_URL**: Check LiteLLM endpoint configuration
3. **Memory not appearing**: Verify `###MEMORIES###` markers in prompts
4. **Search failures**: Check Cognee SDK logs for errors

### Debug Logging
```python
import logging
logging.getLogger("cognee_service").setLevel(logging.DEBUG)
```

## Future Enhancements

1. **Memory Persistence**: Long-term storage beyond sessions
2. **Cross-client Insights**: Aggregate patterns (privacy-aware)
3. **Memory Pruning**: Automatic cleanup of old/irrelevant memories
4. **Advanced Analytics**: More sophisticated NetworkX analysis
5. **Memory Visualization**: Graph visualization for debugging

## Implementation Status

✅ **Complete**:
- Cognee SDK integration
- NetworkX graph analytics
- Client-specific datasets
- GRAPH_COMPLETION search
- Memory injection in all agents
- Dedicated `###MEMORIES###` sections

🔄 **Ongoing**:
- Performance optimization
- Memory analytics refinement
- Extended prompt coverage
